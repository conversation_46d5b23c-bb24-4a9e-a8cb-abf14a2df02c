# 🗄️ GATE Automation - Database Documentation

## 📋 Overview

GATE Automation uses **H2 Database** as an in-memory database for development and testing. This document provides comprehensive guidance for accessing, managing, and troubleshooting the database.

---

## 🌐 H2 Database Console Access

### Connection Details
- **Console URL**: http://localhost:7777/gqa/h2-console
- **JDBC URL**: `jdbc:h2:mem:testdb`
- **Username**: `sa`
- **Password**: *(leave empty)*
- **Driver Class**: `org.h2.Driver`

### Step-by-Step Access Guide

1. **Start the Application**
   ```bash
   ./gradlew bootRun
   ```

2. **Open H2 Console**
   - Navigate to: http://localhost:7777/gqa/h2-console
   - Verify connection settings match the details above
   - Click "Connect"

3. **Verify Connection**
   - You should see the database schema in the left panel
   - Tables: `USERS`, `BUILD_REQUEST`

---

## 📊 Database Schema

### 👤 USERS Table
```sql
CREATE TABLE USERS (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    USERNAME VARCHAR(50) UNIQUE NOT NULL,
    PASSWORD VARCHAR(255) NOT NULL,
    EMAIL VARCHAR(100) UNIQUE NOT NULL,
    FULL_NAME VARCHAR(100) NOT NULL,
    ROLE VARCHAR(20) NOT NULL,
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Columns Description**:
- `ID`: Primary key, auto-increment
- `USERNAME`: Unique username for login
- `PASSWORD`: BCrypt encrypted password
- `EMAIL`: User's email address
- `FULL_NAME`: Display name
- `ROLE`: User role (ADMIN, EDITOR, QA, ULTIMATE)
- `CREATED_AT`: Account creation timestamp
- `UPDATED_AT`: Last modification timestamp

### 🏗️ BUILD_REQUEST Table
```sql
CREATE TABLE BUILD_REQUEST (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    BUILD_ID VARCHAR(100) UNIQUE NOT NULL,
    USERNAME VARCHAR(50) NOT NULL,
    RELEASE_TYPE VARCHAR(20) NOT NULL,
    BUILD_SETUP TEXT,
    ZOHO_SHOW TEXT,
    CONVERSION TEXT,
    STATUS VARCHAR(20) DEFAULT 'PENDING',
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Additional build configuration fields
    DOMAIN_SETUP TEXT,
    LOCAL_URL TEXT,
    BROWSER VARCHAR(50),
    AUTOMATION_STATUS TEXT,
    REPORT_RECEIVED BOOLEAN DEFAULT FALSE
);
```

**Columns Description**:
- `ID`: Primary key, auto-increment
- `BUILD_ID`: Unique identifier for each build
- `USERNAME`: User who created the build request
- `RELEASE_TYPE`: Type of release (HF, Release, Patch)
- `BUILD_SETUP`: Build setup URL/configuration
- `STATUS`: Current build status (PENDING, IN_PROGRESS, COMPLETED, FAILED)
- Various configuration fields for automation setup

---

## 🔍 Common SQL Queries

### User Management Queries

**View All Users**
```sql
SELECT ID, USERNAME, EMAIL, FULL_NAME, ROLE, CREATED_AT 
FROM USERS 
ORDER BY CREATED_AT DESC;
```

**Find User by Username**
```sql
SELECT * FROM USERS WHERE USERNAME = 'admin';
```

**Count Users by Role**
```sql
SELECT ROLE, COUNT(*) as USER_COUNT 
FROM USERS 
GROUP BY ROLE;
```

**Create New User** (Example)
```sql
INSERT INTO USERS (USERNAME, PASSWORD, EMAIL, FULL_NAME, ROLE) 
VALUES ('testuser', '$2a$10$encrypted_password_hash', '<EMAIL>', 'Test User', 'EDITOR');
```

### Build Request Queries

**View All Build Requests**
```sql
SELECT BUILD_ID, USERNAME, RELEASE_TYPE, STATUS, CREATED_AT 
FROM BUILD_REQUEST 
ORDER BY CREATED_AT DESC;
```

**Find Builds by User**
```sql
SELECT * FROM BUILD_REQUEST 
WHERE USERNAME = 'admin' 
ORDER BY CREATED_AT DESC;
```

**Count Builds by Status**
```sql
SELECT STATUS, COUNT(*) as BUILD_COUNT 
FROM BUILD_REQUEST 
GROUP BY STATUS;
```

**Find Recent Builds (Last 24 hours)**
```sql
SELECT BUILD_ID, USERNAME, RELEASE_TYPE, STATUS, CREATED_AT 
FROM BUILD_REQUEST 
WHERE CREATED_AT > DATEADD('HOUR', -24, CURRENT_TIMESTAMP())
ORDER BY CREATED_AT DESC;
```

**Update Build Status**
```sql
UPDATE BUILD_REQUEST 
SET STATUS = 'COMPLETED', UPDATED_AT = CURRENT_TIMESTAMP() 
WHERE BUILD_ID = 'BUILD-1234567890-123';
```

### Advanced Queries

**User Activity Report**
```sql
SELECT 
    u.USERNAME,
    u.ROLE,
    COUNT(br.ID) as TOTAL_BUILDS,
    MAX(br.CREATED_AT) as LAST_BUILD_DATE
FROM USERS u
LEFT JOIN BUILD_REQUEST br ON u.USERNAME = br.USERNAME
GROUP BY u.USERNAME, u.ROLE
ORDER BY TOTAL_BUILDS DESC;
```

**Build Status Summary**
```sql
SELECT 
    RELEASE_TYPE,
    STATUS,
    COUNT(*) as COUNT,
    AVG(DATEDIFF('MINUTE', CREATED_AT, UPDATED_AT)) as AVG_DURATION_MINUTES
FROM BUILD_REQUEST 
GROUP BY RELEASE_TYPE, STATUS
ORDER BY RELEASE_TYPE, STATUS;
```

---

## 🛠️ Database Maintenance

### Data Cleanup Queries

**Delete Old Build Requests (older than 30 days)**
```sql
DELETE FROM BUILD_REQUEST 
WHERE CREATED_AT < DATEADD('DAY', -30, CURRENT_TIMESTAMP());
```

**Reset User Passwords** (for testing)
```sql
UPDATE USERS 
SET PASSWORD = '$2a$10$default_test_password_hash' 
WHERE USERNAME IN ('admin', 'editor', 'qauser', 'ultimate');
```

### Backup and Restore

**Export Data to CSV**
```sql
-- Export Users
CALL CSVWRITE('users_backup.csv', 'SELECT * FROM USERS');

-- Export Build Requests
CALL CSVWRITE('builds_backup.csv', 'SELECT * FROM BUILD_REQUEST');
```

**Import Data from CSV**
```sql
-- Import Users (after clearing table)
DELETE FROM USERS;
CALL CSVREAD('users_backup.csv', 'USERS');

-- Import Build Requests
DELETE FROM BUILD_REQUEST;
CALL CSVREAD('builds_backup.csv', 'BUILD_REQUEST');
```

---

## 🚨 Troubleshooting

### Common Issues and Solutions

**1. Cannot Connect to H2 Console**
- **Issue**: Console not accessible at http://localhost:7777/gqa/h2-console
- **Solution**: 
  - Verify application is running (`./gradlew bootRun`)
  - Check if port 7777 is available
  - Ensure H2 console is enabled in `application.properties`

**2. Wrong JDBC URL Error**
- **Issue**: "Database not found" error
- **Solution**: 
  - Use exact JDBC URL: `jdbc:h2:mem:testdb`
  - Ensure application is running (in-memory database only exists while app runs)

**3. Authentication Failed**
- **Issue**: Cannot login to H2 console
- **Solution**:
  - Username: `sa`
  - Password: leave empty (no password)
  - Driver: `org.h2.Driver`

**4. Tables Not Found**
- **Issue**: USERS or BUILD_REQUEST tables don't exist
- **Solution**:
  - Restart application to trigger table creation
  - Check `DataInitializer.java` for initialization logic
  - Verify JPA entities are properly configured

**5. Data Not Persisting**
- **Issue**: Data disappears after application restart
- **Solution**:
  - This is expected behavior for in-memory database
  - For persistent storage, modify `application.properties` to use file-based H2

### Configuration for Persistent Storage

To make data persist across application restarts, modify `application.properties`:

```properties
# Change from in-memory to file-based
spring.datasource.url=jdbc:h2:file:./data/testdb
spring.datasource.username=sa
spring.datasource.password=

# Enable H2 console
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA settings
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
```

---

## 📈 Performance Monitoring

### Database Performance Queries

**Check Table Sizes**
```sql
SELECT 
    TABLE_NAME,
    ROW_COUNT_ESTIMATE as ESTIMATED_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'PUBLIC';
```

**Monitor Query Performance**
```sql
-- Enable query statistics
SET QUERY_STATISTICS TRUE;

-- Run your queries, then check statistics
SELECT * FROM INFORMATION_SCHEMA.QUERY_STATISTICS;
```

### Index Optimization

**Create Indexes for Better Performance**
```sql
-- Index on username for faster user lookups
CREATE INDEX IF NOT EXISTS IDX_USERS_USERNAME ON USERS(USERNAME);

-- Index on build_id for faster build lookups
CREATE INDEX IF NOT EXISTS IDX_BUILD_REQUEST_BUILD_ID ON BUILD_REQUEST(BUILD_ID);

-- Index on username and created_at for user activity queries
CREATE INDEX IF NOT EXISTS IDX_BUILD_REQUEST_USER_DATE ON BUILD_REQUEST(USERNAME, CREATED_AT);
```

---

## 🔐 Security Considerations

### Database Security Best Practices

1. **Password Encryption**: All user passwords are stored using BCrypt encryption
2. **SQL Injection Prevention**: Use parameterized queries in application code
3. **Access Control**: H2 console should be disabled in production
4. **Data Validation**: Validate all input data before database operations

### Production Configuration

For production deployment, consider:
- Using PostgreSQL or MySQL instead of H2
- Implementing database connection pooling
- Setting up proper backup and recovery procedures
- Configuring database monitoring and alerting

---

## 📞 Support

For database-related issues:
- Check application logs for detailed error messages
- Verify database connection settings
- Consult Spring Boot H2 documentation
- Contact: <EMAIL>

---

**Database Version**: H2 Database Engine (embedded)  
**Last Updated**: July 25, 2025  
**Documentation Version**: 1.1.0
