const { test, expect } = require('@playwright/test');

class UltimateRoleFullTestcaseFlowVerification {
    constructor(page) {
        this.page = page;
        this.baseURL = 'http://localhost:7777/gqa';
        this.credentials = { username: 'ultimate', password: 'ultimate123' };
    }

    async login() {
        await this.page.goto(`${this.baseURL}/login`);
        await this.page.fill('input[name="username"]', this.credentials.username);
        await this.page.fill('input[name="password"]', this.credentials.password);
        await this.page.click('button[type="submit"]');
        await this.page.waitForNavigation();
    }

    async verifyUltimateAccess() {
        // Verify main page access
        await expect(this.page.locator('text=NIC Checks')).toBeVisible();
        
        // Verify admin panel access
        await this.page.goto(`${this.baseURL}/admin`);
        await expect(this.page.locator('.admin-card')).toBeVisible();
        
        // Verify Ultimate-specific elements
        await expect(this.page.locator('#clearStateSection')).toBeVisible();
        await expect(this.page.locator('text=Clear Current Workflow State')).toBeVisible();
    }

    async testFullNICCheckWorkflow() {
        await this.page.goto(this.baseURL);
        
        // Test release type selection (should start empty)
        const releaseTypeSelect = this.page.locator('#releaseType');
        await expect(releaseTypeSelect).toHaveValue('');
        
        // Complete full workflow
        await releaseTypeSelect.selectOption('HF');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        // Fill build form with Ultimate-specific data
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/ultimate/premium-build');
        await this.page.fill('input[name="zohoshow"]', 'ultimate-premium-show');
        await this.page.fill('input[name="conversion"]', 'ultimate-conversion');
        await this.page.fill('input[name="graphikosi18n"]', 'ultimate-i18n');
        await this.page.fill('input[name="graphikosmedia"]', 'ultimate-media');
        await this.page.fill('input[name="pictures"]', 'ultimate-pictures');
        await this.page.fill('input[name="imageconversion"]', 'ultimate-imageconv');
        await this.page.fill('input[name="shapeframework"]', 'ultimate-shape');
        await this.page.fill('input[name="showlistingdialog"]', 'ultimate-listing');
        await this.page.fill('input[name="showoffline"]', 'ultimate-offline');
        await this.page.fill('input[name="showrenderingframework"]', 'ultimate-rendering');
        await this.page.fill('input[name="showrightpanel"]', 'ultimate-rightpanel');
        await this.page.fill('input[name="showserver"]', 'ultimate-server');
        await this.page.fill('input[name="showslideshowviews"]', 'ultimate-slideshow');
        await this.page.fill('input[name="showui"]', 'ultimate-ui');
        await this.page.fill('input[name="zohoshowinput"]', 'ultimate-showinput');
        
        // Submit build form
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
        
        // Configure automation
        await this.page.fill('input[name="domainSetup"]', 'ultimate-domain');
        await this.page.fill('input[name="localUrl"]', 'ultimate.localzoho.com');
        await this.page.selectOption('select[name="browser"]', 'googlechrome');
        
        // Start automation
        await this.page.click('button:has-text("Start Automation")');
        await this.page.waitForSelector('.report-section', { state: 'visible' });
        
        // Confirm report
        await this.page.check('#reportReceivedCheckbox');
        await this.page.waitForSelector('.manual-sanity-container', { state: 'visible' });
    }

    async testPrechecksFullAccess() {
        // Test full edit access to all prechecks (Ultimate privilege)
        await this.page.fill('#sdBuildDiffContent', 'Ultimate SD Build Diff - Full Access Content');
        await this.page.check('#sdBuildDiff_qa');
        await this.page.check('#sdBuildDiff_server');
        await this.page.check('#sdBuildDiff_client');
        
        await this.page.fill('#changeFileDiffContent', 'Ultimate Change File Diff - Complete Analysis');
        await this.page.check('#changeFileDiff_qa');
        await this.page.check('#changeFileDiff_server');
        await this.page.check('#changeFileDiff_client');
        
        await this.page.fill('#zdcmDiffContent', 'Ultimate ZDCM Diff - Comprehensive Review');
        await this.page.check('#zdcmDiff_qa');
        await this.page.check('#zdcmDiff_server');
        await this.page.check('#zdcmDiff_client');
        
        await this.page.fill('#migrationDiffContent', 'Ultimate Migration Diff - Full Migration Plan');
        await this.page.check('#migrationDiff_qa');
        await this.page.check('#migrationDiff_server');
        await this.page.check('#migrationDiff_client');
        
        // Verify all prechecks are editable for Ultimate
        await expect(this.page.locator('#sdBuildDiffContent')).not.toBeDisabled();
        await expect(this.page.locator('#changeFileDiffContent')).not.toBeDisabled();
        await expect(this.page.locator('#zdcmDiffContent')).not.toBeDisabled();
        await expect(this.page.locator('#migrationDiffContent')).not.toBeDisabled();
        
        // Verify all checkboxes are editable
        await expect(this.page.locator('#sdBuildDiff_qa')).not.toBeDisabled();
        await expect(this.page.locator('#changeFileDiff_server')).not.toBeDisabled();
        await expect(this.page.locator('#zdcmDiff_client')).not.toBeDisabled();
        await expect(this.page.locator('#migrationDiff_qa')).not.toBeDisabled();
    }

    async testStateManagementAndReset() {
        await this.page.goto(`${this.baseURL}/admin`);
        
        // Verify Clear State section is visible to Ultimate
        await expect(this.page.locator('#clearStateSection')).toBeVisible();
        await expect(this.page.locator('text=Clear Current Workflow State')).toBeVisible();
        
        // Test clear state functionality (with confirmation)
        await this.page.click('button:has-text("Clear Current Workflow State")');
        
        // Handle confirmation dialog
        this.page.on('dialog', async dialog => {
            expect(dialog.message()).toContain('Clear Current Workflow State');
            await dialog.accept();
        });
        
        // Verify state clearing success
        await expect(this.page.locator('text=Workflow state cleared successfully')).toBeVisible();
        
        // Navigate back to main page and verify form is reset
        await this.page.goto(this.baseURL);
        await expect(this.page.locator('#releaseType')).toHaveValue('');
    }

    async testAdvancedFeatures() {
        // Test Ultimate-specific advanced features
        await this.page.goto(this.baseURL);
        
        // Test pre-build verification with details
        await this.page.selectOption('#releaseType', 'Release');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        // Fill minimal build form to proceed
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/ultimate/advanced-build');
        await this.page.fill('input[name="zohoshow"]', 'ultimate-advanced-show');
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
        
        // Configure and start automation
        await this.page.fill('input[name="domainSetup"]', 'ultimate-advanced');
        await this.page.selectOption('select[name="browser"]', 'googlechrome');
        await this.page.click('button:has-text("Start Automation")');
        await this.page.waitForSelector('.report-section', { state: 'visible' });
        
        // Test pre-build verification section
        await this.page.check('#reportReceivedCheckbox');
        await this.page.waitForSelector('#preBuildVerificationSection', { state: 'visible' });
        
        await this.page.check('#preSanityCheckbox');
        await this.page.fill('#preSanityDetails', 'Ultimate pre-sanity verification completed');
        
        await this.page.check('#preAutomationCheckbox');
        await this.page.fill('#preAutomationDetails', 'Ultimate pre-automation checks passed');
        
        // Test manual testcase section (refactored)
        await this.page.waitForSelector('#manualTestcaseSection', { state: 'visible' });
        await this.page.check('#manualTestcaseCheckbox');
        await this.page.fill('#creatorNicFormUrl', 'https://forms.ultimate.com/nic-creator-123');
        
        // Test live update with all checkboxes
        await this.page.waitForSelector('#sanityInLiveSection', { state: 'visible' });
        await this.page.check('#sanityInLiveCheckbox');
        await this.page.check('#milestoneCheckbox');
        await this.page.check('#reverseMergeCheckbox');
        await this.page.check('#learnDocCheckbox');
        await this.page.check('#connectPostCheckbox');
    }

    async testUserManagement() {
        await this.page.goto(`${this.baseURL}/admin`);
        
        // Test creating user with Ultimate privileges
        await this.page.fill('input[name="username"]', 'ultimate-test');
        await this.page.fill('input[name="email"]', '<EMAIL>');
        await this.page.fill('input[name="fullName"]', 'Ultimate Test User');
        await this.page.fill('input[name="password"]', 'ultimate123');
        await this.page.selectOption('select[name="role"]', 'ULTIMATE');
        await this.page.click('button[type="submit"]');
        
        // Verify user creation
        await expect(this.page.locator('text=User created successfully')).toBeVisible();
    }

    async testCompleteWorkflow() {
        await this.login();
        await this.verifyUltimateAccess();
        await this.testFullNICCheckWorkflow();
        await this.testPrechecksFullAccess();
        await this.testAdvancedFeatures();
        await this.testStateManagementAndReset();
        await this.testUserManagement();
    }
}

test.describe('Ultimate Role Full Testcase Flow Verification', () => {
    let ultimateTest;

    test.beforeEach(async ({ page }) => {
        ultimateTest = new UltimateRoleFullTestcaseFlowVerification(page);
    });

    test('TC-ULTIMATE-001: Login and full access verification', async ({ page }) => {
        await ultimateTest.login();
        await ultimateTest.verifyUltimateAccess();
    });

    test('TC-ULTIMATE-002: Complete NIC Check workflow', async ({ page }) => {
        await ultimateTest.login();
        await ultimateTest.testFullNICCheckWorkflow();
    });

    test('TC-ULTIMATE-003: Full prechecks management access', async ({ page }) => {
        await ultimateTest.login();
        await ultimateTest.testPrechecksFullAccess();
    });

    test('TC-ULTIMATE-004: State management and reset functionality', async ({ page }) => {
        await ultimateTest.login();
        await ultimateTest.testStateManagementAndReset();
    });

    test('TC-ULTIMATE-005: Advanced features testing', async ({ page }) => {
        await ultimateTest.login();
        await ultimateTest.testAdvancedFeatures();
    });

    test('TC-ULTIMATE-006: User management with Ultimate privileges', async ({ page }) => {
        await ultimateTest.login();
        await ultimateTest.testUserManagement();
    });

    test('TC-ULTIMATE-COMPLETE: Full Ultimate workflow end-to-end', async ({ page }) => {
        await ultimateTest.testCompleteWorkflow();
    });

    test.afterEach(async ({ page }) => {
        // Cleanup: logout
        try {
            await page.goto(`${ultimateTest.baseURL}/logout`);
        } catch (error) {
            console.log('Logout cleanup failed:', error);
        }
    });
});

module.exports = UltimateRoleFullTestcaseFlowVerification;
