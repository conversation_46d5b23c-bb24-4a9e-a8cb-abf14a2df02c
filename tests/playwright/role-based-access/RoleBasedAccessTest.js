const { test, expect } = require('@playwright/test');

class RoleBasedAccessTest {
    constructor(page) {
        this.page = page;
        this.baseUrl = 'http://localhost:8080/gqa';
    }

    async login(username, password) {
        console.log(`🔐 Logging in as ${username}`);
        await this.page.goto(`${this.baseUrl}/login`);
        await this.page.fill('input[name="username"]', username);
        await this.page.fill('input[name="password"]', password);
        await this.page.click('button[type="submit"]');
        await this.page.waitForURL(`${this.baseUrl}/main`);
        console.log(`✅ Successfully logged in as ${username}`);
    }

    async takeScreenshot(name) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        await this.page.screenshot({ 
            path: `test-reports/${timestamp}/screenshots/${name}.png`,
            fullPage: true 
        });
    }

    async testRoleAccess(role, username, password, expectedSections, restrictedSections) {
        console.log(`🧪 Testing ${role} role access restrictions`);
        
        try {
            await this.login(username, password);
            await this.takeScreenshot(`${role}-access-test-login`);

            // Test visible navigation items
            for (const section of expectedSections) {
                const navLink = this.page.locator(`a[data-section="${section}"]`);
                await expect(navLink).toBeVisible();
                console.log(`✅ ${role} can see ${section} navigation`);
            }

            // Test restricted navigation items should not be visible
            for (const section of restrictedSections) {
                const navLink = this.page.locator(`a[data-section="${section}"]`);
                await expect(navLink).not.toBeVisible();
                console.log(`✅ ${role} cannot see ${section} navigation (correctly restricted)`);
            }

            // Test section access by attempting to navigate
            for (const section of expectedSections) {
                console.log(`🔄 Testing ${section} access for ${role}`);
                await this.page.click(`a[data-section="${section}"]`);
                await expect(this.page.locator(`#${section}-section.active`)).toBeVisible();
                await this.takeScreenshot(`${role}-${section}-access-granted`);
                console.log(`✅ ${role} successfully accessed ${section}`);
            }

            // Test JavaScript-level access control for restricted sections
            for (const section of restrictedSections) {
                console.log(`🚫 Testing ${section} restriction for ${role}`);
                
                // Try to call switchToSection directly via JavaScript
                const result = await this.page.evaluate((sectionName) => {
                    try {
                        switchToSection(sectionName);
                        return { success: true };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                }, section);

                // Should show access denied notification
                if (result.success) {
                    // Check if access denied notification appears
                    const notification = this.page.locator('.notification.error:has-text("Access denied")');
                    await expect(notification).toBeVisible({ timeout: 2000 });
                    console.log(`✅ ${role} correctly denied access to ${section}`);
                }
                
                await this.takeScreenshot(`${role}-${section}-access-denied`);
            }

            // Test admin settings icon visibility
            if (role === 'ADMIN' || role === 'ULTIMATE') {
                await expect(this.page.locator('.settings-btn')).toBeVisible();
                console.log(`✅ ${role} can see admin settings icon`);
            } else {
                await expect(this.page.locator('.settings-btn')).not.toBeVisible();
                console.log(`✅ ${role} cannot see admin settings icon (correctly restricted)`);
            }

            console.log(`✅ Role access test passed for ${role}`);
            return { success: true, role };

        } catch (error) {
            console.error(`❌ Role access test failed for ${role}:`, error);
            await this.takeScreenshot(`${role}-access-test-ERROR`);
            return { success: false, role, error: error.message };
        }
    }

    async testDirectURLAccess(role, username, password, restrictedUrls) {
        console.log(`🧪 Testing direct URL access restrictions for ${role}`);
        
        try {
            await this.login(username, password);

            for (const url of restrictedUrls) {
                console.log(`🚫 Testing direct access to ${url} for ${role}`);
                
                const response = await this.page.goto(`${this.baseUrl}${url}`);
                
                if (response.status() === 403) {
                    console.log(`✅ ${role} correctly denied access to ${url} (403 Forbidden)`);
                } else if (response.status() === 404) {
                    console.log(`✅ ${role} correctly denied access to ${url} (404 Not Found)`);
                } else {
                    console.log(`⚠️ ${role} got unexpected response ${response.status()} for ${url}`);
                }
                
                await this.takeScreenshot(`${role}-direct-url-${url.replace(/[^a-zA-Z0-9]/g, '-')}`);
            }

            console.log(`✅ Direct URL access test completed for ${role}`);
            return { success: true, role };

        } catch (error) {
            console.error(`❌ Direct URL access test failed for ${role}:`, error);
            return { success: false, role, error: error.message };
        }
    }

    async testCrossRoleDataAccess() {
        console.log('🧪 Testing cross-role data access restrictions');
        
        try {
            // Test that users can only see their own data or appropriate shared data
            // This would involve creating test data and verifying access restrictions
            
            console.log('✅ Cross-role data access test completed');
            return { success: true };

        } catch (error) {
            console.error('❌ Cross-role data access test failed:', error);
            return { success: false, error: error.message };
        }
    }
}

// Define role access matrix
const ROLE_ACCESS_MATRIX = {
    'ADMIN': {
        allowed: ['nic-checks', 'check-status'],
        restricted: ['update-build', 'dashboard'],
        restrictedUrls: ['/api/update-build', '/api/dashboard']
    },
    'EDITOR': {
        allowed: ['nic-checks', 'check-status'],
        restricted: ['update-build', 'dashboard'],
        restrictedUrls: ['/admin', '/api/update-build', '/api/dashboard']
    },
    'QA': {
        allowed: ['nic-checks', 'check-status'],
        restricted: ['update-build', 'dashboard'],
        restrictedUrls: ['/admin', '/api/update-build', '/api/dashboard']
    },
    'ULTIMATE': {
        allowed: ['nic-checks', 'check-status', 'update-build', 'dashboard'],
        restricted: [],
        restrictedUrls: []
    }
};

const USER_CREDENTIALS = {
    'ADMIN': { username: 'admin', password: 'admin123' },
    'EDITOR': { username: 'editor', password: 'editor123' },
    'QA': { username: 'qauser', password: 'qa123' },
    'ULTIMATE': { username: 'ultimate', password: 'ultimate123' }
};

// Export test functions
test.describe('Role-Based Access Control Tests', () => {
    let accessTest;

    test.beforeEach(async ({ page }) => {
        accessTest = new RoleBasedAccessTest(page);
    });

    // Test each role's access restrictions
    Object.keys(ROLE_ACCESS_MATRIX).forEach(role => {
        test(`${role} role access restrictions`, async () => {
            const { username, password } = USER_CREDENTIALS[role];
            const { allowed, restricted } = ROLE_ACCESS_MATRIX[role];
            
            const result = await accessTest.testRoleAccess(role, username, password, allowed, restricted);
            expect(result.success).toBe(true);
        });

        test(`${role} direct URL access restrictions`, async () => {
            const { username, password } = USER_CREDENTIALS[role];
            const { restrictedUrls } = ROLE_ACCESS_MATRIX[role];
            
            const result = await accessTest.testDirectURLAccess(role, username, password, restrictedUrls);
            expect(result.success).toBe(true);
        });
    });

    test('Cross-role data access restrictions', async () => {
        const result = await accessTest.testCrossRoleDataAccess();
        expect(result.success).toBe(true);
    });
});

module.exports = RoleBasedAccessTest;
