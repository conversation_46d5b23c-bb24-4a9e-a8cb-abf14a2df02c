const { test, expect } = require('@playwright/test');

class NICChecksWorkflowTest {
    constructor(page) {
        this.page = page;
        this.baseUrl = 'http://localhost:8080/gqa';
    }

    async login(username, password) {
        console.log(`🔐 Logging in as ${username}`);
        await this.page.goto(`${this.baseUrl}/login`);
        await this.page.fill('input[name="username"]', username);
        await this.page.fill('input[name="password"]', password);
        await this.page.click('button[type="submit"]');
        await this.page.waitForURL(`${this.baseUrl}/main`);
        console.log(`✅ Successfully logged in as ${username}`);
    }

    async takeScreenshot(name) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        await this.page.screenshot({ 
            path: `test-reports/${timestamp}/screenshots/${name}.png`,
            fullPage: true 
        });
    }

    async testCompleteWorkflowForRole(role, username, password) {
        console.log(`🧪 Testing complete NIC Checks workflow for ${role} role`);
        
        try {
            // Step 1: Login
            await this.login(username, password);
            await this.takeScreenshot(`${role}-01-login-success`);

            // Step 2: Navigate to NIC Checks (should be default)
            await expect(this.page.locator('#nic-checks-section')).toBeVisible();
            await expect(this.page.locator('.workflow-step-indicator')).toBeVisible();
            await this.takeScreenshot(`${role}-02-nic-checks-loaded`);

            // Step 3: Select Release Type
            console.log('📋 Step 1: Selecting release type');
            await this.page.selectOption('#releaseType', 'HF');
            
            // Verify workflow step indicator updates
            await expect(this.page.locator('.step-progress.completed')).toBeVisible();
            await expect(this.page.locator('#buildForm')).toBeVisible();
            await this.takeScreenshot(`${role}-03-release-type-selected`);

            // Step 4: Fill Build Form
            console.log('📋 Step 2: Filling build form');
            await this.page.fill('input[name="buildsetup"]', 'https://github.com/test/gc-build');
            await this.page.fill('input[name="zohoshowinput"]', 'https://github.com/test/zohoshow-build');
            await this.page.fill('input[name="shapeframework"]', 'shape-framework-v1.0');
            await this.page.fill('input[name="graphikosmedia"]', 'graphikos-media-v2.1');
            await this.page.fill('input[name="showrenderingframework"]', 'show-rendering-v3.0');
            await this.page.fill('input[name="showserver"]', 'show-server-v4.0');
            await this.page.fill('input[name="conversion"]', 'show-conversion-v2.0');
            await this.page.fill('input[name="pictures"]', 'show-pictures-v1.5');
            await this.page.fill('input[name="imageconversion"]', 'image-conversion-v1.0');
            await this.takeScreenshot(`${role}-04-build-form-filled`);

            // Step 5: Submit Build Form
            console.log('📋 Step 3: Submitting build form');
            await this.page.click('#buildForm button[type="submit"]');
            
            // Wait for success notification
            await expect(this.page.locator('.notification.success')).toBeVisible({ timeout: 10000 });
            
            // Verify automation section appears
            await expect(this.page.locator('#automationSection')).toBeVisible({ timeout: 5000 });
            await this.takeScreenshot(`${role}-05-automation-section-visible`);

            // Step 6: Configure and Start Automation
            console.log('📋 Step 4: Starting automation');
            await this.page.click('button:has-text("Start Automation")');
            
            // Wait for automation report section
            await expect(this.page.locator('#automationReportSection')).toBeVisible({ timeout: 10000 });
            await expect(this.page.locator('#reportReceivedCheckbox')).not.toBeDisabled();
            await this.takeScreenshot(`${role}-06-automation-started`);

            // Step 7: Confirm Report Received
            console.log('📋 Step 5: Confirming report received');
            await this.page.check('#reportReceivedCheckbox');
            
            // Wait for manual testcase section
            await expect(this.page.locator('#manualTestcaseSection')).toBeVisible({ timeout: 5000 });
            await this.takeScreenshot(`${role}-07-manual-testcase-section`);

            // Step 8: Complete Manual Testcase
            console.log('📋 Step 6: Completing manual testcase');
            await this.page.fill('#manualTestcaseSheet', 'https://docs.google.com/spreadsheets/test-sheet');
            await this.page.check('#manualTestcaseCheckbox');
            await this.takeScreenshot(`${role}-08-workflow-completed`);

            console.log(`✅ Complete workflow test passed for ${role} role`);
            return { success: true, role, steps: 8 };

        } catch (error) {
            console.error(`❌ Workflow test failed for ${role} role:`, error);
            await this.takeScreenshot(`${role}-ERROR-${error.message.replace(/[^a-zA-Z0-9]/g, '-')}`);
            return { success: false, role, error: error.message };
        }
    }

    async testFormValidation() {
        console.log('🧪 Testing form validation');
        
        try {
            await this.login('admin', 'admin123');
            
            // Try to submit without selecting release type
            await this.page.click('#buildForm button[type="submit"]');
            await expect(this.page.locator('.notification.error')).toBeVisible();
            await this.takeScreenshot('form-validation-release-type-required');

            // Select release type but leave required fields empty
            await this.page.selectOption('#releaseType', 'Release');
            await this.page.click('#buildForm button[type="submit"]');
            
            // Should show validation error or handle gracefully
            await this.takeScreenshot('form-validation-empty-fields');

            console.log('✅ Form validation test completed');
            return { success: true };

        } catch (error) {
            console.error('❌ Form validation test failed:', error);
            return { success: false, error: error.message };
        }
    }

    async testSequentialStepProgression() {
        console.log('🧪 Testing sequential step progression');
        
        try {
            await this.login('editor', 'editor123');
            
            // Verify initial state - only step 1 should be visible
            await expect(this.page.locator('.workflow-step-indicator')).toBeVisible();
            await expect(this.page.locator('#buildForm')).not.toBeVisible();
            await expect(this.page.locator('#automationSection')).not.toBeVisible();
            
            // Step progression should be enforced
            await this.page.selectOption('#releaseType', 'HF');
            await expect(this.page.locator('#buildForm')).toBeVisible();
            
            // Automation section should not be visible until form submission
            await expect(this.page.locator('#automationSection')).not.toBeVisible();
            
            console.log('✅ Sequential step progression test passed');
            return { success: true };

        } catch (error) {
            console.error('❌ Sequential step progression test failed:', error);
            return { success: false, error: error.message };
        }
    }
}

// Export test functions
test.describe('NIC Checks Workflow Tests', () => {
    let workflowTest;

    test.beforeEach(async ({ page }) => {
        workflowTest = new NICChecksWorkflowTest(page);
    });

    test('Complete workflow - ADMIN role', async () => {
        const result = await workflowTest.testCompleteWorkflowForRole('ADMIN', 'admin', 'admin123');
        expect(result.success).toBe(true);
    });

    test('Complete workflow - EDITOR role', async () => {
        const result = await workflowTest.testCompleteWorkflowForRole('EDITOR', 'editor', 'editor123');
        expect(result.success).toBe(true);
    });

    test('Complete workflow - QA role', async () => {
        const result = await workflowTest.testCompleteWorkflowForRole('QA', 'qauser', 'qa123');
        expect(result.success).toBe(true);
    });

    test('Complete workflow - ULTIMATE role', async () => {
        const result = await workflowTest.testCompleteWorkflowForRole('ULTIMATE', 'ultimate', 'ultimate123');
        expect(result.success).toBe(true);
    });

    test('Form validation tests', async () => {
        const result = await workflowTest.testFormValidation();
        expect(result.success).toBe(true);
    });

    test('Sequential step progression', async () => {
        const result = await workflowTest.testSequentialStepProgression();
        expect(result.success).toBe(true);
    });
});

module.exports = NICChecksWorkflowTest;
