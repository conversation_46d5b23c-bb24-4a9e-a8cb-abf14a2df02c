// Global setup for Playwright tests
const { chromium } = require('@playwright/test');

async function globalSetup() {
  console.log('🚀 Starting global setup for GATE Automation tests...');
  
  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto('http://localhost:7777/gqa/login', { waitUntil: 'networkidle' });
    
    // Verify all default users exist by attempting login
    const users = [
      { username: 'admin', password: 'admin123' },
      { username: 'editor', password: 'editor123' },
      { username: 'qauser', password: 'qa123' },
      { username: 'ultimate', password: 'ultimate123' }
    ];
    
    for (const user of users) {
      console.log(`✅ Verifying user: ${user.username}`);
      await page.goto('http://localhost:7777/gqa/login');
      await page.fill('input[name="username"]', user.username);
      await page.fill('input[name="password"]', user.password);
      await page.click('button[type="submit"]');
      
      // Verify successful login
      await page.waitForNavigation();
      const currentUrl = page.url();
      if (currentUrl.includes('/login')) {
        throw new Error(`❌ Failed to login with user: ${user.username}`);
      }
      
      // Logout
      await page.goto('http://localhost:7777/gqa/logout');
    }
    
    console.log('✅ All users verified successfully');
    
    // Clear any existing test data
    console.log('🧹 Clearing existing test data...');
    await page.goto('http://localhost:7777/gqa/login');
    await page.fill('input[name="username"]', 'ultimate');
    await page.fill('input[name="password"]', 'ultimate123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
    
    // Clear application state if needed
    await page.goto('http://localhost:7777/gqa/admin');
    const clearButton = page.locator('button:has-text("Clear Current Workflow State")');
    if (await clearButton.isVisible()) {
      await clearButton.click();
      // Handle confirmation dialog
      page.on('dialog', async dialog => {
        await dialog.accept();
      });
    }
    
    console.log('✅ Global setup completed successfully');
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

module.exports = globalSetup;
