const { test, expect } = require('@playwright/test');

class EditorRoleFullTestcaseFlowVerification {
    constructor(page) {
        this.page = page;
        this.baseURL = 'http://localhost:7777/gqa';
        this.credentials = { username: 'editor', password: 'editor123' };
    }

    async login() {
        await this.page.goto(`${this.baseURL}/login`);
        await this.page.fill('input[name="username"]', this.credentials.username);
        await this.page.fill('input[name="password"]', this.credentials.password);
        await this.page.click('button[type="submit"]');
        await this.page.waitForNavigation();
    }

    async verifyEditorAccess() {
        // Verify main page access
        await expect(this.page.locator('text=NIC Checks')).toBeVisible();
        
        // Verify admin panel access denied
        await this.page.goto(`${this.baseURL}/admin`);
        await expect(this.page.locator('text=Access Denied')).toBeVisible();
    }

    async testReleaseTypeSelection() {
        await this.page.goto(this.baseURL);
        
        // Verify release type starts empty (no default HF selection)
        const releaseTypeSelect = this.page.locator('#releaseType');
        await expect(releaseTypeSelect).toHaveValue('');
        
        // Test release type selection
        await releaseTypeSelect.selectOption('Release');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        // Verify build form appears
        await expect(this.page.locator('#buildForm')).toBeVisible();
    }

    async testBuildFormSubmission() {
        await this.page.goto(this.baseURL);
        
        // Select release type
        await this.page.selectOption('#releaseType', 'HF');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        // Fill all required build form fields
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/editor/test-build');
        await this.page.fill('input[name="zohoshow"]', 'editor-test-show');
        await this.page.fill('input[name="conversion"]', 'editor-conversion');
        await this.page.fill('input[name="graphikosi18n"]', 'editor-i18n');
        await this.page.fill('input[name="graphikosmedia"]', 'editor-media');
        await this.page.fill('input[name="pictures"]', 'editor-pictures');
        await this.page.fill('input[name="imageconversion"]', 'editor-imageconv');
        await this.page.fill('input[name="shapeframework"]', 'editor-shape');
        await this.page.fill('input[name="showlistingdialog"]', 'editor-listing');
        await this.page.fill('input[name="showoffline"]', 'editor-offline');
        await this.page.fill('input[name="showrenderingframework"]', 'editor-rendering');
        await this.page.fill('input[name="showrightpanel"]', 'editor-rightpanel');
        await this.page.fill('input[name="showserver"]', 'editor-server');
        await this.page.fill('input[name="showslideshowviews"]', 'editor-slideshow');
        await this.page.fill('input[name="showui"]', 'editor-ui');
        await this.page.fill('input[name="zohoshowinput"]', 'editor-showinput');
        
        // Submit build form
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
        
        // Verify automation section appears
        await expect(this.page.locator('.automation-section')).toBeVisible();
    }

    async testAutomationConfiguration() {
        // Configure automation settings
        await this.page.fill('input[name="domainSetup"]', 'editor-domain');
        await this.page.fill('input[name="localUrl"]', 'editor.localzoho.com');
        await this.page.fill('input[name="buildDetailsAPI"]', 'editor/buildDetails');
        await this.page.fill('input[name="automationStatus"]', 'editor-automation');
        await this.page.fill('input[name="reportSubjectForBot"]', 'Editor Automation Report');
        await this.page.fill('input[name="sendReportInBot"]', 'yes');
        await this.page.fill('input[name="cliqReportBotURL"]', 'https://cliq.zoho.com/editor/bot');
        
        // Select browser
        await this.page.selectOption('select[name="browser"]', 'googlechrome');
        
        // Check extension if needed
        await this.page.check('input[name="addExtension"]');
        
        // Start automation
        await this.page.click('button:has-text("Start Automation")');
        await this.page.waitForSelector('.report-section', { state: 'visible' });
    }

    async testReportConfirmation() {
        // Confirm report received
        await this.page.check('#reportReceivedCheckbox');
        await this.page.waitForSelector('.manual-sanity-container', { state: 'visible' });
        
        // Fill manual sanity report
        await this.page.fill('#manualSanityReport', 'Editor manual sanity testing completed successfully');
        
        // Fill automation report URL
        await this.page.fill('#automationReportUrl', 'https://reports.editor.com/automation-123');
        
        // Fill manual sanity sheet URL
        await this.page.fill('#manualSanitySheetUrl', 'https://sheets.editor.com/sanity-456');
    }

    async testPrechecksReadOnlyAccess() {
        // Verify prechecks are read-only for Editor
        await expect(this.page.locator('#sdBuildDiffContent')).toBeDisabled();
        await expect(this.page.locator('#changeFileDiffContent')).toBeDisabled();
        await expect(this.page.locator('#zdcmDiffContent')).toBeDisabled();
        await expect(this.page.locator('#migrationDiffContent')).toBeDisabled();
        
        // Verify checkboxes are also disabled
        await expect(this.page.locator('#sdBuildDiff_qa')).toBeDisabled();
        await expect(this.page.locator('#changeFileDiff_server')).toBeDisabled();
        await expect(this.page.locator('#zdcmDiff_client')).toBeDisabled();
        await expect(this.page.locator('#migrationDiff_qa')).toBeDisabled();
    }

    async testMultipleSubmissions() {
        // Complete first submission
        await this.testCompleteWorkflow();
        
        // Wait for redirection to Check Status
        await this.page.waitForSelector('.status-container', { state: 'visible' });
        
        // Navigate back to NIC Checks for second submission
        await this.page.click('a[data-section="nic-checks"]');
        await this.page.waitForSelector('#releaseType', { state: 'visible' });
        
        // Verify form is reset (release type should be empty)
        await expect(this.page.locator('#releaseType')).toHaveValue('');
        
        // Submit second NIC Check with different data
        await this.page.selectOption('#releaseType', 'Release');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/editor/second-build');
        await this.page.fill('input[name="zohoshow"]', 'editor-second-show');
        
        // Submit second build
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
    }

    async testCompleteWorkflow() {
        await this.login();
        await this.verifyEditorAccess();
        await this.testReleaseTypeSelection();
        await this.testBuildFormSubmission();
        await this.testAutomationConfiguration();
        await this.testReportConfirmation();
        await this.testPrechecksReadOnlyAccess();
    }
}

test.describe('Editor Role Full Testcase Flow Verification', () => {
    let editorTest;

    test.beforeEach(async ({ page }) => {
        editorTest = new EditorRoleFullTestcaseFlowVerification(page);
    });

    test('TC-EDITOR-001: Login and access verification', async ({ page }) => {
        await editorTest.login();
        await editorTest.verifyEditorAccess();
    });

    test('TC-EDITOR-002: Release type selection (starts empty)', async ({ page }) => {
        await editorTest.login();
        await editorTest.testReleaseTypeSelection();
    });

    test('TC-EDITOR-003: Build form submission', async ({ page }) => {
        await editorTest.login();
        await editorTest.testBuildFormSubmission();
    });

    test('TC-EDITOR-004: Automation configuration', async ({ page }) => {
        await editorTest.login();
        await editorTest.testReleaseTypeSelection();
        await editorTest.testBuildFormSubmission();
        await editorTest.testAutomationConfiguration();
    });

    test('TC-EDITOR-005: Report confirmation workflow', async ({ page }) => {
        await editorTest.login();
        await editorTest.testReleaseTypeSelection();
        await editorTest.testBuildFormSubmission();
        await editorTest.testAutomationConfiguration();
        await editorTest.testReportConfirmation();
    });

    test('TC-EDITOR-006: Prechecks read-only access', async ({ page }) => {
        await editorTest.login();
        await editorTest.testPrechecksReadOnlyAccess();
    });

    test('TC-EDITOR-007: Multiple submissions workflow', async ({ page }) => {
        await editorTest.testMultipleSubmissions();
    });

    test('TC-EDITOR-COMPLETE: Full editor workflow end-to-end', async ({ page }) => {
        await editorTest.testCompleteWorkflow();
    });

    test.afterEach(async ({ page }) => {
        // Cleanup: logout
        try {
            await page.goto(`${editorTest.baseURL}/logout`);
        } catch (error) {
            console.log('Logout cleanup failed:', error);
        }
    });
});

module.exports = EditorRoleFullTestcaseFlowVerification;
