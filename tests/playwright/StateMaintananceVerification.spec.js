const { test, expect } = require('@playwright/test');

class StateMaintananceVerification {
    constructor(page) {
        this.page = page;
        this.baseURL = 'http://localhost:7777/gqa';
        this.users = {
            admin: { username: 'admin', password: 'admin123' },
            editor: { username: 'editor', password: 'editor123' },
            ultimate: { username: 'ultimate', password: 'ultimate123' }
        };
    }

    async loginUser(userType) {
        const credentials = this.users[userType];
        await this.page.goto(`${this.baseURL}/login`);
        await this.page.fill('input[name="username"]', credentials.username);
        await this.page.fill('input[name="password"]', credentials.password);
        await this.page.click('button[type="submit"]');
        await this.page.waitForNavigation();
    }

    async testDataPersistenceAcrossRefresh() {
        await this.loginUser('admin');
        await this.page.goto(this.baseURL);
        
        // Fill form data
        await this.page.selectOption('#releaseType', 'HF');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/persistence/test');
        await this.page.fill('input[name="zohoshow"]', 'persistence-show');
        await this.page.fill('input[name="conversion"]', 'persistence-conversion');
        
        // Wait for auto-save
        await this.page.waitForTimeout(3000);
        
        // Refresh page
        await this.page.reload();
        await this.page.waitForSelector('#releaseType', { state: 'visible' });
        
        // Verify data persists
        await expect(this.page.locator('#releaseType')).toHaveValue('HF');
        await expect(this.page.locator('input[name="buildsetup"]')).toHaveValue('https://github.com/persistence/test');
        await expect(this.page.locator('input[name="zohoshow"]')).toHaveValue('persistence-show');
        await expect(this.page.locator('input[name="conversion"]')).toHaveValue('persistence-conversion');
    }

    async testWorkflowProgressMaintenance() {
        await this.loginUser('editor');
        await this.page.goto(this.baseURL);
        
        // Complete workflow steps
        await this.page.selectOption('#releaseType', 'Release');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/workflow/progress');
        await this.page.fill('input[name="zohoshow"]', 'workflow-show');
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
        
        // Configure automation
        await this.page.fill('input[name="domainSetup"]', 'workflow-domain');
        await this.page.selectOption('select[name="browser"]', 'googlechrome');
        await this.page.click('button:has-text("Start Automation")');
        await this.page.waitForSelector('.report-section', { state: 'visible' });
        
        // Close browser and reopen
        await this.page.close();
        this.page = await this.page.context().newPage();
        
        // Login again and verify workflow progress
        await this.loginUser('editor');
        await this.page.goto(this.baseURL);
        
        // Verify workflow is at correct step
        await expect(this.page.locator('.report-section')).toBeVisible();
        await expect(this.page.locator('input[name="domainSetup"]')).toHaveValue('workflow-domain');
    }

    async testPrechecksDataSynchronization() {
        await this.loginUser('admin');
        await this.page.goto(this.baseURL);
        
        // Fill prechecks data
        await this.page.fill('#sdBuildDiffContent', 'Admin SD Build Diff Content for Sync Test');
        await this.page.check('#sdBuildDiff_qa');
        await this.page.check('#sdBuildDiff_server');
        
        await this.page.fill('#changeFileDiffContent', 'Admin Change File Diff Content for Sync Test');
        await this.page.check('#changeFileDiff_client');
        
        // Wait for auto-save
        await this.page.waitForTimeout(3000);
        
        // Logout and login as different user
        await this.page.goto(`${this.baseURL}/logout`);
        await this.loginUser('ultimate');
        await this.page.goto(this.baseURL);
        
        // Verify prechecks data is synchronized
        await expect(this.page.locator('#sdBuildDiffContent')).toHaveValue('Admin SD Build Diff Content for Sync Test');
        await expect(this.page.locator('#sdBuildDiff_qa')).toBeChecked();
        await expect(this.page.locator('#sdBuildDiff_server')).toBeChecked();
        
        await expect(this.page.locator('#changeFileDiffContent')).toHaveValue('Admin Change File Diff Content for Sync Test');
        await expect(this.page.locator('#changeFileDiff_client')).toBeChecked();
    }

    async testCheckStatusRealTimeUpdates() {
        await this.loginUser('admin');
        await this.page.goto(this.baseURL);
        
        // Complete a workflow to add entry to Check Status
        await this.page.selectOption('#releaseType', 'HF');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/status/realtime');
        await this.page.fill('input[name="zohoshow"]', 'status-show');
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
        
        await this.page.fill('input[name="domainSetup"]', 'status-domain');
        await this.page.selectOption('select[name="browser"]', 'googlechrome');
        await this.page.click('button:has-text("Start Automation")');
        await this.page.waitForSelector('.report-section', { state: 'visible' });
        
        await this.page.check('#reportReceivedCheckbox');
        
        // Navigate to Check Status
        await this.page.click('a[data-section="check-status"]');
        await this.page.waitForSelector('.status-container', { state: 'visible' });
        
        // Verify entry appears in table
        await expect(this.page.locator('#nicChecksTableBody tr')).toHaveCount(1);
        
        // Get build ID from table
        const buildId = await this.page.locator('#nicChecksTableBody tr td:first-child').textContent();
        expect(buildId).toContain('BUILD-');
    }

    async testUltimateStateClearingTargeted() {
        await this.loginUser('ultimate');
        await this.page.goto(this.baseURL);
        
        // Create some workflow data
        await this.page.selectOption('#releaseType', 'Release');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/clear/test');
        await this.page.fill('input[name="zohoshow"]', 'clear-show');
        
        // Complete workflow to create historical data
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
        
        await this.page.fill('input[name="domainSetup"]', 'clear-domain');
        await this.page.selectOption('select[name="browser"]', 'googlechrome');
        await this.page.click('button:has-text("Start Automation")');
        await this.page.waitForSelector('.report-section', { state: 'visible' });
        
        await this.page.check('#reportReceivedCheckbox');
        
        // Navigate to Check Status to verify historical data
        await this.page.click('a[data-section="check-status"]');
        await this.page.waitForSelector('.status-container', { state: 'visible' });
        
        const entriesBeforeClear = await this.page.locator('#nicChecksTableBody tr').count();
        expect(entriesBeforeClear).toBeGreaterThan(0);
        
        // Go to admin page and clear current workflow state
        await this.page.goto(`${this.baseURL}/admin`);
        await this.page.click('button:has-text("Clear Current Workflow State")');
        
        // Handle confirmation dialog
        this.page.on('dialog', async dialog => {
            await dialog.accept();
        });
        
        // Navigate back to main page
        await this.page.goto(this.baseURL);
        
        // Verify current workflow is cleared
        await expect(this.page.locator('#releaseType')).toHaveValue('');
        
        // Navigate to Check Status and verify historical data is preserved
        await this.page.click('a[data-section="check-status"]');
        await this.page.waitForSelector('.status-container', { state: 'visible' });
        
        const entriesAfterClear = await this.page.locator('#nicChecksTableBody tr').count();
        expect(entriesAfterClear).toBe(entriesBeforeClear); // Historical data preserved
    }

    async testMultipleSubmissionsUniqueIds() {
        await this.loginUser('editor');
        await this.page.goto(this.baseURL);
        
        // First submission
        await this.page.selectOption('#releaseType', 'HF');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/multi/first');
        await this.page.fill('input[name="zohoshow"]', 'multi-first-show');
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
        
        await this.page.fill('input[name="domainSetup"]', 'multi-first-domain');
        await this.page.selectOption('select[name="browser"]', 'googlechrome');
        await this.page.click('button:has-text("Start Automation")');
        await this.page.waitForSelector('.report-section', { state: 'visible' });
        
        await this.page.check('#reportReceivedCheckbox');
        
        // Wait for redirection and form reset
        await this.page.waitForSelector('.status-container', { state: 'visible' });
        
        // Navigate back to NIC Checks for second submission
        await this.page.click('a[data-section="nic-checks"]');
        await this.page.waitForSelector('#releaseType', { state: 'visible' });
        
        // Verify form is reset
        await expect(this.page.locator('#releaseType')).toHaveValue('');
        
        // Second submission
        await this.page.selectOption('#releaseType', 'Release');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/multi/second');
        await this.page.fill('input[name="zohoshow"]', 'multi-second-show');
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
        
        await this.page.fill('input[name="domainSetup"]', 'multi-second-domain');
        await this.page.selectOption('select[name="browser"]', 'googlechrome');
        await this.page.click('button:has-text("Start Automation")');
        await this.page.waitForSelector('.report-section', { state: 'visible' });
        
        await this.page.check('#reportReceivedCheckbox');
        
        // Navigate to Check Status
        await this.page.click('a[data-section="check-status"]');
        await this.page.waitForSelector('.status-container', { state: 'visible' });
        
        // Verify both submissions appear with unique IDs
        const tableRows = await this.page.locator('#nicChecksTableBody tr');
        await expect(tableRows).toHaveCount(2);
        
        // Get build IDs
        const firstBuildId = await tableRows.nth(0).locator('td:first-child').textContent();
        const secondBuildId = await tableRows.nth(1).locator('td:first-child').textContent();
        
        expect(firstBuildId).toContain('BUILD-');
        expect(secondBuildId).toContain('BUILD-');
        expect(firstBuildId).not.toBe(secondBuildId); // Unique IDs
    }

    async testFormResetBetweenSubmissions() {
        await this.loginUser('admin');
        await this.page.goto(this.baseURL);
        
        // First submission
        await this.page.selectOption('#releaseType', 'HF');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/reset/first');
        await this.page.fill('input[name="zohoshow"]', 'reset-first-show');
        await this.page.fill('input[name="conversion"]', 'reset-first-conversion');
        
        // Submit and complete workflow
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
        
        await this.page.fill('input[name="domainSetup"]', 'reset-first-domain');
        await this.page.selectOption('select[name="browser"]', 'googlechrome');
        await this.page.click('button:has-text("Start Automation")');
        await this.page.waitForSelector('.report-section', { state: 'visible' });
        
        await this.page.check('#reportReceivedCheckbox');
        
        // Wait for redirection to Check Status
        await this.page.waitForSelector('.status-container', { state: 'visible' });
        
        // Navigate back to NIC Checks
        await this.page.click('a[data-section="nic-checks"]');
        await this.page.waitForSelector('#releaseType', { state: 'visible' });
        
        // Verify complete form reset
        await expect(this.page.locator('#releaseType')).toHaveValue('');
        await expect(this.page.locator('input[name="buildsetup"]')).toHaveValue('');
        await expect(this.page.locator('input[name="zohoshow"]')).toHaveValue('');
        await expect(this.page.locator('input[name="conversion"]')).toHaveValue('');
        await expect(this.page.locator('input[name="domainSetup"]')).toHaveValue('');
        
        // Verify build form is hidden
        await expect(this.page.locator('#buildFormContainer')).not.toBeVisible();
    }
}

test.describe('State Maintenance Verification', () => {
    let stateTest;

    test.beforeEach(async ({ page }) => {
        stateTest = new StateMaintananceVerification(page);
    });

    test('TC-STATE-001: Data persistence across page refreshes', async ({ page }) => {
        await stateTest.testDataPersistenceAcrossRefresh();
    });

    test('TC-STATE-002: Workflow progress maintained across sessions', async ({ page }) => {
        await stateTest.testWorkflowProgressMaintenance();
    });

    test('TC-STATE-003: Prechecks data synchronized across users', async ({ page }) => {
        await stateTest.testPrechecksDataSynchronization();
    });

    test('TC-STATE-004: Check Status table real-time updates', async ({ page }) => {
        await stateTest.testCheckStatusRealTimeUpdates();
    });

    test('TC-STATE-005: Ultimate role targeted state clearing', async ({ page }) => {
        await stateTest.testUltimateStateClearingTargeted();
    });

    test('TC-STATE-006: Multiple submissions with unique IDs', async ({ page }) => {
        await stateTest.testMultipleSubmissionsUniqueIds();
    });

    test('TC-STATE-007: Form reset between submissions', async ({ page }) => {
        await stateTest.testFormResetBetweenSubmissions();
    });

    test.afterEach(async ({ page }) => {
        // Cleanup: logout
        try {
            await page.goto(`${stateTest.baseURL}/logout`);
        } catch (error) {
            console.log('Logout cleanup failed:', error);
        }
    });
});

module.exports = StateMaintananceVerification;
