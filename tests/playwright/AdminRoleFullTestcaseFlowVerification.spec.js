const { test, expect } = require('@playwright/test');

class AdminRoleFullTestcaseFlowVerification {
    constructor(page) {
        this.page = page;
        this.baseURL = 'http://localhost:7777/gqa';
        this.credentials = { username: 'admin', password: 'admin123' };
    }

    async login() {
        await this.page.goto(`${this.baseURL}/login`);
        await this.page.fill('input[name="username"]', this.credentials.username);
        await this.page.fill('input[name="password"]', this.credentials.password);
        await this.page.click('button[type="submit"]');
        await this.page.waitForNavigation();
    }

    async verifyAdminAccess() {
        // Verify admin panel access
        await this.page.goto(`${this.baseURL}/admin`);
        await expect(this.page.locator('.admin-card')).toBeVisible();
        
        // Verify admin-specific elements
        await expect(this.page.locator('text=User Management')).toBeVisible();
        await expect(this.page.locator('text=Create New User')).toBeVisible();
    }

    async testUserManagement() {
        await this.page.goto(`${this.baseURL}/admin`);
        
        // Test create new user
        await this.page.fill('input[name="username"]', 'testuser');
        await this.page.fill('input[name="email"]', '<EMAIL>');
        await this.page.fill('input[name="fullName"]', 'Test User');
        await this.page.fill('input[name="password"]', 'test123');
        await this.page.selectOption('select[name="role"]', 'EDITOR');
        await this.page.click('button[type="submit"]');
        
        // Verify user creation success
        await expect(this.page.locator('text=User created successfully')).toBeVisible();
    }

    async testNICCheckWorkflow() {
        await this.page.goto(this.baseURL);
        
        // Test release type selection (should start empty)
        const releaseTypeSelect = this.page.locator('#releaseType');
        await expect(releaseTypeSelect).toHaveValue('');
        
        // Select release type
        await releaseTypeSelect.selectOption('HF');
        await this.page.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        // Fill build form
        await this.page.fill('input[name="buildsetup"]', 'https://github.com/admin/test-build');
        await this.page.fill('input[name="zohoshow"]', 'admin-test-show');
        await this.page.fill('input[name="conversion"]', 'admin-conversion');
        
        // Submit build form
        await this.page.click('button:has-text("Submit Build")');
        await this.page.waitForSelector('.automation-section', { state: 'visible' });
        
        // Configure automation
        await this.page.fill('input[name="domainSetup"]', 'admin-domain');
        await this.page.fill('input[name="localUrl"]', 'admin.localzoho.com');
        await this.page.selectOption('select[name="browser"]', 'googlechrome');
        
        // Start automation
        await this.page.click('button:has-text("Start Automation")');
        await this.page.waitForSelector('.report-section', { state: 'visible' });
        
        // Confirm report received
        await this.page.check('#reportReceivedCheckbox');
        await this.page.waitForSelector('.manual-sanity-container', { state: 'visible' });
    }

    async testPrechecksManagement() {
        // Test prechecks management (Admin should have edit access)
        await this.page.fill('#sdBuildDiffContent', 'Admin SD Build Diff Content');
        await this.page.check('#sdBuildDiff_qa');
        await this.page.check('#sdBuildDiff_server');
        
        await this.page.fill('#changeFileDiffContent', 'Admin Change File Diff Content');
        await this.page.check('#changeFileDiff_client');
        
        await this.page.fill('#zdcmDiffContent', 'Admin ZDCM Diff Content');
        await this.page.check('#zdcmDiff_qa');
        
        await this.page.fill('#migrationDiffContent', 'Admin Migration Diff Content');
        await this.page.check('#migrationDiff_server');
        
        // Verify prechecks are editable for Admin
        await expect(this.page.locator('#sdBuildDiffContent')).not.toBeDisabled();
        await expect(this.page.locator('#sdBuildDiff_qa')).not.toBeDisabled();
    }

    async testStateManagement() {
        // Admin should NOT be able to clear application state (Ultimate only)
        await this.page.goto(`${this.baseURL}/admin`);
        
        // Clear state section should not be visible to Admin
        const clearStateSection = this.page.locator('#clearStateSection');
        await expect(clearStateSection).not.toBeVisible();
    }

    async testCompleteWorkflow() {
        await this.login();
        await this.verifyAdminAccess();
        await this.testUserManagement();
        await this.testNICCheckWorkflow();
        await this.testPrechecksManagement();
        await this.testStateManagement();
    }
}

test.describe('Admin Role Full Testcase Flow Verification', () => {
    let adminTest;

    test.beforeEach(async ({ page }) => {
        adminTest = new AdminRoleFullTestcaseFlowVerification(page);
    });

    test('TC-ADMIN-001: Login with valid credentials', async ({ page }) => {
        await adminTest.login();
        await expect(page.locator('text=NIC Checks')).toBeVisible();
    });

    test('TC-ADMIN-002: Access admin panel', async ({ page }) => {
        await adminTest.login();
        await adminTest.verifyAdminAccess();
    });

    test('TC-ADMIN-003: User management functionality', async ({ page }) => {
        await adminTest.login();
        await adminTest.testUserManagement();
    });

    test('TC-ADMIN-004: Complete NIC Check workflow', async ({ page }) => {
        await adminTest.login();
        await adminTest.testNICCheckWorkflow();
    });

    test('TC-ADMIN-005: Prechecks management access', async ({ page }) => {
        await adminTest.login();
        await adminTest.testNICCheckWorkflow();
        await adminTest.testPrechecksManagement();
    });

    test('TC-ADMIN-006: State management restrictions', async ({ page }) => {
        await adminTest.login();
        await adminTest.testStateManagement();
    });

    test('TC-ADMIN-COMPLETE: Full admin workflow end-to-end', async ({ page }) => {
        await adminTest.testCompleteWorkflow();
    });

    test.afterEach(async ({ page }) => {
        // Cleanup: logout
        try {
            await page.goto(`${adminTest.baseURL}/logout`);
        } catch (error) {
            console.log('Logout cleanup failed:', error);
        }
    });
});

module.exports = AdminRoleFullTestcaseFlowVerification;
