const { test, expect } = require('@playwright/test');

class CollaborationVerification {
    constructor() {
        this.baseURL = 'http://localhost:7777/gqa';
        this.users = {
            admin: { username: 'admin', password: 'admin123' },
            editor: { username: 'editor', password: 'editor123' },
            qauser: { username: 'qauser', password: 'qa123' },
            ultimate: { username: 'ultimate', password: 'ultimate123' }
        };
    }

    async loginUser(page, userType) {
        const credentials = this.users[userType];
        await page.goto(`${this.baseURL}/login`);
        await page.fill('input[name="username"]', credentials.username);
        await page.fill('input[name="password"]', credentials.password);
        await page.click('button[type="submit"]');
        await page.waitForNavigation();
    }

    async startWorkflowAsAdmin(adminPage) {
        await this.loginUser(adminPage, 'admin');
        await adminPage.goto(this.baseURL);
        
        // Start workflow
        await adminPage.selectOption('#releaseType', 'HF');
        await adminPage.waitForSelector('#buildFormContainer', { state: 'visible' });
        
        // Fill partial build form
        await adminPage.fill('input[name="buildsetup"]', 'https://github.com/collab/admin-start');
        await adminPage.fill('input[name="zohoshow"]', 'admin-collab-show');
        await adminPage.fill('input[name="conversion"]', 'admin-conversion');
        
        // Submit build form
        await adminPage.click('button:has-text("Submit Build")');
        await adminPage.waitForSelector('.automation-section', { state: 'visible' });
        
        // Configure automation partially
        await adminPage.fill('input[name="domainSetup"]', 'admin-collab-domain');
        await adminPage.fill('input[name="localUrl"]', 'admin-collab.localzoho.com');
        
        return 'workflow-started-by-admin';
    }

    async continueWorkflowAsEditor(editorPage) {
        await this.loginUser(editorPage, 'editor');
        await editorPage.goto(this.baseURL);
        
        // Verify workflow state is loaded from admin
        await expect(editorPage.locator('#releaseType')).toHaveValue('HF');
        await expect(editorPage.locator('input[name="buildsetup"]')).toHaveValue('https://github.com/collab/admin-start');
        await expect(editorPage.locator('input[name="domainSetup"]')).toHaveValue('admin-collab-domain');
        
        // Continue automation configuration
        await editorPage.selectOption('select[name="browser"]', 'googlechrome');
        await editorPage.fill('input[name="buildDetailsAPI"]', 'editor/buildDetails');
        
        // Start automation
        await editorPage.click('button:has-text("Start Automation")');
        await editorPage.waitForSelector('.report-section', { state: 'visible' });
        
        // Confirm report
        await editorPage.check('#reportReceivedCheckbox');
        await editorPage.waitForSelector('.manual-sanity-container', { state: 'visible' });
        
        return 'workflow-continued-by-editor';
    }

    async verifyStateAsQA(qaPage) {
        await this.loginUser(qaPage, 'qauser');
        await qaPage.goto(this.baseURL);
        
        // Verify QA can see the collaborative state but with read-only access
        await expect(qaPage.locator('#releaseType')).toHaveValue('HF');
        await expect(qaPage.locator('input[name="buildsetup"]')).toHaveValue('https://github.com/collab/admin-start');
        
        // Verify prechecks are read-only for QA
        await expect(qaPage.locator('#sdBuildDiffContent')).toBeDisabled();
        await expect(qaPage.locator('#changeFileDiffContent')).toBeDisabled();
        await expect(qaPage.locator('#zdcmDiffContent')).toBeDisabled();
        await expect(qaPage.locator('#migrationDiffContent')).toBeDisabled();
        
        return 'state-verified-by-qa';
    }

    async testRealTimeSync(page1, page2) {
        // Login different users on different pages
        await this.loginUser(page1, 'admin');
        await this.loginUser(page2, 'editor');
        
        // Navigate both to main page
        await page1.goto(this.baseURL);
        await page2.goto(this.baseURL);
        
        // Admin makes changes
        await page1.selectOption('#releaseType', 'Release');
        await page1.waitForSelector('#buildFormContainer', { state: 'visible' });
        await page1.fill('input[name="buildsetup"]', 'https://github.com/realtime/sync-test');
        
        // Wait for auto-save (30 seconds interval, but input triggers immediate save)
        await page1.waitForTimeout(3000);
        
        // Refresh editor page and verify changes are synced
        await page2.reload();
        await page2.waitForSelector('#releaseType', { state: 'visible' });
        
        // Verify editor sees admin's changes
        await expect(page2.locator('#releaseType')).toHaveValue('Release');
        await expect(page2.locator('input[name="buildsetup"]')).toHaveValue('https://github.com/realtime/sync-test');
    }

    async testFieldLocking(adminPage, editorPage) {
        // Admin starts workflow
        await this.loginUser(adminPage, 'admin');
        await adminPage.goto(this.baseURL);
        
        await adminPage.selectOption('#releaseType', 'HF');
        await adminPage.waitForSelector('#buildFormContainer', { state: 'visible' });
        await adminPage.fill('input[name="buildsetup"]', 'https://github.com/locking/test');
        await adminPage.click('button:has-text("Submit Build")');
        await adminPage.waitForSelector('.automation-section', { state: 'visible' });
        
        // Editor tries to access - should see locked fields
        await this.loginUser(editorPage, 'editor');
        await editorPage.goto(this.baseURL);
        
        // Verify release type and build form are locked
        await expect(editorPage.locator('#releaseType')).toBeDisabled();
        await expect(editorPage.locator('input[name="buildsetup"]')).toBeDisabled();
        
        // Verify lock indicators are present
        await expect(editorPage.locator('.lock-indicator')).toBeVisible();
    }

    async testMultipleUsersCheckStatus(adminPage, editorPage, qaPage) {
        // Admin completes a workflow
        await this.startWorkflowAsAdmin(adminPage);
        await adminPage.fill('input[name="domainSetup"]', 'multi-user-domain');
        await adminPage.selectOption('select[name="browser"]', 'googlechrome');
        await adminPage.click('button:has-text("Start Automation")');
        await adminPage.waitForSelector('.report-section', { state: 'visible' });
        await adminPage.check('#reportReceivedCheckbox');
        
        // Navigate to Check Status
        await adminPage.click('a[data-section="check-status"]');
        await adminPage.waitForSelector('.status-container', { state: 'visible' });
        
        // Editor checks status
        await this.loginUser(editorPage, 'editor');
        await editorPage.goto(`${this.baseURL}#check-status`);
        await editorPage.waitForSelector('.status-container', { state: 'visible' });
        
        // QA checks status
        await this.loginUser(qaPage, 'qauser');
        await qaPage.goto(`${this.baseURL}#check-status`);
        await qaPage.waitForSelector('.status-container', { state: 'visible' });
        
        // Verify all users see the same data
        const adminTableRows = await adminPage.locator('#nicChecksTableBody tr').count();
        const editorTableRows = await editorPage.locator('#nicChecksTableBody tr').count();
        const qaTableRows = await qaPage.locator('#nicChecksTableBody tr').count();
        
        expect(adminTableRows).toBe(editorTableRows);
        expect(editorTableRows).toBe(qaTableRows);
    }
}

test.describe('Collaboration Verification', () => {
    let collaboration;

    test.beforeEach(async () => {
        collaboration = new CollaborationVerification();
    });

    test('TC-COLLAB-001: Admin starts workflow, Editor continues', async ({ browser }) => {
        const adminContext = await browser.newContext();
        const editorContext = await browser.newContext();
        
        const adminPage = await adminContext.newPage();
        const editorPage = await editorContext.newPage();
        
        try {
            const adminResult = await collaboration.startWorkflowAsAdmin(adminPage);
            expect(adminResult).toBe('workflow-started-by-admin');
            
            const editorResult = await collaboration.continueWorkflowAsEditor(editorPage);
            expect(editorResult).toBe('workflow-continued-by-editor');
        } finally {
            await adminContext.close();
            await editorContext.close();
        }
    });

    test('TC-COLLAB-002: Real-time state synchronization', async ({ browser }) => {
        const context1 = await browser.newContext();
        const context2 = await browser.newContext();
        
        const page1 = await context1.newPage();
        const page2 = await context2.newPage();
        
        try {
            await collaboration.testRealTimeSync(page1, page2);
        } finally {
            await context1.close();
            await context2.close();
        }
    });

    test('TC-COLLAB-003: Field locking prevents conflicts', async ({ browser }) => {
        const adminContext = await browser.newContext();
        const editorContext = await browser.newContext();
        
        const adminPage = await adminContext.newPage();
        const editorPage = await editorContext.newPage();
        
        try {
            await collaboration.testFieldLocking(adminPage, editorPage);
        } finally {
            await adminContext.close();
            await editorContext.close();
        }
    });

    test('TC-COLLAB-004: QA verifies collaborative state (read-only)', async ({ browser }) => {
        const adminContext = await browser.newContext();
        const qaContext = await browser.newContext();
        
        const adminPage = await adminContext.newPage();
        const qaPage = await qaContext.newPage();
        
        try {
            await collaboration.startWorkflowAsAdmin(adminPage);
            const qaResult = await collaboration.verifyStateAsQA(qaPage);
            expect(qaResult).toBe('state-verified-by-qa');
        } finally {
            await adminContext.close();
            await qaContext.close();
        }
    });

    test('TC-COLLAB-005: Multiple users view Check Status updates', async ({ browser }) => {
        const adminContext = await browser.newContext();
        const editorContext = await browser.newContext();
        const qaContext = await browser.newContext();
        
        const adminPage = await adminContext.newPage();
        const editorPage = await editorContext.newPage();
        const qaPage = await qaContext.newPage();
        
        try {
            await collaboration.testMultipleUsersCheckStatus(adminPage, editorPage, qaPage);
        } finally {
            await adminContext.close();
            await editorContext.close();
            await qaContext.close();
        }
    });

    test('TC-COLLAB-006: Cross-device workflow continuation', async ({ browser }) => {
        // Simulate different devices with different viewport sizes
        const desktopContext = await browser.newContext({ viewport: { width: 1920, height: 1080 } });
        const mobileContext = await browser.newContext({ viewport: { width: 375, height: 667 } });
        
        const desktopPage = await desktopContext.newPage();
        const mobilePage = await mobileContext.newPage();
        
        try {
            // Start on desktop
            await collaboration.startWorkflowAsAdmin(desktopPage);
            
            // Continue on mobile
            await collaboration.loginUser(mobilePage, 'admin');
            await mobilePage.goto(collaboration.baseURL);
            
            // Verify state is maintained across devices
            await expect(mobilePage.locator('#releaseType')).toHaveValue('HF');
            await expect(mobilePage.locator('input[name="buildsetup"]')).toHaveValue('https://github.com/collab/admin-start');
            
        } finally {
            await desktopContext.close();
            await mobileContext.close();
        }
    });
});

module.exports = CollaborationVerification;
