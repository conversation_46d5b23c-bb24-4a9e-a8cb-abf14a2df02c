// Global teardown for Playwright tests
const { chromium } = require('@playwright/test');

async function globalTeardown() {
  console.log('🧹 Starting global teardown for GATE Automation tests...');
  
  // Launch browser for cleanup
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Login as Ultimate user for cleanup
    await page.goto('http://localhost:7777/gqa/login');
    await page.fill('input[name="username"]', 'ultimate');
    await page.fill('input[name="password"]', 'ultimate123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
    
    // Clear application state
    console.log('🧹 Clearing application state...');
    await page.goto('http://localhost:7777/gqa/admin');
    
    const clearButton = page.locator('button:has-text("Clear Current Workflow State")');
    if (await clearButton.isVisible()) {
      await clearButton.click();
      
      // Handle confirmation dialog
      page.on('dialog', async dialog => {
        console.log('📋 Accepting clear state confirmation');
        await dialog.accept();
      });
      
      // Wait for clearing to complete
      await page.waitForTimeout(2000);
    }
    
    // Logout
    await page.goto('http://localhost:7777/gqa/logout');
    
    console.log('✅ Global teardown completed successfully');
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  } finally {
    await browser.close();
  }
}

module.exports = globalTeardown;
