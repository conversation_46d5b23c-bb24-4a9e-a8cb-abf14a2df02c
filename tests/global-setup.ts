import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for Graphikos Automation tests...');
  
  // Create directories for test results
  const fs = require('fs');
  const path = require('path');
  
  const dirs = [
    'test-results',
    'allure-results',
    'test-results/videos',
    'test-results/screenshots'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  });
  
  // Wait for the application to be ready
  console.log('⏳ Waiting for application to be ready...');
  
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Try to access the login page to ensure app is running
    await page.goto('http://localhost:8080/gqa/login', { 
      waitUntil: 'networkidle',
      timeout: 60000 
    });
    
    // Verify the login page loads correctly
    await page.waitForSelector('#username', { timeout: 10000 });
    await page.waitForSelector('#password', { timeout: 10000 });
    
    console.log('✅ Application is ready for testing');
    
    // Optionally, you can perform any global setup tasks here
    // such as creating test data, clearing databases, etc.
    
  } catch (error) {
    console.error('❌ Failed to connect to application:', error);
    throw error;
  } finally {
    await browser.close();
  }
  
  console.log('🎯 Global setup completed successfully');
}

export default globalSetup;
