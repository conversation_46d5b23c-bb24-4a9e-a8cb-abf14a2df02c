const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

class ComprehensiveWorkflowTest {
    constructor(page) {
        this.page = page;
        this.baseUrl = 'http://localhost:7777/gqa';
        this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        this.reportDir = `report/Allure-Report/${this.timestamp}`;
    }

    async setupReportDirectory() {
        // Create timestamped report directory
        if (!fs.existsSync(this.reportDir)) {
            fs.mkdirSync(this.reportDir, { recursive: true });
        }
        
        // Create subdirectories
        const subdirs = ['screenshots', 'videos', 'test-data'];
        subdirs.forEach(dir => {
            const fullPath = path.join(this.reportDir, dir);
            if (!fs.existsSync(fullPath)) {
                fs.mkdirSync(fullPath, { recursive: true });
            }
        });
    }

    async login(username, password, role) {
        console.log(`🔐 Logging in as ${username} (${role})`);
        await this.page.goto(`${this.baseUrl}/login`);
        await this.page.fill('input[name="username"]', username);
        await this.page.fill('input[name="password"]', password);
        await this.page.click('button[type="submit"]');
        await this.page.waitForURL(`${this.baseUrl}/main`);
        console.log(`✅ Successfully logged in as ${username}`);
    }

    async takeScreenshot(name, role, iteration) {
        const filename = `${role}-iteration${iteration}-${name}.png`;
        const filepath = path.join(this.reportDir, 'screenshots', filename);
        await this.page.screenshot({ 
            path: filepath,
            fullPage: true 
        });
        console.log(`📸 Screenshot saved: ${filename}`);
    }

    async testCompleteWorkflow(role, username, password, iteration) {
        console.log(`🧪 Testing complete workflow for ${role} - Iteration ${iteration}`);
        
        const testResults = {
            role,
            iteration,
            username,
            startTime: new Date().toISOString(),
            steps: [],
            success: false,
            error: null
        };

        try {
            // Step 1: Login
            await this.login(username, password, role);
            await this.takeScreenshot('01-login-success', role, iteration);
            testResults.steps.push({ step: 1, name: 'Login', status: 'passed', timestamp: new Date().toISOString() });

            // Step 2: Navigate to NIC Checks
            await expect(this.page.locator('#nic-checks-section')).toBeVisible();
            await this.takeScreenshot('02-nic-checks-loaded', role, iteration);
            testResults.steps.push({ step: 2, name: 'NIC Checks Section', status: 'passed', timestamp: new Date().toISOString() });

            // Step 3: Select Release Type
            await this.page.selectOption('#releaseType', 'HF');
            await expect(this.page.locator('#buildForm')).toBeVisible();
            await this.takeScreenshot('03-release-type-selected', role, iteration);
            testResults.steps.push({ step: 3, name: 'Release Type Selection', status: 'passed', timestamp: new Date().toISOString() });

            // Step 4: Fill Build Form
            await this.page.fill('input[name="buildsetup"]', `https://github.com/test/gc-build-${role}-${iteration}`);
            await this.page.fill('input[name="zohoshowinput"]', `https://github.com/test/zoho-${role}-${iteration}`);
            await this.page.fill('input[name="shapeframework"]', 'shape-framework-v1.0');
            await this.page.fill('input[name="graphikosmedia"]', 'graphikos-media-v2.1');
            await this.takeScreenshot('04-build-form-filled', role, iteration);
            testResults.steps.push({ step: 4, name: 'Build Form Fill', status: 'passed', timestamp: new Date().toISOString() });

            // Step 5: Submit Build Form
            await this.page.click('#buildForm button[type="submit"]');
            await expect(this.page.locator('.notification.success')).toBeVisible({ timeout: 10000 });
            await expect(this.page.locator('#automationSection')).toBeVisible({ timeout: 5000 });
            await this.takeScreenshot('05-build-submitted', role, iteration);
            testResults.steps.push({ step: 5, name: 'Build Submission', status: 'passed', timestamp: new Date().toISOString() });

            // Step 6: Start Automation
            await this.page.click('button:has-text("Start Automation")');
            await expect(this.page.locator('#automationReportSection')).toBeVisible({ timeout: 10000 });
            await this.takeScreenshot('06-automation-started', role, iteration);
            testResults.steps.push({ step: 6, name: 'Start Automation', status: 'passed', timestamp: new Date().toISOString() });

            // Step 7: Confirm Report Received
            await this.page.check('#reportReceivedCheckbox');
            await expect(this.page.locator('.notification.success')).toBeVisible({ timeout: 5000 });
            await this.takeScreenshot('07-report-confirmed', role, iteration);
            testResults.steps.push({ step: 7, name: 'Report Confirmation', status: 'passed', timestamp: new Date().toISOString() });

            // Step 8: Wait for Pre-Build Update Section
            await expect(this.page.locator('#preBuildUpdateSection')).toBeVisible({ timeout: 5000 });
            await this.takeScreenshot('08-prebuild-section-visible', role, iteration);
            testResults.steps.push({ step: 8, name: 'Pre-Build Section Display', status: 'passed', timestamp: new Date().toISOString() });

            // Step 9: Fill Manual Testcase
            await this.page.fill('#manualTestcaseSheet', `https://docs.google.com/spreadsheets/test-${role}-${iteration}`);
            await this.page.check('#manualTestcaseCheckbox');
            await this.takeScreenshot('09-manual-testcase-completed', role, iteration);
            testResults.steps.push({ step: 9, name: 'Manual Testcase', status: 'passed', timestamp: new Date().toISOString() });

            // Step 10: Complete Pre-Checks (QA, Server, Client)
            await this.page.check('#qaPreCheckbox');
            await this.page.check('#serverPreCheckbox');
            await this.page.check('#clientPreCheckbox');
            await this.takeScreenshot('10-prechecks-completed', role, iteration);
            testResults.steps.push({ step: 10, name: 'Pre-Checks Completion', status: 'passed', timestamp: new Date().toISOString() });

            // Step 11: Update Build in Pre
            await this.page.click('#preBuildUpdateSection button');
            await expect(this.page.locator('.notification.success')).toBeVisible({ timeout: 5000 });
            await expect(this.page.locator('#preBuildVerificationSection')).toBeVisible({ timeout: 5000 });
            await this.takeScreenshot('11-prebuild-updated', role, iteration);
            testResults.steps.push({ step: 11, name: 'Pre-Build Update', status: 'passed', timestamp: new Date().toISOString() });

            // Step 12: Complete Pre-Build Verification
            await this.page.check('#preSanityCheckbox');
            await this.page.check('#preAutomationCheckbox');
            await expect(this.page.locator('#liveBuildUpdateSection')).toBeVisible({ timeout: 5000 });
            await this.takeScreenshot('12-prebuild-verification-completed', role, iteration);
            testResults.steps.push({ step: 12, name: 'Pre-Build Verification', status: 'passed', timestamp: new Date().toISOString() });

            // Step 13: Update to Live
            await this.page.click('#updateToLiveBtn');
            await expect(this.page.locator('.notification.success')).toBeVisible({ timeout: 5000 });
            await expect(this.page.locator('#sanityInLiveSection')).toBeVisible({ timeout: 5000 });
            await this.takeScreenshot('13-live-updated', role, iteration);
            testResults.steps.push({ step: 13, name: 'Live Update', status: 'passed', timestamp: new Date().toISOString() });

            // Step 14: Complete Sanity in Live
            await this.page.check('#sanityInLiveCheckbox');
            await expect(this.page.locator('#completeReleaseSection')).toBeVisible({ timeout: 5000 });
            await this.takeScreenshot('14-sanity-live-completed', role, iteration);
            testResults.steps.push({ step: 14, name: 'Sanity in Live', status: 'passed', timestamp: new Date().toISOString() });

            // Step 15: Complete Release
            await this.page.click('#completeReleaseBtn');
            await expect(this.page.locator('.notification.success')).toBeVisible({ timeout: 5000 });
            await expect(this.page.locator('#check-status-section.active')).toBeVisible({ timeout: 5000 });
            await this.takeScreenshot('15-release-completed', role, iteration);
            testResults.steps.push({ step: 15, name: 'Release Completion', status: 'passed', timestamp: new Date().toISOString() });

            // Step 16: Verify Check Status Page
            await expect(this.page.locator('#nicChecksTable')).toBeVisible();
            await this.takeScreenshot('16-check-status-page', role, iteration);
            testResults.steps.push({ step: 16, name: 'Check Status Verification', status: 'passed', timestamp: new Date().toISOString() });

            testResults.success = true;
            testResults.endTime = new Date().toISOString();
            
            console.log(`✅ Complete workflow test passed for ${role} - Iteration ${iteration}`);
            return testResults;

        } catch (error) {
            console.error(`❌ Workflow test failed for ${role} - Iteration ${iteration}:`, error);
            await this.takeScreenshot(`ERROR-${error.message.replace(/[^a-zA-Z0-9]/g, '-')}`, role, iteration);
            
            testResults.success = false;
            testResults.error = error.message;
            testResults.endTime = new Date().toISOString();
            
            return testResults;
        }
    }

    async saveTestResults(allResults) {
        const reportData = {
            testSuite: 'Comprehensive NIC Checks Workflow',
            timestamp: this.timestamp,
            totalTests: allResults.length,
            passedTests: allResults.filter(r => r.success).length,
            failedTests: allResults.filter(r => !r.success).length,
            results: allResults
        };

        const reportPath = path.join(this.reportDir, 'test-results.json');
        fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));

        // Generate HTML report
        await this.generateHTMLReport(reportData);
        
        console.log(`📊 Test results saved to: ${reportPath}`);
        return reportData;
    }

    async generateHTMLReport(reportData) {
        const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GATE Workflow Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .test-result { margin-bottom: 20px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .test-header { background: #f8f9fa; padding: 15px; font-weight: bold; }
        .test-header.passed { background: #d4edda; color: #155724; }
        .test-header.failed { background: #f8d7da; color: #721c24; }
        .test-steps { padding: 15px; }
        .step { display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px solid #eee; }
        .step:last-child { border-bottom: none; }
        .step.passed { color: #28a745; }
        .step.failed { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GATE Application - Comprehensive Workflow Test Report</h1>
            <p>Generated on: ${new Date(reportData.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>Total Tests</h3>
                <h2>${reportData.totalTests}</h2>
            </div>
            <div class="summary-card">
                <h3>Passed</h3>
                <h2>${reportData.passedTests}</h2>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <h2>${reportData.failedTests}</h2>
            </div>
            <div class="summary-card">
                <h3>Success Rate</h3>
                <h2>${Math.round((reportData.passedTests / reportData.totalTests) * 100)}%</h2>
            </div>
        </div>
        
        <div class="test-results">
            ${reportData.results.map(result => `
                <div class="test-result">
                    <div class="test-header ${result.success ? 'passed' : 'failed'}">
                        ${result.role} - Iteration ${result.iteration} - ${result.success ? '✅ PASSED' : '❌ FAILED'}
                        ${result.error ? `<br><small>Error: ${result.error}</small>` : ''}
                    </div>
                    <div class="test-steps">
                        ${result.steps.map(step => `
                            <div class="step ${step.status}">
                                <span>Step ${step.step}: ${step.name}</span>
                                <span>${step.status === 'passed' ? '✅' : '❌'}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>`;

        const htmlPath = path.join(this.reportDir, 'report.html');
        fs.writeFileSync(htmlPath, htmlContent);
        console.log(`📄 HTML report generated: ${htmlPath}`);
    }
}

// Test credentials
const testUsers = [
    { role: 'ADMIN', username: 'admin', password: 'admin123' },
    { role: 'EDITOR', username: 'editor', password: 'editor123' },
    { role: 'QA', username: 'qauser', password: 'qa123' }
];

// Export test functions
test.describe('Comprehensive NIC Checks Workflow Tests', () => {
    let workflowTest;
    let allResults = [];

    test.beforeAll(async ({ page }) => {
        workflowTest = new ComprehensiveWorkflowTest(page);
        await workflowTest.setupReportDirectory();
    });

    // Run 3 iterations for each role
    testUsers.forEach(user => {
        for (let iteration = 1; iteration <= 3; iteration++) {
            test(`${user.role} - Complete Workflow - Iteration ${iteration}`, async ({ page }) => {
                workflowTest.page = page;
                const result = await workflowTest.testCompleteWorkflow(user.role, user.username, user.password, iteration);
                allResults.push(result);
                expect(result.success).toBe(true);
            });
        }
    });

    test.afterAll(async () => {
        if (workflowTest && allResults.length > 0) {
            await workflowTest.saveTestResults(allResults);
            console.log(`🎉 All tests completed! Report available at: ${workflowTest.reportDir}/report.html`);
        }
    });
});

module.exports = ComprehensiveWorkflowTest;
