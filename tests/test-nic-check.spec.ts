import { test, expect } from '@playwright/test';

test.describe('NIC Check Complete Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('http://localhost:8080/gqa/login');
    
    // Login as admin user
    await page.fill('#username', 'admin');
    await page.fill('#password', 'admin123');
    await page.click('button[type="submit"]');
    
    // Wait for navigation to main page
    await page.waitForURL('**/main');
    
    // Ensure NIC Checks section is active
    await page.click('a[data-section="nic-checks"]');
    await page.waitForTimeout(1000);
  });

  test('Complete NIC Check form workflow', async ({ page }) => {
    // Step 1: Select Release Type
    await page.selectOption('#releaseType', 'HF');
    await expect(page.locator('#buildForm')).toBeVisible();
    
    // Step 2: Fill Build Form
    await page.fill('input[name="buildsetup"]', 'https://github.com/test/gc-build-hf-123');
    await page.fill('input[name="zohoshowinput"]', 'https://github.com/test/zohoshow-build-hf-123');
    
    // Fill client inputs
    await page.fill('input[name="shapeframework"]', 'shape-framework-v2.1.0');
    await page.fill('input[name="graphikosmedia"]', 'graphikos-media-v3.2.1');
    await page.fill('input[name="showrenderingframework"]', 'show-rendering-v4.0.0');
    await page.fill('input[name="graphikosi18n"]', 'graphikos-i18n-v2.5.0');
    await page.fill('input[name="showlistingdialog"]', 'show-listing-dialog-v3.1.0');
    await page.fill('input[name="showrightpanel"]', 'show-right-panel-v2.8.0');
    await page.fill('input[name="showslideshowviews"]', 'show-slideshow-views-v3.5.0');
    await page.fill('input[name="showui"]', 'show-ui-v5.0.0');
    await page.fill('input[name="showoffline"]', 'show-offline-v2.2.0');
    
    // Fill server configuration
    await page.fill('input[name="showserver"]', 'show-server-v4.1.0');
    await page.check('input[name="enableAutobuildUpdate"]');
    
    // Fill other configurations
    await page.fill('input[name="conversion"]', 'show-conversion-v3.0.0');
    await page.fill('input[name="pictures"]', 'show-pictures-v2.5.0');
    await page.fill('input[name="imageconversion"]', 'image-conversion-v2.0.0');
    
    // Step 3: Submit Build Form
    await page.click('#buildForm button[type="submit"]');
    
    // Wait for automation section to appear
    await expect(page.locator('#automationSection')).toBeVisible();
    
    // Step 4: Fill Automation Configuration
    await page.fill('input[name="domainSetup"]', 'gcautomation-test');
    await page.fill('input[name="localUrl"]', 'gcautomation-test.localzoho.com');
    await page.fill('input[name="localServerMachineName"]', 'test-server-01');
    await page.fill('input[name="tomcatPath"]', '/opt/tomcat/test');
    await page.selectOption('select[name="browser"]', 'firefox');
    await page.uncheck('input[name="addExtension"]');
    await page.check('input[name="gridEnabled"]');
    
    // Step 5: Start Automation
    await page.click('button:has-text("Start Automation")');
    
    // Wait for automation report section
    await expect(page.locator('#automationReportSection')).toBeVisible();
    
    // Step 6: Confirm Report Received
    await page.check('#reportReceivedCheckbox');
    
    // Step 7: Fill Manual Testcase
    await page.fill('#manualTestcaseSheet', 'https://docs.google.com/spreadsheets/test-manual-cases');
    await page.check('#manualTestcaseCheckbox');
    
    // Step 8: Update Build in Pre
    await expect(page.locator('#preBuildUpdateSection')).toBeVisible();
    await page.click('button:has-text("Update Build in Pre")');
    
    // Step 9: Pre-Build Verification
    await expect(page.locator('#preCheckboxesSection')).toBeVisible();
    await page.check('#preSanityCheckbox');
    await page.check('#preAutomationCheckbox');
    
    // Step 10: Admin Prechecks (Admin only)
    if (await page.locator('#prechecksPanelAdmin').isVisible()) {
      await page.fill('#buildDiffContent', 'Test build diff content for HF-123');
      await page.check('#buildDiffChecked');
      
      await page.fill('#changedFilesContent', 'Modified files: src/main/java/TestClass.java');
      await page.check('#changedFilesChecked');
      
      await page.fill('#zdcmFilesContent', 'ZDCM files: config/zdcm-test.xml');
      await page.check('#zdcmFilesChecked');
      
      await page.fill('#migrationFilesContent', 'Migration files: db/migration/V1_2_3__test.sql');
      await page.check('#migrationFilesChecked');
    }
    
    // Step 11: Update Build to Live
    await expect(page.locator('#liveBuildUpdateSection')).toBeVisible();
    await page.click('#updateToLiveBtn');
    
    // Step 12: Final Sanity
    await expect(page.locator('#finalSanitySection')).toBeVisible();
    await page.fill('#sanityUrl', 'https://test-sanity.zoho.com/show/sanity-hf-123');
    await page.check('#finalSanityCheckbox');
    
    // Step 13: Complete Build
    await expect(page.locator('#completeButton')).toBeVisible();
    await page.click('#completeButton');
    
    // Step 14: Verify in Check Status
    await page.click('a[data-section="check-status"]');
    await page.waitForTimeout(1000);
    
    // Verify NIC Checks table shows the completed form
    await expect(page.locator('#nicChecksTable')).toBeVisible();
    
    // Check if the table contains our test data
    const tableContent = await page.locator('#nicChecksTable tbody').textContent();
    expect(tableContent).toContain('HF'); // Release type should be visible
    
    // Take screenshot for verification
    await page.screenshot({ path: 'test-results/nic-check-completed.png', fullPage: true });
  });

  test('Verify automation configuration form fields', async ({ page }) => {
    // Select release type to show form
    await page.selectOption('#releaseType', 'Release');
    await page.fill('input[name="buildsetup"]', 'test-build');
    await page.click('#buildForm button[type="submit"]');
    
    // Wait for automation section
    await expect(page.locator('#automationSection')).toBeVisible();
    
    // Verify all automation config fields are present
    await expect(page.locator('input[name="domainSetup"]')).toHaveValue('gcautomation');
    await expect(page.locator('input[name="localUrl"]')).toHaveValue('gcautomation.localzoho.com');
    await expect(page.locator('input[name="buildDetailsAPI"]')).toHaveValue('show/buildDetails');
    await expect(page.locator('input[name="automationStatus"]')).toHaveValue('automation');
    await expect(page.locator('input[name="reportSubjectForBot"]')).toHaveValue('Full Automation');
    await expect(page.locator('input[name="sendReportInBot"]')).toHaveValue('yes');
    
    // Verify browser dropdown options
    const browserOptions = await page.locator('select[name="browser"] option').allTextContents();
    expect(browserOptions).toContain('Google Chrome');
    expect(browserOptions).toContain('Firefox');
    expect(browserOptions).toContain('Edge');
    
    // Verify checkboxes
    await expect(page.locator('input[name="addExtension"]')).toBeChecked();
    await expect(page.locator('input[name="enableConsoleLogs"]')).toBeChecked();
    await expect(page.locator('input[name="enableBiDiLogging"]')).toBeChecked();
    await expect(page.locator('input[name="testOpVideo"]')).toBeChecked();
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/automation-config-form.png', fullPage: true });
  });
});
