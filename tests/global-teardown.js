// Global teardown for Playwright tests
const { exec } = require('child_process');
const fs = require('fs');

async function globalTeardown() {
    console.log('🧹 Starting global test teardown...');
    
    const timestamp = process.env.TEST_TIMESTAMP;
    
    if (timestamp) {
        console.log('📊 Generating Allure reports...');
        
        // Generate Allure report
        const allureCommand = `allure generate test-reports/${timestamp}/allure-results --clean -o test-reports/${timestamp}/allure-report`;
        
        return new Promise((resolve) => {
            exec(allureCommand, (error, stdout, stderr) => {
                if (error) {
                    console.error('❌ Error generating Allure report:', error.message);
                } else {
                    console.log('✅ Allure report generated successfully');
                    console.log(`📊 Report available at: test-reports/${timestamp}/allure-report/index.html`);
                }
                
                console.log('✅ Global teardown completed');
                resolve();
            });
        });
    } else {
        console.log('✅ Global teardown completed (no timestamp found)');
    }
}

module.exports = globalTeardown;
