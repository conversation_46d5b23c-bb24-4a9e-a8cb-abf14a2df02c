const { test, expect, chromium } = require('@playwright/test');

test.describe('Multi-User Collaborative Workflow Tests', () => {
    let browser1, browser2, browser3;
    let context1, context2, context3;
    let page1, page2, page3;

    test.beforeAll(async () => {
        // Launch 3 separate browser instances
        browser1 = await chromium.launch({ headless: false });
        browser2 = await chromium.launch({ headless: false });
        browser3 = await chromium.launch({ headless: false });

        // Create separate contexts for each browser
        context1 = await browser1.newContext();
        context2 = await browser2.newContext();
        context3 = await browser3.newContext();

        // Create pages
        page1 = await context1.newPage();
        page2 = await context2.newPage();
        page3 = await context3.newPage();
    });

    test.afterAll(async () => {
        await browser1.close();
        await browser2.close();
        await browser3.close();
    });

    test('Collaborative Workflow: Editor → Admin → Editor', async () => {
        console.log('🚀 Starting multi-user collaborative workflow test');

        // Browser 1: Editor login and start build update
        console.log('👤 Browser 1: Editor login and build setup');
        await page1.goto('http://localhost:7777/gqa/login');
        await page1.fill('input[name="username"]', 'editor');
        await page1.fill('input[name="password"]', 'editor123');
        await page1.click('button[type="submit"]');
        await page1.waitForURL('**/main');

        // Navigate to NIC Checks and start workflow
        await page1.waitForSelector('#nic-checks-section', { timeout: 10000 });
        await page1.selectOption('#releaseType', 'HF');
        await page1.waitForSelector('#buildFormContainer', { visible: true });

        // Fill build form
        await page1.fill('input[name="buildsetup"]', 'https://github.com/test/collaborative-build');
        await page1.fill('input[name="zohoshowinput"]', 'https://github.com/test/collaborative-zoho');
        await page1.fill('input[name="shapeframework"]', 'shape-framework-v1.0');
        await page1.fill('input[name="graphikosmedia"]', 'graphikos-media-v2.1');
        
        // Submit build form
        await page1.click('#buildForm button[type="submit"]');
        await page1.waitForSelector('.notification.success', { timeout: 10000 });
        console.log('✅ Browser 1: Build form submitted');

        // Start automation
        await page1.waitForSelector('#automationSection', { visible: true });
        await page1.click('button:has-text("Start Automation")');
        await page1.waitForSelector('#automationReportSection', { visible: true });
        console.log('✅ Browser 1: Automation started');

        // Browser 2: Admin login and interact with Prechecks
        console.log('👤 Browser 2: Admin login and prechecks interaction');
        await page2.goto('http://localhost:7777/gqa/login');
        await page2.fill('input[name="username"]', 'admin');
        await page2.fill('input[name="password"]', 'admin123');
        await page2.click('button[type="submit"]');
        await page2.waitForURL('**/main');

        // Navigate to admin panel
        await page2.goto('http://localhost:7777/gqa/admin');
        await page2.waitForSelector('#prechecksPanelAdmin', { visible: true });

        // Complete admin pre-checks
        const adminPreChecks = [
            '#showBuildDiff_qa', '#showBuildDiff_server', '#showBuildDiff_client',
            '#showMigrationFiles_qa', '#showMigrationFiles_server', '#showMigrationFiles_client',
            '#databaseChanges_qa', '#databaseChanges_server', '#databaseChanges_client',
            '#configurationUpdates_qa', '#configurationUpdates_server', '#configurationUpdates_client',
            '#dependenciesCheck_qa', '#dependenciesCheck_server', '#dependenciesCheck_client'
        ];

        for (const checkboxId of adminPreChecks) {
            await page2.check(checkboxId);
        }
        console.log('✅ Browser 2: All admin pre-checks completed');

        // Browser 3: Editor continues workflow
        console.log('👤 Browser 3: Editor continues workflow');
        await page3.goto('http://localhost:7777/gqa/login');
        await page3.fill('input[name="username"]', 'editor');
        await page3.fill('input[name="password"]', 'editor123');
        await page3.click('button[type="submit"]');
        await page3.waitForURL('**/main');

        // Should see the ongoing workflow state
        await page3.waitForSelector('#nic-checks-section', { timeout: 10000 });
        
        // Verify state synchronization - automation section should be visible
        await expect(page3.locator('#automationReportSection')).toBeVisible();
        console.log('✅ Browser 3: State synchronized - automation section visible');

        // Continue workflow - confirm report
        await page3.check('#reportReceivedCheckbox');
        await page3.waitForSelector('.notification.success', { timeout: 5000 });
        console.log('✅ Browser 3: Report confirmed');

        // Fill manual testcase
        await page3.waitForSelector('#manualTestcaseSection', { visible: true });
        await page3.fill('#manualTestcaseSheet', 'https://docs.google.com/spreadsheets/collaborative-test');
        await page3.check('#manualTestcaseCheckbox');
        console.log('✅ Browser 3: Manual testcase completed');

        // Verify Update Build in Pre button is enabled (all admin pre-checks + manual testcase)
        const updatePreButton = page3.locator('#preBuildUpdateSection button');
        await expect(updatePreButton).toBeEnabled();
        console.log('✅ Browser 3: Update Build in Pre button enabled');

        // Update to Pre environment
        await page3.click('#preBuildUpdateSection button');
        await page3.waitForSelector('.notification.success', { timeout: 5000 });
        await page3.waitForSelector('#preBuildVerificationSection', { visible: true });
        console.log('✅ Browser 3: Pre-build update completed');

        // Complete pre-build verification
        await page3.check('#preSanityCheckbox');
        await page3.check('#preAutomationCheckbox');
        await page3.waitForSelector('#liveBuildUpdateSection', { visible: true });
        console.log('✅ Browser 3: Pre-build verification completed');

        // Update to Live
        await page3.click('#updateToLiveBtn');
        await page3.waitForSelector('#sanityInLiveSection', { visible: true });
        console.log('✅ Browser 3: Live update completed');

        // Complete sanity in live
        await page3.check('#sanityInLiveCheckbox');
        await page3.waitForSelector('#completeReleaseSection', { visible: true });
        console.log('✅ Browser 3: Sanity in live completed');

        // Complete release
        await page3.click('#completeReleaseBtn');
        await page3.waitForSelector('#check-status-section.active', { timeout: 5000 });
        console.log('✅ Browser 3: Release completed');

        // Verify state synchronization across all browsers
        console.log('🔍 Verifying state synchronization across all browsers');

        // Browser 1: Should see the completed state
        await page1.reload();
        await page1.waitForSelector('#nic-checks-section', { timeout: 10000 });
        // Should be reset to initial state after completion
        const releaseTypeValue1 = await page1.inputValue('#releaseType');
        expect(releaseTypeValue1).toBe('');
        console.log('✅ Browser 1: State synchronized - workflow reset');

        // Browser 2: Check admin panel state
        await page2.reload();
        await page2.waitForSelector('#prechecksPanelAdmin', { visible: true });
        // Admin pre-checks should be reset
        const firstCheckbox = await page2.isChecked('#showBuildDiff_qa');
        expect(firstCheckbox).toBe(false);
        console.log('✅ Browser 2: State synchronized - admin pre-checks reset');

        // Browser 3: Should be on check status page with completed build
        await expect(page3.locator('#check-status-section.active')).toBeVisible();
        await expect(page3.locator('#nicChecksTable tbody tr')).toHaveCount(2); // Header + 1 completed build
        console.log('✅ Browser 3: State synchronized - completed build visible');

        console.log('🎉 Multi-user collaborative workflow test completed successfully!');
    });

    test('Real-Time State Synchronization Test', async () => {
        console.log('🔄 Testing real-time state synchronization');

        // Browser 1: Start new workflow
        await page1.goto('http://localhost:7777/gqa');
        await page1.selectOption('#releaseType', 'Release');
        await page1.waitForSelector('#buildFormContainer', { visible: true });

        // Browser 2: Should see the same state
        await page2.goto('http://localhost:7777/gqa');
        await page2.waitForTimeout(2000); // Wait for state sync
        const releaseTypeValue = await page2.inputValue('#releaseType');
        expect(releaseTypeValue).toBe('Release');
        console.log('✅ Real-time state sync: Release type synchronized');

        // Browser 1: Fill form field
        await page1.fill('input[name="buildsetup"]', 'https://github.com/test/sync-test');

        // Browser 2: Should see the same value after auto-save
        await page2.waitForTimeout(3000); // Wait for auto-save and sync
        await page2.reload();
        await page2.waitForSelector('#buildFormContainer', { visible: true });
        const buildSetupValue = await page2.inputValue('input[name="buildsetup"]');
        expect(buildSetupValue).toBe('https://github.com/test/sync-test');
        console.log('✅ Real-time state sync: Form field synchronized');

        console.log('🎉 Real-time state synchronization test completed!');
    });

    test('Collaborative Conflict Resolution Test', async () => {
        console.log('⚡ Testing collaborative conflict resolution');

        // Browser 1: Start workflow
        await page1.goto('http://localhost:7777/gqa');
        await page1.selectOption('#releaseType', 'HF');
        await page1.fill('input[name="buildsetup"]', 'https://github.com/test/conflict-test-1');

        // Browser 2: Try to modify same workflow
        await page2.goto('http://localhost:7777/gqa');
        await page2.waitForTimeout(2000); // Wait for state sync
        
        // Should see the ongoing workflow
        const releaseTypeValue = await page2.inputValue('#releaseType');
        expect(releaseTypeValue).toBe('HF');
        
        // Should see the build setup value
        const buildSetupValue = await page2.inputValue('input[name="buildsetup"]');
        expect(buildSetupValue).toBe('https://github.com/test/conflict-test-1');
        
        console.log('✅ Conflict resolution: Consistent state maintained');

        // Browser 2: Try to modify - should be disabled if workflow is in progress
        const releaseTypeSelect = page2.locator('#releaseType');
        const isDisabled = await releaseTypeSelect.isDisabled();
        
        if (isDisabled) {
            console.log('✅ Conflict resolution: Previous sections properly disabled');
        } else {
            console.log('ℹ️ Conflict resolution: Sections not disabled (may be expected behavior)');
        }

        console.log('🎉 Collaborative conflict resolution test completed!');
    });
});

module.exports = {};
