import { test, expect } from '@playwright/test';

test.describe('Update Build Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('http://localhost:8080/gqa/login');
    
    // Login as editor user
    await page.fill('#username', 'editor');
    await page.fill('#password', 'editor123');
    await page.click('button[type="submit"]');
    
    // Wait for navigation to main page
    await page.waitForURL('**/main');
    
    // Navigate to Update Build section
    await page.click('a[data-section="update-build"]');
    await page.waitForTimeout(1000);
  });

  test('Complete Update Build automation workflow', async ({ page }) => {
    // Verify Update Build section is loaded
    await expect(page.locator('#update-build-section')).toBeVisible();
    await expect(page.locator('.automation-config-form')).toBeVisible();
    
    // Step 1: Fill GC Launch Configurations
    await page.fill('input[name="domainSetup"]', 'gcautomation-update-test');
    await page.fill('input[name="localUrl"]', 'gcautomation-update.localzoho.com');
    await page.fill('input[name="buildDetailsAPI"]', 'show/buildDetails/v2');
    
    // Step 2: Fill Report and Account Configurations
    await page.fill('input[name="localServerMachineName"]', 'update-build-server-01');
    
    // Step 3: Fill Notification Configurations
    await page.fill('input[name="automationStatus"]', 'debug'); // Testing debug mode
    
    // Step 4: Fill Cliq Bot Properties
    await page.fill('input[name="tomcatPath"]', '/opt/tomcat/update-build');
    await page.fill('input[name="reportSubjectForBot"]', 'Update Build Automation');
    await page.fill('input[name="sendReportInBot"]', 'yes');
    await page.fill('input[name="cliqReportBotURL"]', 'https://cliq.zoho.com/company/*********/api/v2/bots/updatebuild/incoming');
    
    // Step 5: Configure Browser Settings
    await page.selectOption('select[name="browser"]', 'chromeheadless');
    
    // Configure checkboxes
    await page.uncheck('input[name="addExtension"]');
    await page.check('input[name="idc"]');
    await page.check('input[name="gridEnabled"]');
    await page.uncheck('input[name="enableConsoleLogs"]');
    await page.uncheck('input[name="enableBiDiLogging"]');
    await page.uncheck('input[name="testOpVideo"]');
    
    // Take screenshot before starting automation
    await page.screenshot({ path: 'test-results/update-build-config-filled.png', fullPage: true });
    
    // Step 6: Start Automation
    await page.click('button:has-text("Start Automation")');
    
    // Wait for automation to start (simulate response)
    await page.waitForTimeout(2000);
    
    // Step 7: Verify automation started (check for success notification or status change)
    // Note: This is a placeholder as the actual automation integration is not implemented
    
    // Step 8: Navigate to Check Status to verify entry
    await page.click('a[data-section="check-status"]');
    await page.waitForTimeout(1000);
    
    // Step 9: Switch to Recent Builds view
    await page.selectOption('#statusTypeFilter', 'recent-builds');
    await expect(page.locator('#recentBuildsTable')).toBeVisible();
    await expect(page.locator('#nicChecksTable')).not.toBeVisible();
    
    // Verify the table structure for Recent Builds
    const tableHeaders = await page.locator('#recentBuildsTable thead th').allTextContents();
    expect(tableHeaders).toContain('Domain Setup');
    expect(tableHeaders).toContain('Browser');
    expect(tableHeaders).toContain('Automation Status');
    expect(tableHeaders).toContain('Report URL');
    expect(tableHeaders).toContain('Status');
    expect(tableHeaders).toContain('Created');
    
    // Take final screenshot
    await page.screenshot({ path: 'test-results/update-build-status-view.png', fullPage: true });
  });

  test('Verify Update Build automation form fields and defaults', async ({ page }) => {
    // Verify automation configuration form is visible
    await expect(page.locator('.automation-config-form')).toBeVisible();
    
    // Verify GC Launch Configuration defaults
    await expect(page.locator('input[name="domainSetup"]')).toHaveValue('gcautomation');
    await expect(page.locator('input[name="localUrl"]')).toHaveValue('gcautomation.localzoho.com');
    await expect(page.locator('input[name="buildDetailsAPI"]')).toHaveValue('show/buildDetails');
    
    // Verify Report and Account Configuration
    await expect(page.locator('input[name="localServerMachineName"]')).toHaveValue('');
    
    // Verify Notification Configuration
    await expect(page.locator('input[name="automationStatus"]')).toHaveValue('automation');
    
    // Verify Cliq Bot Properties defaults
    await expect(page.locator('input[name="tomcatPath"]')).toHaveValue('');
    await expect(page.locator('input[name="reportSubjectForBot"]')).toHaveValue('Full Automation');
    await expect(page.locator('input[name="sendReportInBot"]')).toHaveValue('yes');
    await expect(page.locator('input[name="cliqReportBotURL"]')).toHaveValue('https://cliq.zoho.com/company/*********/api/v2/bots/showreport/incoming');
    
    // Verify Browser Configuration defaults
    await expect(page.locator('select[name="browser"]')).toHaveValue('googlechrome');
    
    // Verify checkbox defaults
    await expect(page.locator('input[name="addExtension"]')).toBeChecked();
    await expect(page.locator('input[name="idc"]')).not.toBeChecked();
    await expect(page.locator('input[name="gridEnabled"]')).not.toBeChecked();
    await expect(page.locator('input[name="enableConsoleLogs"]')).toBeChecked();
    await expect(page.locator('input[name="enableBiDiLogging"]')).toBeChecked();
    await expect(page.locator('input[name="testOpVideo"]')).toBeChecked();
    
    // Verify all browser options are available
    const browserOptions = await page.locator('select[name="browser"] option').allTextContents();
    expect(browserOptions).toEqual([
      'Google Chrome',
      'Chrome Headless', 
      'Firefox',
      'Edge',
      'Internet Explorer',
      'Safari',
      'PhantomJS'
    ]);
    
    // Verify Start Automation button is present
    await expect(page.locator('button:has-text("Start Automation")')).toBeVisible();
    
    // Take screenshot of the form
    await page.screenshot({ path: 'test-results/update-build-form-defaults.png', fullPage: true });
  });

  test('Test browser dropdown functionality', async ({ page }) => {
    // Test changing browser selection
    await page.selectOption('select[name="browser"]', 'firefox');
    await expect(page.locator('select[name="browser"]')).toHaveValue('firefox');
    
    await page.selectOption('select[name="browser"]', 'edge');
    await expect(page.locator('select[name="browser"]')).toHaveValue('edge');
    
    await page.selectOption('select[name="browser"]', 'safari');
    await expect(page.locator('select[name="browser"]')).toHaveValue('safari');
    
    // Test checkbox interactions
    await page.check('input[name="idc"]');
    await expect(page.locator('input[name="idc"]')).toBeChecked();
    
    await page.uncheck('input[name="addExtension"]');
    await expect(page.locator('input[name="addExtension"]')).not.toBeChecked();
    
    // Test text input modifications
    await page.fill('input[name="automationStatus"]', 'debug');
    await expect(page.locator('input[name="automationStatus"]')).toHaveValue('debug');
    
    // Take screenshot of modified form
    await page.screenshot({ path: 'test-results/update-build-form-modified.png', fullPage: true });
  });
});
