// Global setup for Playwright tests
const { chromium } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

async function globalSetup() {
    console.log('🚀 Starting global test setup...');
    
    // Create test reports directory structure
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportDir = `test-reports/${timestamp}`;
    
    const directories = [
        reportDir,
        `${reportDir}/screenshots`,
        `${reportDir}/videos`,
        `${reportDir}/allure-results`,
        `${reportDir}/allure-report`
    ];
    
    directories.forEach(dir => {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            console.log(`📁 Created directory: ${dir}`);
        }
    });
    
    // Store timestamp for use in tests
    process.env.TEST_TIMESTAMP = timestamp;
    
    // Wait for application to be ready
    console.log('⏳ Waiting for GATE application to be ready...');
    
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    try {
        // Try to access the login page
        await page.goto('http://localhost:7777/gqa/login', {
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // Check if login form is present
        await page.waitForSelector('input[name="username"]', { timeout: 10000 });
        console.log('✅ GATE application is ready for testing');
        
    } catch (error) {
        console.error('❌ GATE application is not ready:', error.message);
        throw new Error('Application startup failed');
    } finally {
        await browser.close();
    }
    
    console.log('✅ Global setup completed successfully');
}

module.exports = globalSetup;
