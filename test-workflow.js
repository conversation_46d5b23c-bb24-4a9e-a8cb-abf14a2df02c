const { chromium } = require('playwright');

async function testWorkflow() {
    console.log('🚀 Starting workflow test...');
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    try {
        // Navigate to login
        await page.goto('http://localhost:7777/gqa/login');
        console.log('📱 Navigated to login page');
        
        // Login as admin
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin123');
        await page.click('button[type="submit"]');
        await page.waitForURL('**/main');
        console.log('✅ Logged in successfully');
        
        // Wait for NIC Checks section to load
        await page.waitForSelector('#nic-checks-section', { timeout: 10000 });
        console.log('📋 NIC Checks section loaded');
        
        // Select release type
        await page.selectOption('#releaseType', 'HF');
        console.log('✅ Release type selected');
        
        // Wait for build form to appear
        await page.waitForSelector('#buildForm', { visible: true, timeout: 5000 });
        console.log('📝 Build form appeared');
        
        // Fill some basic fields
        await page.fill('input[name="buildsetup"]', 'test-build-url');
        await page.fill('input[name="zohoshowinput"]', 'test-zoho-url');
        console.log('📝 Form fields filled');
        
        // Submit the form
        await page.click('#buildForm button[type="submit"]');
        console.log('📤 Build form submitted');
        
        // Wait for success notification
        await page.waitForSelector('.notification.success', { timeout: 10000 });
        console.log('✅ Build submission successful');
        
        // Wait for automation section to appear
        await page.waitForSelector('#automationSection', { visible: true, timeout: 5000 });
        console.log('🤖 Automation section appeared');
        
        // Start automation
        await page.click('button:has-text("Start Automation")');
        console.log('🚀 Automation started');
        
        // Wait for report section
        await page.waitForSelector('#automationReportSection', { visible: true, timeout: 10000 });
        console.log('📊 Report section appeared');
        
        // Check the report received checkbox
        await page.check('#reportReceivedCheckbox');
        console.log('✅ Report received checkbox checked');
        
        // Wait a moment for the API call
        await page.waitForTimeout(3000);
        
        // Check for success or error
        const successNotification = await page.locator('.notification.success').count();
        const errorNotification = await page.locator('.notification.error').count();
        
        if (successNotification > 0) {
            console.log('🎉 SUCCESS: Confirm report received worked!');
        } else if (errorNotification > 0) {
            const errorText = await page.locator('.notification.error').textContent();
            console.log('❌ ERROR: Confirm report failed:', errorText);
        } else {
            console.log('⏳ No notification found, checking console logs...');
        }
        
        // Get console logs
        const logs = await page.evaluate(() => {
            return window.console.logs || [];
        });
        
        console.log('📋 Console logs:', logs);
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        await browser.close();
    }
}

testWorkflow().catch(console.error);
