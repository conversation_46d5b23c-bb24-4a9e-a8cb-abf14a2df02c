#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Create timestamp for report folder
const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
const reportDir = `report/Allure-Report/${timestamp}`;

console.log('🎯 GATE Automation - Comprehensive Test Report Generation');
console.log('=' .repeat(60));

// Ensure report directories exist
function ensureDirectoryExists(dir) {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`📁 Created directory: ${dir}`);
    }
}

// Run tests for specific role
async function runRoleTests(roleName, specFile) {
    console.log(`\n🧪 Running ${roleName} Role Tests...`);
    console.log('-'.repeat(40));
    
    try {
        const command = `npx playwright test ${specFile} --reporter=allure-playwright`;
        console.log(`📋 Command: ${command}`);
        
        execSync(command, { 
            stdio: 'inherit',
            cwd: process.cwd()
        });
        
        console.log(`✅ ${roleName} tests completed successfully`);
        return true;
    } catch (error) {
        console.error(`❌ ${roleName} tests failed:`, error.message);
        return false;
    }
}

// Generate Allure report
function generateAllureReport() {
    console.log('\n📊 Generating Allure Report...');
    console.log('-'.repeat(40));
    
    try {
        // Ensure report directory exists
        ensureDirectoryExists(reportDir);
        
        // Generate Allure report
        const command = `npx allure generate allure-results --output ${reportDir}/allure-report --clean`;
        console.log(`📋 Command: ${command}`);
        
        execSync(command, { stdio: 'inherit' });
        
        console.log(`✅ Allure report generated: ${reportDir}/allure-report`);
        return true;
    } catch (error) {
        console.error('❌ Failed to generate Allure report:', error.message);
        return false;
    }
}

// Generate HTML summary report
function generateHtmlReport() {
    console.log('\n📄 Generating HTML Summary Report...');
    console.log('-'.repeat(40));
    
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GATE Automation Test Report - ${timestamp}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .timestamp { color: #7f8c8d; font-size: 14px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; background: #ecf0f1; }
        .section h2 { margin-top: 0; color: #2c3e50; }
        .test-class { margin: 15px 0; padding: 10px; background: white; border-radius: 4px; }
        .test-class h3 { margin: 0 0 10px 0; color: #34495e; }
        .test-list { list-style: none; padding: 0; }
        .test-list li { padding: 5px 0; border-bottom: 1px solid #ecf0f1; }
        .status-pass { color: #27ae60; font-weight: bold; }
        .status-fail { color: #e74c3c; font-weight: bold; }
        .links { text-align: center; margin: 20px 0; }
        .links a { display: inline-block; margin: 0 10px; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 4px; }
        .links a:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 GATE Automation Test Report</h1>
            <div class="timestamp">Generated: ${new Date().toLocaleString()}</div>
            <div class="timestamp">Version: 1.1.0-stable</div>
        </div>
        
        <div class="section">
            <h2>📊 Test Execution Summary</h2>
            <p>Comprehensive role-based testing for GATE Automation application covering all user roles and collaborative workflows.</p>
        </div>
        
        <div class="section">
            <h2>🧪 Test Classes Executed</h2>
            
            <div class="test-class">
                <h3>👤 AdminRoleFullTestcaseFlowVerification</h3>
                <ul class="test-list">
                    <li>TC-ADMIN-001: Login with valid credentials</li>
                    <li>TC-ADMIN-002: Access admin panel</li>
                    <li>TC-ADMIN-003: User management functionality</li>
                    <li>TC-ADMIN-004: Complete NIC Check workflow</li>
                    <li>TC-ADMIN-005: Prechecks management access</li>
                    <li>TC-ADMIN-006: State management restrictions</li>
                    <li>TC-ADMIN-COMPLETE: Full admin workflow end-to-end</li>
                </ul>
            </div>
            
            <div class="test-class">
                <h3>✏️ EditorRoleFullTestcaseFlowVerification</h3>
                <ul class="test-list">
                    <li>TC-EDITOR-001: Login and access verification</li>
                    <li>TC-EDITOR-002: Release type selection (starts empty)</li>
                    <li>TC-EDITOR-003: Build form submission</li>
                    <li>TC-EDITOR-004: Automation configuration</li>
                    <li>TC-EDITOR-005: Report confirmation workflow</li>
                    <li>TC-EDITOR-006: Prechecks read-only access</li>
                    <li>TC-EDITOR-007: Multiple submissions workflow</li>
                    <li>TC-EDITOR-COMPLETE: Full editor workflow end-to-end</li>
                </ul>
            </div>
            
            <div class="test-class">
                <h3>🔑 UltimateRoleFullTestcaseFlowVerification</h3>
                <ul class="test-list">
                    <li>TC-ULTIMATE-001: Login and full access verification</li>
                    <li>TC-ULTIMATE-002: Complete NIC Check workflow</li>
                    <li>TC-ULTIMATE-003: Full prechecks management access</li>
                    <li>TC-ULTIMATE-004: State management and reset functionality</li>
                    <li>TC-ULTIMATE-005: Advanced features testing</li>
                    <li>TC-ULTIMATE-006: User management with Ultimate privileges</li>
                    <li>TC-ULTIMATE-COMPLETE: Full Ultimate workflow end-to-end</li>
                </ul>
            </div>
            
            <div class="test-class">
                <h3>🤝 CollaborationVerification</h3>
                <ul class="test-list">
                    <li>TC-COLLAB-001: Admin starts workflow, Editor continues</li>
                    <li>TC-COLLAB-002: Real-time state synchronization</li>
                    <li>TC-COLLAB-003: Field locking prevents conflicts</li>
                    <li>TC-COLLAB-004: QA verifies collaborative state (read-only)</li>
                    <li>TC-COLLAB-005: Multiple users view Check Status updates</li>
                    <li>TC-COLLAB-006: Cross-device workflow continuation</li>
                </ul>
            </div>
            
            <div class="test-class">
                <h3>📊 StateMaintananceVerification</h3>
                <ul class="test-list">
                    <li>TC-STATE-001: Data persistence across page refreshes</li>
                    <li>TC-STATE-002: Workflow progress maintained across sessions</li>
                    <li>TC-STATE-003: Prechecks data synchronized across users</li>
                    <li>TC-STATE-004: Check Status table real-time updates</li>
                    <li>TC-STATE-005: Ultimate role targeted state clearing</li>
                    <li>TC-STATE-006: Multiple submissions with unique IDs</li>
                    <li>TC-STATE-007: Form reset between submissions</li>
                </ul>
            </div>
        </div>
        
        <div class="links">
            <a href="allure-report/index.html">📊 View Detailed Allure Report</a>
            <a href="../../../testcase.md">📋 View Test Case Documentation</a>
            <a href="http://localhost:7777/gqa">🌐 Live Application</a>
        </div>
        
        <div class="section">
            <h2>🎯 Test Environment</h2>
            <ul>
                <li><strong>Application URL:</strong> http://localhost:7777/gqa</li>
                <li><strong>Test Framework:</strong> Playwright</li>
                <li><strong>Report Framework:</strong> Allure</li>
                <li><strong>Browser Coverage:</strong> Chrome, Firefox, Safari</li>
                <li><strong>Mobile Testing:</strong> iOS Safari, Android Chrome</li>
            </ul>
        </div>
    </div>
</body>
</html>`;

    const htmlPath = path.join(reportDir, 'report.html');
    fs.writeFileSync(htmlPath, htmlContent);
    console.log(`✅ HTML report generated: ${htmlPath}`);
}

// Main execution
async function main() {
    console.log(`📅 Report timestamp: ${timestamp}`);
    console.log(`📁 Report directory: ${reportDir}`);
    
    // Ensure base directories exist
    ensureDirectoryExists('report');
    ensureDirectoryExists('report/Allure-Report');
    ensureDirectoryExists(reportDir);
    
    // Test specifications
    const testSpecs = [
        { name: 'Admin', file: 'tests/playwright/AdminRoleFullTestcaseFlowVerification.spec.js' },
        { name: 'Editor', file: 'tests/playwright/EditorRoleFullTestcaseFlowVerification.spec.js' },
        { name: 'Ultimate', file: 'tests/playwright/UltimateRoleFullTestcaseFlowVerification.spec.js' },
        { name: 'Collaboration', file: 'tests/playwright/CollaborationVerification.spec.js' },
        { name: 'State Maintenance', file: 'tests/playwright/StateMaintananceVerification.spec.js' }
    ];
    
    let allTestsPassed = true;
    
    // Run all test suites
    for (const spec of testSpecs) {
        const result = await runRoleTests(spec.name, spec.file);
        if (!result) {
            allTestsPassed = false;
        }
    }
    
    // Generate reports
    const allureGenerated = generateAllureReport();
    generateHtmlReport();
    
    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📋 EXECUTION SUMMARY');
    console.log('='.repeat(60));
    console.log(`📊 All Tests Passed: ${allTestsPassed ? '✅ YES' : '❌ NO'}`);
    console.log(`📄 Allure Report: ${allureGenerated ? '✅ Generated' : '❌ Failed'}`);
    console.log(`📁 Report Location: ${reportDir}`);
    console.log(`🌐 View Report: file://${path.resolve(reportDir)}/report.html`);
    console.log('='.repeat(60));
    
    process.exit(allTestsPassed ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, runRoleTests, generateAllureReport, generateHtmlReport };
