const fs = require('fs');
const path = require('path');

/**
 * Playwright Standalone Reporter
 * Generates self-contained HTML reports after test execution
 */
class StandaloneReporter {
    constructor(options = {}) {
        this.options = options;
        this.testResults = [];
        this.startTime = Date.now();
    }

    onBegin(config, suite) {
        console.log('🚀 Starting test execution with standalone reporting...');
        this.config = config;
        this.suite = suite;
    }

    onTestEnd(test, result) {
        // Process attachments to extract screenshots and videos
        const screenshots = [];
        const videos = [];
        const traces = [];

        if (result.attachments) {
            result.attachments.forEach(attachment => {
                if (attachment.name === 'screenshot' && attachment.path) {
                    screenshots.push(attachment.path);
                } else if (attachment.name === 'video' && attachment.path) {
                    videos.push(attachment.path);
                } else if (attachment.name === 'trace' && attachment.path) {
                    traces.push(attachment.path);
                }
            });
        }

        this.testResults.push({
            title: test.title,
            file: test.location.file,
            status: result.status,
            duration: result.duration,
            error: result.error,
            attachments: result.attachments || [],
            screenshots: screenshots,
            videos: videos,
            traces: traces
        });
    }

    onEnd(result) {
        console.log('📊 Generating standalone HTML report...');
        this.generateStandaloneReport(result);
    }

    generateStandaloneReport(result) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
                         new Date().toTimeString().split(' ')[0].replace(/:/g, '-');
        
        const reportDir = path.join(process.cwd(), 'report', 'Allure-Report', timestamp);
        
        // Ensure report directory exists
        if (!fs.existsSync(reportDir)) {
            fs.mkdirSync(reportDir, { recursive: true });
        }

        // Generate HTML content
        const htmlContent = this.generateHTMLContent(result, timestamp);
        
        // Write the standalone report
        const reportPath = path.join(reportDir, 'report.html');
        fs.writeFileSync(reportPath, htmlContent);
        
        console.log(`✅ Standalone report generated: ${reportPath}`);
        console.log(`🌐 Open in browser: file://${reportPath}`);
    }

    generateHTMLContent(result, timestamp) {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(t => t.status === 'passed').length;
        const failedTests = this.testResults.filter(t => t.status === 'failed').length;
        const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
        const duration = Date.now() - this.startTime;

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GATE Application - Standalone Test Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        .content {
            padding: 2rem;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .summary-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .summary-card h3 {
            margin: 0 0 0.5rem 0;
            color: #495057;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .summary-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin: 0;
        }
        .test-results {
            margin-top: 2rem;
        }
        .test-item {
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .test-name {
            font-weight: 600;
            color: #495057;
        }
        .test-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-passed {
            background: #d4edda;
            color: #155724;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        .status-skipped {
            background: #fff3cd;
            color: #856404;
        }
        .test-details {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        .test-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 1rem;
            margin-top: 0.5rem;
            font-family: monospace;
            font-size: 0.8rem;
            color: #721c24;
        }
        .test-media {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .media-section {
            margin-bottom: 1rem;
        }
        .media-section h5 {
            margin: 0 0 0.5rem 0;
            color: #495057;
            font-size: 0.9rem;
        }
        .screenshot-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        .screenshot-item {
            max-width: 200px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }
        .screenshot-item img {
            width: 100%;
            height: auto;
            cursor: pointer;
        }
        .video-player {
            max-width: 100%;
            border-radius: 4px;
        }
        .expandable-media {
            cursor: pointer;
            background: #e9ecef;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.25rem 0;
            font-size: 0.9rem;
        }
        .expandable-media:hover {
            background: #dee2e6;
        }
        .footer {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GATE Test Report</h1>
            <p>Standalone Test Execution Results</p>
            <p>Generated: ${new Date().toLocaleString()}</p>
            <p>Report ID: ${timestamp}</p>
        </div>
        
        <div class="content">
            <div class="summary">
                <div class="summary-card">
                    <h3>Total Tests</h3>
                    <div class="number">${totalTests}</div>
                </div>
                <div class="summary-card">
                    <h3>Passed</h3>
                    <div class="number">${passedTests}</div>
                </div>
                <div class="summary-card">
                    <h3>Failed</h3>
                    <div class="number">${failedTests}</div>
                </div>
                <div class="summary-card">
                    <h3>Success Rate</h3>
                    <div class="number">${successRate}%</div>
                </div>
                <div class="summary-card">
                    <h3>Duration</h3>
                    <div class="number">${Math.round(duration / 1000)}s</div>
                </div>
            </div>

            <div class="test-results">
                <h2>Test Execution Details</h2>
                ${this.generateTestItems()}
            </div>
        </div>

        <div class="footer">
            <p><strong>GATE Application - Comprehensive Testing Framework</strong></p>
            <p>This is a standalone report that can be viewed without a server.</p>
            <p>Generated by Playwright with custom standalone reporter.</p>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.9);">
        <span onclick="closeImageModal()" style="position: absolute; top: 15px; right: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; cursor: pointer;">&times;</span>
        <img id="modalImage" style="margin: auto; display: block; width: 80%; max-width: 700px; margin-top: 50px;">
    </div>

    <script>
        function toggleMediaSection(sectionId) {
            const section = document.getElementById(sectionId);
            const arrow = event.target.querySelector('.arrow');

            if (section.style.display === 'none') {
                section.style.display = 'block';
                arrow.textContent = '▼';
            } else {
                section.style.display = 'none';
                arrow.textContent = '▶';
            }
        }

        function openImageModal(imageSrc) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.style.display = 'block';
            modalImg.src = imageSrc;
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>`;
    }

    generateTestItems() {
        return this.testResults.map((test, index) => {
            const statusClass = `status-${test.status}`;
            const duration = test.duration ? `${Math.round(test.duration)}ms` : 'N/A';
            const fileName = path.basename(test.file);

            // Generate media content
            const mediaContent = this.generateMediaContent(test, index);

            return `
                <div class="test-item">
                    <div class="test-header">
                        <div class="test-name">${test.title}</div>
                        <div class="test-status ${statusClass}">${test.status}</div>
                    </div>
                    <div class="test-details">
                        <strong>File:</strong> ${fileName} |
                        <strong>Duration:</strong> ${duration}
                    </div>
                    ${test.error ? `
                        <div class="test-error">
                            <strong>Error:</strong><br>
                            ${test.error.message || test.error}
                        </div>
                    ` : ''}
                    ${mediaContent}
                </div>
            `;
        }).join('');
    }

    generateMediaContent(test, index) {
        if (!test.screenshots.length && !test.videos.length && !test.traces.length) {
            return '';
        }

        let mediaHtml = '<div class="test-media">';

        // Screenshots section
        if (test.screenshots.length > 0) {
            mediaHtml += `
                <div class="media-section">
                    <div class="expandable-media" onclick="toggleMediaSection('screenshots-${index}')">
                        📸 Screenshots (${test.screenshots.length}) <span class="arrow">▶</span>
                    </div>
                    <div id="screenshots-${index}" class="screenshot-gallery" style="display: none;">
                        ${test.screenshots.map((screenshot, i) => `
                            <div class="screenshot-item">
                                <img src="${screenshot}" alt="Screenshot ${i + 1}"
                                     onclick="openImageModal('${screenshot}')"
                                     title="Click to view full size">
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // Videos section
        if (test.videos.length > 0) {
            mediaHtml += `
                <div class="media-section">
                    <div class="expandable-media" onclick="toggleMediaSection('videos-${index}')">
                        🎥 Videos (${test.videos.length}) <span class="arrow">▶</span>
                    </div>
                    <div id="videos-${index}" style="display: none;">
                        ${test.videos.map((video, i) => `
                            <video class="video-player" controls>
                                <source src="${video}" type="video/webm">
                                <source src="${video}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // Traces section
        if (test.traces.length > 0) {
            mediaHtml += `
                <div class="media-section">
                    <div class="expandable-media" onclick="toggleMediaSection('traces-${index}')">
                        🔍 Traces (${test.traces.length}) <span class="arrow">▶</span>
                    </div>
                    <div id="traces-${index}" style="display: none;">
                        ${test.traces.map((trace, i) => `
                            <p><a href="${trace}" target="_blank">View Trace ${i + 1}</a></p>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        mediaHtml += '</div>';
        return mediaHtml;
    }
}

module.exports = StandaloneReporter;
