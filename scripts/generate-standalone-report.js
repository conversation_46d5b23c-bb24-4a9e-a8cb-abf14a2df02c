const fs = require('fs');
const path = require('path');

/**
 * Generate Standalone HTML Report
 * Creates a self-contained HTML report that can be viewed without a server
 */
function generateStandaloneReport() {
    console.log('🚀 Generating standalone HTML report...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
                     new Date().toTimeString().split(' ')[0].replace(/:/g, '-');
    
    const reportDir = path.join(__dirname, '..', 'report', 'Allure-Report', timestamp);
    
    // Ensure report directory exists
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }
    
    // Generate comprehensive HTML report
    const htmlContent = generateHTMLReport(timestamp);
    
    // Write the standalone report
    const reportPath = path.join(reportDir, 'report.html');
    fs.writeFileSync(reportPath, htmlContent);
    
    console.log(`✅ Standalone report generated: ${reportPath}`);
    return reportPath;
}

/**
 * Generate HTML Report Content
 */
function generateHTMLReport(timestamp) {
    const testResults = getTestResults();
    const currentDate = new Date().toLocaleString();
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GATE Application - Test Execution Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        .content {
            padding: 2rem;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .summary-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .summary-card h3 {
            margin: 0 0 0.5rem 0;
            color: #495057;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .summary-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin: 0;
        }
        .test-results {
            margin-top: 2rem;
        }
        .test-item {
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-name {
            font-weight: 500;
        }
        .test-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-passed {
            background: #d4edda;
            color: #155724;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        .screenshot {
            max-width: 100%;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .expandable {
            cursor: pointer;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 0.5rem 0;
        }
        .expandable:hover {
            background: #e9ecef;
        }
        .details {
            display: none;
            padding: 1rem;
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-top: 0.5rem;
        }
        .details.expanded {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GATE Test Report</h1>
            <p>Comprehensive Test Execution Results</p>
            <p>Generated: ${currentDate}</p>
            <p>Report ID: ${timestamp}</p>
        </div>
        
        <div class="content">
            <div class="summary">
                <div class="summary-card">
                    <h3>Total Tests</h3>
                    <div class="number">${testResults.total}</div>
                </div>
                <div class="summary-card">
                    <h3>Passed</h3>
                    <div class="number">${testResults.passed}</div>
                </div>
                <div class="summary-card">
                    <h3>Failed</h3>
                    <div class="number">${testResults.failed}</div>
                </div>
                <div class="summary-card">
                    <h3>Success Rate</h3>
                    <div class="number">${testResults.successRate}%</div>
                </div>
            </div>

            <div class="test-results">
                <h2>Test Execution Details</h2>
                ${generateTestItems(testResults.tests)}
            </div>
        </div>
    </div>

    <script>
        function toggleDetails(element) {
            const details = element.nextElementSibling;
            details.classList.toggle('expanded');
            const arrow = element.querySelector('.arrow');
            arrow.textContent = details.classList.contains('expanded') ? '▼' : '▶';
        }
    </script>
</body>
</html>`;
}

/**
 * Get Test Results (mock data for now, can be enhanced to read actual test results)
 */
function getTestResults() {
    return {
        total: 12,
        passed: 10,
        failed: 2,
        successRate: 83,
        tests: [
            {
                name: 'Login Functionality Test',
                status: 'passed',
                duration: '2.3s',
                details: 'Successfully tested login with all user roles'
            },
            {
                name: 'NIC Checks Workflow Test',
                status: 'passed',
                duration: '15.7s',
                details: 'Complete workflow from release type selection to completion'
            },
            {
                name: 'Admin Panel Pre-Checks Test',
                status: 'passed',
                duration: '8.2s',
                details: 'Validated all 15 admin pre-check checkboxes'
            },
            {
                name: 'Collaborative Functionality Test',
                status: 'passed',
                duration: '25.1s',
                details: 'Multi-user state synchronization working correctly'
            },
            {
                name: 'Build Form Validation Test',
                status: 'passed',
                duration: '4.6s',
                details: 'All form fields validated correctly'
            },
            {
                name: 'Automation Process Test',
                status: 'passed',
                duration: '12.3s',
                details: 'Automation workflow completed successfully'
            },
            {
                name: 'Report Generation Test',
                status: 'passed',
                duration: '3.8s',
                details: 'Markdown reports generated correctly'
            },
            {
                name: 'State Persistence Test',
                status: 'passed',
                duration: '6.9s',
                details: 'Workflow state maintained across sessions'
            },
            {
                name: 'Update to Live Test',
                status: 'passed',
                duration: '9.4s',
                details: 'Live update section displayed correctly'
            },
            {
                name: 'Download Functionality Test',
                status: 'passed',
                duration: '2.1s',
                details: 'Report download working as expected'
            },
            {
                name: 'Cross-Browser Compatibility Test',
                status: 'failed',
                duration: '18.7s',
                details: 'Some styling issues in Safari browser'
            },
            {
                name: 'Performance Load Test',
                status: 'failed',
                duration: '45.2s',
                details: 'Response time exceeded threshold under heavy load'
            }
        ]
    };
}

/**
 * Generate Test Items HTML
 */
function generateTestItems(tests) {
    return tests.map(test => `
        <div class="test-item">
            <div class="test-name">${test.name}</div>
            <div>
                <span class="test-status status-${test.status}">${test.status}</span>
                <small style="margin-left: 1rem; color: #6c757d;">${test.duration}</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> ${test.details}</p>
            <p><strong>Duration:</strong> ${test.duration}</p>
            <p><strong>Status:</strong> ${test.status.toUpperCase()}</p>
        </div>
    `).join('');
}

// Export for use in other scripts
module.exports = { generateStandaloneReport };

// Run if called directly
if (require.main === module) {
    generateStandaloneReport();
}
