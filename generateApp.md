# 🎯 GATE Automation - Complete Application Generation Prompt

## 📋 Application Overview

Generate a comprehensive web-based **GATE Automation** application for managing NIC (Network Interface Card) check workflows with role-based access control, collaborative features, and professional testing coverage.

---

## 🛠️ Technology Stack Requirements

### Backend Framework
- **Spring Boot**: Version 2.7.18
- **Java**: Version 11 or higher
- **Database**: H2 Database (in-memory for development)
- **Security**: Spring Security with BCrypt password encryption
- **Template Engine**: Thymeleaf for server-side rendering
- **Build Tool**: Gradle with wrapper

### Frontend Technologies
- **Architecture**: Single Page Application (SPA) with vanilla JavaScript
- **CSS Framework**: Custom responsive CSS with Grid and Flexbox
- **UI Components**: Modern form controls, modals, notifications
- **State Management**: localStorage and sessionStorage for persistence
- **Real-time Updates**: Auto-save functionality with 30-second intervals

### Testing Framework
- **Test Framework**: Playwright for end-to-end testing
- **Reporting**: Allure reports with comprehensive test coverage
- **Test Types**: Role-based testing, collaborative workflows, state persistence
- **Browser Coverage**: Chrome, Firefox, Safari, Edge
- **Mobile Testing**: iOS Safari, Android Chrome

---

## 🎭 User Roles & Permissions

### 👤 Admin Role
**Permissions**:
- Complete NIC check workflow access
- User management (create, update, view users)
- Admin panel access
- Prechecks management (full edit access)

**Restrictions**:
- Cannot clear application state (Ultimate role only)

**Use Cases**:
- Managing user accounts and roles
- Overseeing NIC check workflows
- System administration tasks

### ✏️ Editor Role
**Permissions**:
- Full NIC check workflow submission
- Form completion and automation configuration
- Report management and confirmation
- Basic workflow progression

**Restrictions**:
- No admin panel access
- Read-only access to prechecks management
- Cannot create or modify users

**Use Cases**:
- Daily NIC check operations
- Build submissions and configurations
- Report handling and validation

### 🔍 QA Role
**Permissions**:
- Basic workflow viewing
- Report validation and review
- Check Status table access

**Restrictions**:
- Read-only access to most features
- Cannot submit new NIC checks
- Cannot modify prechecks data

**Use Cases**:
- Quality assurance validation
- Report verification and review
- Workflow monitoring

### 🔑 Ultimate Role
**Permissions**:
- Complete system access (all Admin permissions)
- Application state reset functionality
- Advanced configuration access
- All prechecks management features

**Special Features**:
- Clear current workflow state (targeted reset)
- Advanced troubleshooting capabilities
- System maintenance operations

**Use Cases**:
- System maintenance and troubleshooting
- Complete workflow control
- Emergency state management

---

## 🔄 Complete Workflow Process

### 1. Authentication & Authorization
- Login page with username/password authentication
- Role-based redirection after successful login
- Session management with proper logout functionality

### 2. Release Type Selection
- Dropdown with options: HF (Hotfix), Release, Patch
- Starts empty (no default selection)
- Triggers build form display when selected

### 3. Build Configuration Form
**Required Fields**:
- Build Setup URL
- Zoho Show configuration
- Conversion settings
- Graphikos i18n configuration
- Graphikos Media settings
- Pictures configuration
- Image Conversion settings
- Shape Framework configuration
- Show Listing Dialog settings
- Show Offline configuration
- Show Rendering Framework settings
- Show Right Panel configuration
- Show Server settings
- Show Slideshow Views configuration
- Show UI configuration
- Zoho Show Input settings

### 4. Automation Configuration
**Settings**:
- Domain Setup configuration
- Local URL specification
- Build Details API endpoint
- Automation Status tracking
- Report Subject for Bot
- Send Report in Bot (yes/no)
- Cliq Report Bot URL
- Browser selection (Chrome, Firefox, Safari)
- Extension checkbox option

### 5. Report Confirmation Workflow
- Report Received checkbox
- Manual Sanity Report textarea
- Automation Report URL input field
- Manual Sanity Sheet URL input field

### 6. Prechecks Management (Right Panel)
**Four Containers**:
1. **SD Build Diff**: Large textarea + QA/Server/Client checkboxes
2. **Change File Diff**: Large textarea + QA/Server/Client checkboxes
3. **ZDCM Diff**: Large textarea + QA/Server/Client checkboxes
4. **Migration Diff**: Large textarea + QA/Server/Client checkboxes

**Access Control**:
- Admin/Ultimate: Full edit access
- Editor: Read-only access
- QA: Read-only access

### 7. Pre-Build Verification
- Pre Sanity Checkbox + Details input field
- Pre Automation Checkbox + Details input field

### 8. Manual Testcase Section (Refactored)
- Creator NIC Form Completed checkbox
- Creator NIC Form URL input field
- No "Manual Testcase Sheet" label
- No "confirm manual testcase" checkboxes

### 9. Live Update Section
**Five Required Checkboxes**:
- Sanity in Live
- Milestone
- Reverse Merge
- Learn Doc
- Connect Post

All checkboxes must be checked for workflow completion.

### 10. Check Status Page
- Table displaying all submitted NIC checks
- Unique build IDs with timestamps
- Build details panel (slides in from right)
- Real-time updates across users
- Professional expand/collapse functionality

---

## 🗄️ Database Schema

### USERS Table
```sql
CREATE TABLE USERS (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    USERNAME VARCHAR(50) UNIQUE NOT NULL,
    PASSWORD VARCHAR(255) NOT NULL,
    EMAIL VARCHAR(100) UNIQUE NOT NULL,
    FULL_NAME VARCHAR(100) NOT NULL,
    ROLE VARCHAR(20) NOT NULL,
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### BUILD_REQUEST Table
```sql
CREATE TABLE BUILD_REQUEST (
    ID BIGINT AUTO_INCREMENT PRIMARY KEY,
    BUILD_ID VARCHAR(100) UNIQUE NOT NULL,
    USERNAME VARCHAR(50) NOT NULL,
    RELEASE_TYPE VARCHAR(20) NOT NULL,
    BUILD_SETUP TEXT,
    ZOHO_SHOW TEXT,
    CONVERSION TEXT,
    STATUS VARCHAR(20) DEFAULT 'PENDING',
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Additional configuration fields
    DOMAIN_SETUP TEXT,
    LOCAL_URL TEXT,
    BROWSER VARCHAR(50),
    AUTOMATION_STATUS TEXT,
    REPORT_RECEIVED BOOLEAN DEFAULT FALSE
);
```

### Default Users
```sql
INSERT INTO USERS (USERNAME, PASSWORD, EMAIL, FULL_NAME, ROLE) VALUES
('admin', '$2a$10$encrypted_password', '<EMAIL>', 'Admin User', 'ADMIN'),
('editor', '$2a$10$encrypted_password', '<EMAIL>', 'Editor User', 'EDITOR'),
('qauser', '$2a$10$encrypted_password', '<EMAIL>', 'QA User', 'QA'),
('ultimate', '$2a$10$encrypted_password', '<EMAIL>', 'Ultimate User', 'ULTIMATE');
```

---

## 🎨 UI/UX Requirements

### Layout Design
- **Two-Column Layout**: Left panel (workflow), Right panel (prechecks)
- **Responsive Design**: Mobile-first approach with breakpoints
- **Navigation**: Top navigation bar with role-based menu items
- **Color Scheme**: Professional blue/gray theme with accent colors

### Form Design
- **Progressive Disclosure**: Show sections as user progresses
- **Field Validation**: Real-time validation with error messages
- **Auto-save**: Save form data every 30 seconds
- **State Persistence**: Maintain form state across sessions

### Interactive Elements
- **Sliding Panels**: Build details panel slides from right
- **Smooth Animations**: 300ms transitions for all interactions
- **Loading States**: Professional loading indicators
- **Notifications**: Toast notifications for user feedback

### Accessibility
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels
- **Color Contrast**: WCAG 2.1 AA compliance
- **Focus Management**: Clear focus indicators

---

## 🔐 Security Requirements

### Authentication
- **BCrypt Encryption**: All passwords encrypted with BCrypt
- **Session Management**: Secure session handling
- **CSRF Protection**: Cross-site request forgery protection
- **XSS Prevention**: Input sanitization and output encoding

### Authorization
- **Role-Based Access**: Method-level security annotations
- **URL Protection**: Secure endpoints based on roles
- **Admin Panel Security**: Ultimate role verification for sensitive operations

### Data Protection
- **Input Validation**: Server-side validation for all inputs
- **SQL Injection Prevention**: Parameterized queries
- **Data Sanitization**: Clean all user inputs

---

## 🧪 Testing Requirements

### Test Coverage
- **Role-Based Testing**: Comprehensive tests for all 4 user roles
- **Workflow Testing**: End-to-end workflow validation
- **Collaborative Testing**: Multi-user scenarios
- **State Persistence**: Data integrity across sessions
- **Cross-Browser Testing**: Chrome, Firefox, Safari, Edge
- **Mobile Testing**: iOS Safari, Android Chrome

### Test Classes Required
1. **AdminRoleFullTestcaseFlowVerification**: Admin workflow testing
2. **EditorRoleFullTestcaseFlowVerification**: Editor functionality validation
3. **UltimateRoleFullTestcaseFlowVerification**: Ultimate role comprehensive testing
4. **CollaborationVerification**: Multi-user collaborative workflows
5. **StateMaintananceVerification**: State persistence and data integrity

### Reporting
- **Allure Reports**: Professional test reporting with screenshots
- **HTML Reports**: Summary reports with test results
- **Video Recording**: Test execution videos for failures
- **Screenshots**: Failure screenshots for debugging

---

## 📊 API Endpoints

### Authentication Endpoints
- `POST /login` - User authentication
- `POST /logout` - User logout
- `GET /` - Main application page

### Build Management
- `POST /api/submit-build` - Submit new NIC check
- `GET /api/build-status/{id}` - Get build status
- `PUT /api/update-build/{id}` - Update build information
- `GET /api/completed-builds` - Get all completed builds

### Admin Operations
- `GET /admin` - Admin panel access
- `POST /api/admin/create-user` - Create new user
- `PUT /api/admin/update-roles` - Update user roles
- `POST /api/admin/clear-state` - Clear workflow state (Ultimate only)

### Prechecks Management
- `PUT /api/prechecks/{buildId}` - Update prechecks data
- `GET /api/prechecks/{buildId}` - Get prechecks status

---

## 🚀 Implementation Steps

### Phase 1: Project Setup
1. Initialize Spring Boot project with Gradle
2. Configure H2 database and JPA entities
3. Set up Spring Security with role-based authentication
4. Create basic project structure

### Phase 2: Backend Development
1. Implement User and BuildRequest entities
2. Create repositories and services
3. Develop REST API controllers
4. Implement security configuration

### Phase 3: Frontend Development
1. Create responsive HTML templates with Thymeleaf
2. Implement SPA functionality with vanilla JavaScript
3. Develop form validation and state management
4. Create interactive UI components

### Phase 4: Testing Implementation
1. Set up Playwright testing framework
2. Create comprehensive test suites for all roles
3. Implement Allure reporting
4. Add cross-browser and mobile testing

### Phase 5: Documentation & Packaging
1. Generate JavaDoc documentation
2. Create comprehensive README
3. Write database documentation
4. Package for distribution

---

## ✅ Quality Assurance Criteria

### Functionality
- All 4 user roles working with proper permissions
- Complete NIC check workflow from start to finish
- Multiple submissions with unique build IDs
- Real-time collaborative state management
- Professional UI with smooth animations

### Performance
- Page load times < 2 seconds
- State synchronization < 30 seconds
- Responsive design across all screen sizes
- Smooth animations and transitions

### Security
- Role-based access control working correctly
- All inputs validated and sanitized
- Passwords encrypted with BCrypt
- CSRF and XSS protection implemented

### Testing
- 95%+ test coverage across all modules
- All test cases passing consistently
- Cross-browser compatibility verified
- Mobile responsiveness confirmed

---

## 📞 Success Metrics

**Application is considered complete when**:
- ✅ All 4 user roles implemented with correct permissions
- ✅ Complete NIC check workflow functional
- ✅ Multiple submissions supported with unique IDs
- ✅ Collaborative state management working
- ✅ Professional UI with responsive design
- ✅ Comprehensive test suite with 95%+ coverage
- ✅ Complete documentation and packaging
- ✅ Production-ready deployment package

**Target Timeline**: 2-3 weeks for complete implementation
**Team Size**: 1-2 developers with AI assistance
**Deployment**: Ready for immediate production use

---

---

## 🔧 Detailed Implementation Guidance

### Spring Boot Configuration
```properties
# application.properties
server.port=7777
server.servlet.context-path=/gqa

# H2 Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.username=sa
spring.datasource.password=
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
```

### Gradle Build Configuration
```gradle
plugins {
    id 'org.springframework.boot' version '2.7.18'
    id 'io.spring.dependency-management' version '1.0.15.RELEASE'
    id 'java'
}

java {
    sourceCompatibility = '11'
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.thymeleaf.extras:thymeleaf-extras-springsecurity5'
    runtimeOnly 'com.h2database:h2'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}
```

### Key Implementation Details

#### State Management System
- **Auto-save Interval**: 30 seconds for all form data
- **Cross-tab Synchronization**: BroadcastChannel API for real-time updates
- **Collaborative Features**: Multiple users can work on same workflow
- **Field Locking**: Prevent conflicts during concurrent editing
- **State Persistence**: localStorage + server-side synchronization

#### Form Validation Rules
- **Required Fields**: All build configuration fields mandatory
- **URL Validation**: Proper URL format for all URL fields
- **Email Validation**: Valid email format for user creation
- **Password Strength**: Minimum 6 characters for user passwords
- **Real-time Validation**: Immediate feedback on field blur

#### Responsive Breakpoints
```css
/* Mobile First Approach */
@media (max-width: 576px) { /* Mobile */ }
@media (min-width: 577px) and (max-width: 768px) { /* Tablet */ }
@media (min-width: 769px) and (max-width: 992px) { /* Small Desktop */ }
@media (min-width: 993px) { /* Large Desktop */ }
```

#### Animation Specifications
- **Panel Transitions**: 300ms ease-in-out
- **Form Validation**: 200ms fade-in for error messages
- **Loading States**: Smooth spinner animations
- **Hover Effects**: 150ms transition for all interactive elements

---

## 📋 File Structure Template

```
GATE-Automation/
├── src/main/
│   ├── java/graphikos/automation/
│   │   ├── GraphikosAutomationApplication.java
│   │   ├── config/
│   │   │   ├── SecurityConfig.java
│   │   │   └── DataInitializer.java
│   │   ├── controller/
│   │   │   ├── AuthController.java
│   │   │   ├── MainController.java
│   │   │   ├── AdminController.java
│   │   │   ├── AdminPageController.java
│   │   │   └── BuildApiController.java
│   │   ├── model/
│   │   │   ├── User.java
│   │   │   └── BuildRequest.java
│   │   ├── repository/
│   │   │   ├── UserRepository.java
│   │   │   └── BuildRequestRepository.java
│   │   └── service/
│   │       ├── UserService.java
│   │       ├── BuildService.java
│   │       └── CustomUserDetailsService.java
│   └── resources/
│       ├── static/
│       │   ├── css/
│       │   │   ├── main.css
│       │   │   └── responsive.css
│       │   ├── js/
│       │   │   ├── spa.js
│       │   │   ├── admin-page.js
│       │   │   └── notifications.js
│       │   └── images/
│       │       └── logo.svg
│       ├── templates/
│       │   ├── index.html
│       │   ├── auth/
│       │   │   └── login.html
│       │   └── admin/
│       │       └── gate_admin.html
│       └── application.properties
├── tests/playwright/
│   ├── AdminRoleFullTestcaseFlowVerification.spec.js
│   ├── EditorRoleFullTestcaseFlowVerification.spec.js
│   ├── UltimateRoleFullTestcaseFlowVerification.spec.js
│   ├── CollaborationVerification.spec.js
│   ├── StateMaintananceVerification.spec.js
│   ├── global-setup.js
│   └── global-teardown.js
├── scripts/
│   └── generate-reports.js
├── docs/
│   └── javadoc/
├── playwright.config.js
├── package.json
├── build.gradle
├── README.md
├── testcase.md
├── db.md
└── generateApp.md
```

---

## 🎯 Critical Success Factors

### Must-Have Features
1. **Role-Based Access Control**: All 4 roles with correct permissions
2. **Complete Workflow**: From login to final submission
3. **Multiple Submissions**: Sequential NIC checks with unique IDs
4. **Collaborative State**: Real-time synchronization across users
5. **Professional UI**: Responsive design with smooth animations
6. **Comprehensive Testing**: 95%+ coverage with all test classes
7. **Complete Documentation**: README, testcase.md, db.md, JavaDoc

### Performance Requirements
- **Page Load**: < 2 seconds for all pages
- **State Sync**: < 30 seconds for collaborative updates
- **Form Validation**: < 200ms response time
- **Database Queries**: < 500ms for all operations
- **Test Execution**: < 5 minutes for complete test suite

### Quality Gates
- **Code Coverage**: Minimum 95% across all modules
- **Test Pass Rate**: 100% for all critical test cases
- **Browser Compatibility**: Chrome, Firefox, Safari, Edge
- **Mobile Responsiveness**: iOS Safari, Android Chrome
- **Security Validation**: All OWASP top 10 vulnerabilities addressed

---

## 📞 Support & Maintenance

### Troubleshooting Guide
- **Port Conflicts**: Use `lsof -ti:7777 | xargs kill -9` to free port
- **Database Issues**: Restart application to reset H2 in-memory database
- **Test Failures**: Clear browser cache and restart application
- **State Sync Issues**: Check localStorage and sessionStorage

### Monitoring & Logging
- **Application Logs**: Console output with structured logging
- **Database Console**: H2 console at `/h2-console` for debugging
- **Test Reports**: Allure reports with detailed execution logs
- **Performance Metrics**: Built-in Spring Boot Actuator endpoints

### Deployment Considerations
- **Production Database**: Replace H2 with PostgreSQL or MySQL
- **Security Hardening**: Disable H2 console in production
- **Load Balancing**: Configure for horizontal scaling
- **Monitoring**: Implement APM tools for production monitoring

---

**This comprehensive prompt provides all necessary specifications, implementation details, and quality criteria for generating a complete, production-ready GATE Automation application identical to the original.**

**Total Implementation Time**: 2-3 weeks with AI assistance
**Complexity Level**: Intermediate to Advanced
**Production Readiness**: Immediate deployment capability
**Maintenance Level**: Low to Medium ongoing maintenance required
