<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Class Hierarchy (GQA 1.0-SNAPSHOT API)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="jquery/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Class Hierarchy (GQA 1.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "./";
var useModuleDirectories = true;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a id="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a id="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
</nav>
</header>
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For All Packages</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="graphikos/automation/package-tree.html">graphikos.automation</a>, </li>
<li><a href="graphikos/automation/config/package-tree.html">graphikos.automation.config</a>, </li>
<li><a href="graphikos/automation/controller/package-tree.html">graphikos.automation.controller</a>, </li>
<li><a href="graphikos/automation/model/package-tree.html">graphikos.automation.model</a>, </li>
<li><a href="graphikos/automation/repository/package-tree.html">graphikos.automation.repository</a>, </li>
<li><a href="graphikos/automation/service/package-tree.html">graphikos.automation.service</a></li>
</ul>
</div>
<div class="contentContainer">
<section>
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.html" title="class in graphikos.automation.controller"><span class="typeNameLink">AdminController</span></a></li>
<li class="circle">graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller"><span class="typeNameLink">AdminController.UserCreateRequest</span></a></li>
<li class="circle">graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html" title="class in graphikos.automation.controller"><span class="typeNameLink">AdminController.UserRoleUpdate</span></a></li>
<li class="circle">graphikos.automation.controller.<a href="graphikos/automation/controller/AdminPageController.html" title="class in graphikos.automation.controller"><span class="typeNameLink">AdminPageController</span></a></li>
<li class="circle">graphikos.automation.controller.<a href="graphikos/automation/controller/AuthController.html" title="class in graphikos.automation.controller"><span class="typeNameLink">AuthController</span></a></li>
<li class="circle">graphikos.automation.controller.<a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller"><span class="typeNameLink">BuildApiController</span></a></li>
<li class="circle">graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model"><span class="typeNameLink">BuildRequest</span></a></li>
<li class="circle">graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service"><span class="typeNameLink">BuildService</span></a></li>
<li class="circle">graphikos.automation.service.<a href="graphikos/automation/service/CustomUserDetailsService.html" title="class in graphikos.automation.service"><span class="typeNameLink">CustomUserDetailsService</span></a> (implements org.springframework.security.core.userdetails.UserDetailsService)</li>
<li class="circle">graphikos.automation.config.<a href="graphikos/automation/config/DataInitializer.html" title="class in graphikos.automation.config"><span class="typeNameLink">DataInitializer</span></a> (implements org.springframework.boot.CommandLineRunner)</li>
<li class="circle">graphikos.automation.<a href="graphikos/automation/GraphikosAutomationApplication.html" title="class in graphikos.automation"><span class="typeNameLink">GraphikosAutomationApplication</span></a></li>
<li class="circle">graphikos.automation.controller.<a href="graphikos/automation/controller/MainController.html" title="class in graphikos.automation.controller"><span class="typeNameLink">MainController</span></a></li>
<li class="circle">graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model"><span class="typeNameLink">User</span></a></li>
<li class="circle">graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service"><span class="typeNameLink">UserService</span></a></li>
<li class="circle">org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter (implements org.springframework.security.config.annotation.web.WebSecurityConfigurer&lt;T&gt;)
<ul>
<li class="circle">graphikos.automation.config.<a href="graphikos/automation/config/SecurityConfig.html" title="class in graphikos.automation.config"><span class="typeNameLink">SecurityConfig</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">org.springframework.data.repository.query.QueryByExampleExecutor&lt;T&gt;
<ul>
<li class="circle">org.springframework.data.jpa.repository.JpaRepository&lt;T,&#8203;ID&gt; (also extends org.springframework.data.repository.PagingAndSortingRepository&lt;T,&#8203;ID&gt;)
<ul>
<li class="circle">graphikos.automation.repository.<a href="graphikos/automation/repository/BuildRequestRepository.html" title="interface in graphikos.automation.repository"><span class="typeNameLink">BuildRequestRepository</span></a></li>
<li class="circle">graphikos.automation.repository.<a href="graphikos/automation/repository/UserRepository.html" title="interface in graphikos.automation.repository"><span class="typeNameLink">UserRepository</span></a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.springframework.data.repository.Repository&lt;T,&#8203;ID&gt;
<ul>
<li class="circle">org.springframework.data.repository.CrudRepository&lt;T,&#8203;ID&gt;
<ul>
<li class="circle">org.springframework.data.repository.PagingAndSortingRepository&lt;T,&#8203;ID&gt;
<ul>
<li class="circle">org.springframework.data.jpa.repository.JpaRepository&lt;T,&#8203;ID&gt; (also extends org.springframework.data.repository.query.QueryByExampleExecutor&lt;T&gt;)
<ul>
<li class="circle">graphikos.automation.repository.<a href="graphikos/automation/repository/BuildRequestRepository.html" title="interface in graphikos.automation.repository"><span class="typeNameLink">BuildRequestRepository</span></a></li>
<li class="circle">graphikos.automation.repository.<a href="graphikos/automation/repository/UserRepository.html" title="interface in graphikos.automation.repository"><span class="typeNameLink">UserRepository</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li class="circle">java.lang.Object
<ul>
<li class="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li class="circle">graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model"><span class="typeNameLink">BuildRequest.BuildStatus</span></a></li>
<li class="circle">graphikos.automation.model.<a href="graphikos/automation/model/User.Role.html" title="enum in graphikos.automation.model"><span class="typeNameLink">User.Role</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</div>
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a id="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a id="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</nav>
</footer>
</body>
</html>
