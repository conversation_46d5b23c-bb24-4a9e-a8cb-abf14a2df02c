<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>BuildService (GQA 1.0-SNAPSHOT API)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BuildService (GQA 1.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../";
var useModuleDirectories = true;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a id="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a id="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
</nav>
</header>
<!-- ======== START OF CLASS DATA ======== -->
<main role="main">
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">graphikos.automation.service</a></div>
<h2 title="Class BuildService" class="title">Class BuildService</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>graphikos.automation.service.BuildService</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>@Service
public class <span class="typeNameLabel">BuildService</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#%3Cinit%3E()">BuildService</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
</section>
<!-- ========== METHOD SUMMARY =========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#countByStatus(graphikos.automation.model.BuildRequest.BuildStatus)">countByStatus</a></span>&#8203;(<a href="../model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#countByUser(graphikos.automation.model.User)">countByUser</a></span>&#8203;(<a href="../model/User.html" title="class in graphikos.automation.model">User</a>&nbsp;user)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#createBuildRequest(graphikos.automation.model.BuildRequest,graphikos.automation.model.User)">createBuildRequest</a></span>&#8203;(<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;buildRequest,
                  <a href="../model/User.html" title="class in graphikos.automation.model">User</a>&nbsp;user)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findAllBuilds()">findAllBuilds</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findBuildsByStatus(graphikos.automation.model.BuildRequest.BuildStatus)">findBuildsByStatus</a></span>&#8203;(<a href="../model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findBuildsByUser(graphikos.automation.model.User)">findBuildsByUser</a></span>&#8203;(<a href="../model/User.html" title="class in graphikos.automation.model">User</a>&nbsp;user)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.Optional&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findById(java.lang.Long)">findById</a></span>&#8203;(java.lang.Long&nbsp;id)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#startAutomation(java.lang.Long,java.lang.String)">startAutomation</a></span>&#8203;(java.lang.Long&nbsp;id,
               java.lang.String&nbsp;reportUrl)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#updateBuildRequest(graphikos.automation.model.BuildRequest)">updateBuildRequest</a></span>&#8203;(<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;buildRequest)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#updateBuildStatus(java.lang.Long,graphikos.automation.model.BuildRequest.BuildStatus)">updateBuildStatus</a></span>&#8203;(java.lang.Long&nbsp;id,
                 <a href="../model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#updatePrechecks(java.lang.Long,java.lang.String,java.lang.String,java.lang.String,java.lang.String)">updatePrechecks</a></span>&#8203;(java.lang.Long&nbsp;id,
               java.lang.String&nbsp;buildDiff,
               java.lang.String&nbsp;changedFiles,
               java.lang.String&nbsp;zdcmFiles,
               java.lang.String&nbsp;migrationFiles)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a id="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</section>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a id="&lt;init&gt;()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BuildService</h4>
<pre>public&nbsp;BuildService()</pre>
</li>
</ul>
</li>
</ul>
</section>
<!-- ============ METHOD DETAIL ========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a id="createBuildRequest(graphikos.automation.model.BuildRequest,graphikos.automation.model.User)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBuildRequest</h4>
<pre class="methodSignature">public&nbsp;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;createBuildRequest&#8203;(<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;buildRequest,
                                       <a href="../model/User.html" title="class in graphikos.automation.model">User</a>&nbsp;user)</pre>
</li>
</ul>
<a id="findById(java.lang.Long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findById</h4>
<pre class="methodSignature">public&nbsp;java.util.Optional&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;&nbsp;findById&#8203;(java.lang.Long&nbsp;id)</pre>
</li>
</ul>
<a id="findAllBuilds()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findAllBuilds</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;&nbsp;findAllBuilds()</pre>
</li>
</ul>
<a id="findBuildsByUser(graphikos.automation.model.User)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findBuildsByUser</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;&nbsp;findBuildsByUser&#8203;(<a href="../model/User.html" title="class in graphikos.automation.model">User</a>&nbsp;user)</pre>
</li>
</ul>
<a id="findBuildsByStatus(graphikos.automation.model.BuildRequest.BuildStatus)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findBuildsByStatus</h4>
<pre class="methodSignature">public&nbsp;java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;&nbsp;findBuildsByStatus&#8203;(<a href="../model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</pre>
</li>
</ul>
<a id="updateBuildRequest(graphikos.automation.model.BuildRequest)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateBuildRequest</h4>
<pre class="methodSignature">public&nbsp;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;updateBuildRequest&#8203;(<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;buildRequest)</pre>
</li>
</ul>
<a id="updateBuildStatus(java.lang.Long,graphikos.automation.model.BuildRequest.BuildStatus)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateBuildStatus</h4>
<pre class="methodSignature">public&nbsp;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;updateBuildStatus&#8203;(java.lang.Long&nbsp;id,
                                      <a href="../model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</pre>
</li>
</ul>
<a id="startAutomation(java.lang.Long,java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startAutomation</h4>
<pre class="methodSignature">public&nbsp;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;startAutomation&#8203;(java.lang.Long&nbsp;id,
                                    java.lang.String&nbsp;reportUrl)</pre>
</li>
</ul>
<a id="updatePrechecks(java.lang.Long,java.lang.String,java.lang.String,java.lang.String,java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updatePrechecks</h4>
<pre class="methodSignature">public&nbsp;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;updatePrechecks&#8203;(java.lang.Long&nbsp;id,
                                    java.lang.String&nbsp;buildDiff,
                                    java.lang.String&nbsp;changedFiles,
                                    java.lang.String&nbsp;zdcmFiles,
                                    java.lang.String&nbsp;migrationFiles)</pre>
</li>
</ul>
<a id="countByStatus(graphikos.automation.model.BuildRequest.BuildStatus)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>countByStatus</h4>
<pre class="methodSignature">public&nbsp;long&nbsp;countByStatus&#8203;(<a href="../model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</pre>
</li>
</ul>
<a id="countByUser(graphikos.automation.model.User)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>countByUser</h4>
<pre class="methodSignature">public&nbsp;long&nbsp;countByUser&#8203;(<a href="../model/User.html" title="class in graphikos.automation.model">User</a>&nbsp;user)</pre>
</li>
</ul>
</li>
</ul>
</section>
</li>
</ul>
</div>
</div>
</main>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a id="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a id="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</nav>
</footer>
</body>
</html>
