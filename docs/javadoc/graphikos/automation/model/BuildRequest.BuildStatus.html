<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>BuildRequest.BuildStatus (GQA 1.0-SNAPSHOT API)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BuildRequest.BuildStatus (GQA 1.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":9,"i1":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../";
var useModuleDirectories = true;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a id="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a id="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
</nav>
</header>
<!-- ======== START OF CLASS DATA ======== -->
<main role="main">
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">graphikos.automation.model</a></div>
<h2 title="Enum BuildRequest.BuildStatus" class="title">Enum BuildRequest.BuildStatus</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&gt;</li>
<li>
<ul class="inheritance">
<li>graphikos.automation.model.BuildRequest.BuildStatus</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><code>java.io.Serializable</code>, <code>java.lang.Comparable&lt;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&gt;</code></dd>
</dl>
<dl>
<dt>Enclosing class:</dt>
<dd><a href="BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dd>
</dl>
<hr>
<pre>public static enum <span class="typeNameLabel">BuildRequest.BuildStatus</span>
extends java.lang.Enum&lt;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&gt;</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum Constant</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#AUTOMATION_RUNNING">AUTOMATION_RUNNING</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#COMPLETED">COMPLETED</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#FAILED">FAILED</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#IN_PROGRESS">IN_PROGRESS</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#LIVE_BUILD_UPDATED">LIVE_BUILD_UPDATED</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#PRE_BUILD_UPDATED">PRE_BUILD_UPDATED</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<th class="colFirst" scope="row"><code><span class="memberNameLink"><a href="#SUBMITTED">SUBMITTED</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
</section>
<!-- ========== METHOD SUMMARY =========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#valueOf(java.lang.String)">valueOf</a></span>&#8203;(java.lang.String&nbsp;name)</code></th>
<td class="colLast">
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>[]</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#values()">values</a></span>()</code></th>
<td class="colLast">
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a id="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Enum</h3>
<code>clone, compareTo, equals, finalize, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a id="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</section>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a id="SUBMITTED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUBMITTED</h4>
<pre>public static final&nbsp;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a> SUBMITTED</pre>
</li>
</ul>
<a id="IN_PROGRESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IN_PROGRESS</h4>
<pre>public static final&nbsp;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a> IN_PROGRESS</pre>
</li>
</ul>
<a id="AUTOMATION_RUNNING">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AUTOMATION_RUNNING</h4>
<pre>public static final&nbsp;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a> AUTOMATION_RUNNING</pre>
</li>
</ul>
<a id="PRE_BUILD_UPDATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRE_BUILD_UPDATED</h4>
<pre>public static final&nbsp;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a> PRE_BUILD_UPDATED</pre>
</li>
</ul>
<a id="LIVE_BUILD_UPDATED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LIVE_BUILD_UPDATED</h4>
<pre>public static final&nbsp;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a> LIVE_BUILD_UPDATED</pre>
</li>
</ul>
<a id="COMPLETED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COMPLETED</h4>
<pre>public static final&nbsp;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a> COMPLETED</pre>
</li>
</ul>
<a id="FAILED">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FAILED</h4>
<pre>public static final&nbsp;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a> FAILED</pre>
</li>
</ul>
</li>
</ul>
</section>
<!-- ============ METHOD DETAIL ========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a id="values()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre class="methodSignature">public static&nbsp;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared. This method may be used to iterate
over the constants as follows:
<pre>
for (BuildRequest.BuildStatus c : BuildRequest.BuildStatus.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a id="valueOf(java.lang.String)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>valueOf</h4>
<pre class="methodSignature">public static&nbsp;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;valueOf&#8203;(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if this enum type has no constant with the specified name</dd>
<dd><code>java.lang.NullPointerException</code> - if the argument is null</dd>
</dl>
</li>
</ul>
</li>
</ul>
</section>
</li>
</ul>
</div>
</div>
</main>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a id="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a id="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</nav>
</footer>
</body>
</html>
