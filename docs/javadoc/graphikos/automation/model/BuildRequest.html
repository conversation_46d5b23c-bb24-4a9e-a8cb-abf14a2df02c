<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>BuildRequest (GQA 1.0-SNAPSHOT API)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BuildRequest (GQA 1.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../";
var useModuleDirectories = true;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a id="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a id="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
</nav>
</header>
<!-- ======== START OF CLASS DATA ======== -->
<main role="main">
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">graphikos.automation.model</a></div>
<h2 title="Class BuildRequest" class="title">Class BuildRequest</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>graphikos.automation.model.BuildRequest</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>@Entity
public class <span class="typeNameLabel">BuildRequest</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></span></code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
</section>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#%3Cinit%3E()">BuildRequest</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
</section>
<!-- ========== METHOD SUMMARY =========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAutomationConfig()">getAutomationConfig</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getAutomationReportUrl()">getAutomationReportUrl</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuildDiffContent()">getBuildDiffContent</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuildsetup()">getBuildsetup</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getChangedFilesContent()">getChangedFilesContent</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getConversion()">getConversion</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.time.LocalDateTime</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCreatedAt()">getCreatedAt</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="User.html" title="class in graphikos.automation.model">User</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getCreatedBy()">getCreatedBy</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGraphikosi18n()">getGraphikosi18n</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getGraphikosmedia()">getGraphikosmedia</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.Long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getId()">getId</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getImageconversion()">getImageconversion</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getManualTestcaseSheet()">getManualTestcaseSheet</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getMigrationFilesContent()">getMigrationFilesContent</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getPictures()">getPictures</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getReleaseType()">getReleaseType</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getSanityUrl()">getSanityUrl</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShapeframework()">getShapeframework</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowlistingdialog()">getShowlistingdialog</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowoffline()">getShowoffline</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowrenderingframework()">getShowrenderingframework</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowrightpanel()">getShowrightpanel</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowserver()">getShowserver</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowslideshowviews()">getShowslideshowviews</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getShowui()">getShowui</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getStatus()">getStatus</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.time.LocalDateTime</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getUpdatedAt()">getUpdatedAt</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getZdcmFilesContent()">getZdcmFilesContent</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getZohoshow()">getZohoshow</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getZohoshowinput()">getZohoshowinput</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isBuildDiffChecked()">isBuildDiffChecked</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isBuildUpdatedToLive()">isBuildUpdatedToLive</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isChangedFilesChecked()">isChangedFilesChecked</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isConversionAutoBuildUpdate()">isConversionAutoBuildUpdate</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isEnableAutobuildUpdate()">isEnableAutobuildUpdate</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isFinalSanityCompleted()">isFinalSanityCompleted</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isImageconversionAutoBuildUpdate()">isImageconversionAutoBuildUpdate</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isManualTestcaseConfirmed()">isManualTestcaseConfirmed</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isMigrationFilesChecked()">isMigrationFilesChecked</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isPicturesAutoBuildUpdate()">isPicturesAutoBuildUpdate</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isPreAutomation()">isPreAutomation</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isPreSanity()">isPreSanity</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isReportReceived()">isReportReceived</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#isZdcmFilesChecked()">isZdcmFilesChecked</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setAutomationConfig(java.lang.String)">setAutomationConfig</a></span>&#8203;(java.lang.String&nbsp;automationConfig)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setAutomationReportUrl(java.lang.String)">setAutomationReportUrl</a></span>&#8203;(java.lang.String&nbsp;automationReportUrl)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBuildDiffChecked(boolean)">setBuildDiffChecked</a></span>&#8203;(boolean&nbsp;buildDiffChecked)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBuildDiffContent(java.lang.String)">setBuildDiffContent</a></span>&#8203;(java.lang.String&nbsp;buildDiffContent)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBuildsetup(java.lang.String)">setBuildsetup</a></span>&#8203;(java.lang.String&nbsp;buildsetup)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setBuildUpdatedToLive(boolean)">setBuildUpdatedToLive</a></span>&#8203;(boolean&nbsp;buildUpdatedToLive)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setChangedFilesChecked(boolean)">setChangedFilesChecked</a></span>&#8203;(boolean&nbsp;changedFilesChecked)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setChangedFilesContent(java.lang.String)">setChangedFilesContent</a></span>&#8203;(java.lang.String&nbsp;changedFilesContent)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConversion(java.lang.String)">setConversion</a></span>&#8203;(java.lang.String&nbsp;conversion)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setConversionAutoBuildUpdate(boolean)">setConversionAutoBuildUpdate</a></span>&#8203;(boolean&nbsp;conversionAutoBuildUpdate)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCreatedAt(java.time.LocalDateTime)">setCreatedAt</a></span>&#8203;(java.time.LocalDateTime&nbsp;createdAt)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setCreatedBy(graphikos.automation.model.User)">setCreatedBy</a></span>&#8203;(<a href="User.html" title="class in graphikos.automation.model">User</a>&nbsp;createdBy)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setEnableAutobuildUpdate(boolean)">setEnableAutobuildUpdate</a></span>&#8203;(boolean&nbsp;enableAutobuildUpdate)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setFinalSanityCompleted(boolean)">setFinalSanityCompleted</a></span>&#8203;(boolean&nbsp;finalSanityCompleted)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setGraphikosi18n(java.lang.String)">setGraphikosi18n</a></span>&#8203;(java.lang.String&nbsp;graphikosi18n)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setGraphikosmedia(java.lang.String)">setGraphikosmedia</a></span>&#8203;(java.lang.String&nbsp;graphikosmedia)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setId(java.lang.Long)">setId</a></span>&#8203;(java.lang.Long&nbsp;id)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setImageconversion(java.lang.String)">setImageconversion</a></span>&#8203;(java.lang.String&nbsp;imageconversion)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setImageconversionAutoBuildUpdate(boolean)">setImageconversionAutoBuildUpdate</a></span>&#8203;(boolean&nbsp;imageconversionAutoBuildUpdate)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setManualTestcaseConfirmed(boolean)">setManualTestcaseConfirmed</a></span>&#8203;(boolean&nbsp;manualTestcaseConfirmed)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setManualTestcaseSheet(java.lang.String)">setManualTestcaseSheet</a></span>&#8203;(java.lang.String&nbsp;manualTestcaseSheet)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMigrationFilesChecked(boolean)">setMigrationFilesChecked</a></span>&#8203;(boolean&nbsp;migrationFilesChecked)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setMigrationFilesContent(java.lang.String)">setMigrationFilesContent</a></span>&#8203;(java.lang.String&nbsp;migrationFilesContent)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPictures(java.lang.String)">setPictures</a></span>&#8203;(java.lang.String&nbsp;pictures)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPicturesAutoBuildUpdate(boolean)">setPicturesAutoBuildUpdate</a></span>&#8203;(boolean&nbsp;picturesAutoBuildUpdate)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPreAutomation(boolean)">setPreAutomation</a></span>&#8203;(boolean&nbsp;preAutomation)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setPreSanity(boolean)">setPreSanity</a></span>&#8203;(boolean&nbsp;preSanity)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setReleaseType(java.lang.String)">setReleaseType</a></span>&#8203;(java.lang.String&nbsp;releaseType)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setReportReceived(boolean)">setReportReceived</a></span>&#8203;(boolean&nbsp;reportReceived)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setSanityUrl(java.lang.String)">setSanityUrl</a></span>&#8203;(java.lang.String&nbsp;sanityUrl)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShapeframework(java.lang.String)">setShapeframework</a></span>&#8203;(java.lang.String&nbsp;shapeframework)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowlistingdialog(java.lang.String)">setShowlistingdialog</a></span>&#8203;(java.lang.String&nbsp;showlistingdialog)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowoffline(java.lang.String)">setShowoffline</a></span>&#8203;(java.lang.String&nbsp;showoffline)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowrenderingframework(java.lang.String)">setShowrenderingframework</a></span>&#8203;(java.lang.String&nbsp;showrenderingframework)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowrightpanel(java.lang.String)">setShowrightpanel</a></span>&#8203;(java.lang.String&nbsp;showrightpanel)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowserver(java.lang.String)">setShowserver</a></span>&#8203;(java.lang.String&nbsp;showserver)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowslideshowviews(java.lang.String)">setShowslideshowviews</a></span>&#8203;(java.lang.String&nbsp;showslideshowviews)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setShowui(java.lang.String)">setShowui</a></span>&#8203;(java.lang.String&nbsp;showui)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setStatus(graphikos.automation.model.BuildRequest.BuildStatus)">setStatus</a></span>&#8203;(<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setUpdatedAt(java.time.LocalDateTime)">setUpdatedAt</a></span>&#8203;(java.time.LocalDateTime&nbsp;updatedAt)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setZdcmFilesChecked(boolean)">setZdcmFilesChecked</a></span>&#8203;(boolean&nbsp;zdcmFilesChecked)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setZdcmFilesContent(java.lang.String)">setZdcmFilesContent</a></span>&#8203;(java.lang.String&nbsp;zdcmFilesContent)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setZohoshow(java.lang.String)">setZohoshow</a></span>&#8203;(java.lang.String&nbsp;zohoshow)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>void</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#setZohoshowinput(java.lang.String)">setZohoshowinput</a></span>&#8203;(java.lang.String&nbsp;zohoshowinput)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a id="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</section>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a id="&lt;init&gt;()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BuildRequest</h4>
<pre>public&nbsp;BuildRequest()</pre>
</li>
</ul>
</li>
</ul>
</section>
<!-- ============ METHOD DETAIL ========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a id="getId()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getId</h4>
<pre class="methodSignature">public&nbsp;java.lang.Long&nbsp;getId()</pre>
</li>
</ul>
<a id="setId(java.lang.Long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setId</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setId&#8203;(java.lang.Long&nbsp;id)</pre>
</li>
</ul>
<a id="getReleaseType()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReleaseType</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getReleaseType()</pre>
</li>
</ul>
<a id="setReleaseType(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReleaseType</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setReleaseType&#8203;(java.lang.String&nbsp;releaseType)</pre>
</li>
</ul>
<a id="getBuildsetup()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuildsetup</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getBuildsetup()</pre>
</li>
</ul>
<a id="setBuildsetup(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuildsetup</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setBuildsetup&#8203;(java.lang.String&nbsp;buildsetup)</pre>
</li>
</ul>
<a id="getZohoshowinput()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZohoshowinput</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getZohoshowinput()</pre>
</li>
</ul>
<a id="setZohoshowinput(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZohoshowinput</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setZohoshowinput&#8203;(java.lang.String&nbsp;zohoshowinput)</pre>
</li>
</ul>
<a id="getShapeframework()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShapeframework</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getShapeframework()</pre>
</li>
</ul>
<a id="setShapeframework(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShapeframework</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setShapeframework&#8203;(java.lang.String&nbsp;shapeframework)</pre>
</li>
</ul>
<a id="getGraphikosmedia()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGraphikosmedia</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getGraphikosmedia()</pre>
</li>
</ul>
<a id="setGraphikosmedia(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGraphikosmedia</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setGraphikosmedia&#8203;(java.lang.String&nbsp;graphikosmedia)</pre>
</li>
</ul>
<a id="getShowrenderingframework()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowrenderingframework</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getShowrenderingframework()</pre>
</li>
</ul>
<a id="setShowrenderingframework(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowrenderingframework</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setShowrenderingframework&#8203;(java.lang.String&nbsp;showrenderingframework)</pre>
</li>
</ul>
<a id="getGraphikosi18n()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGraphikosi18n</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getGraphikosi18n()</pre>
</li>
</ul>
<a id="setGraphikosi18n(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGraphikosi18n</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setGraphikosi18n&#8203;(java.lang.String&nbsp;graphikosi18n)</pre>
</li>
</ul>
<a id="getShowlistingdialog()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowlistingdialog</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getShowlistingdialog()</pre>
</li>
</ul>
<a id="setShowlistingdialog(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowlistingdialog</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setShowlistingdialog&#8203;(java.lang.String&nbsp;showlistingdialog)</pre>
</li>
</ul>
<a id="getShowrightpanel()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowrightpanel</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getShowrightpanel()</pre>
</li>
</ul>
<a id="setShowrightpanel(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowrightpanel</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setShowrightpanel&#8203;(java.lang.String&nbsp;showrightpanel)</pre>
</li>
</ul>
<a id="getShowslideshowviews()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowslideshowviews</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getShowslideshowviews()</pre>
</li>
</ul>
<a id="setShowslideshowviews(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowslideshowviews</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setShowslideshowviews&#8203;(java.lang.String&nbsp;showslideshowviews)</pre>
</li>
</ul>
<a id="getShowui()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowui</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getShowui()</pre>
</li>
</ul>
<a id="setShowui(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowui</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setShowui&#8203;(java.lang.String&nbsp;showui)</pre>
</li>
</ul>
<a id="getShowoffline()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowoffline</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getShowoffline()</pre>
</li>
</ul>
<a id="setShowoffline(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowoffline</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setShowoffline&#8203;(java.lang.String&nbsp;showoffline)</pre>
</li>
</ul>
<a id="getShowserver()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShowserver</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getShowserver()</pre>
</li>
</ul>
<a id="setShowserver(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShowserver</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setShowserver&#8203;(java.lang.String&nbsp;showserver)</pre>
</li>
</ul>
<a id="isEnableAutobuildUpdate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableAutobuildUpdate</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isEnableAutobuildUpdate()</pre>
</li>
</ul>
<a id="setEnableAutobuildUpdate(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnableAutobuildUpdate</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setEnableAutobuildUpdate&#8203;(boolean&nbsp;enableAutobuildUpdate)</pre>
</li>
</ul>
<a id="getZohoshow()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZohoshow</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getZohoshow()</pre>
</li>
</ul>
<a id="setZohoshow(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZohoshow</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setZohoshow&#8203;(java.lang.String&nbsp;zohoshow)</pre>
</li>
</ul>
<a id="getConversion()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConversion</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getConversion()</pre>
</li>
</ul>
<a id="setConversion(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConversion</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConversion&#8203;(java.lang.String&nbsp;conversion)</pre>
</li>
</ul>
<a id="isConversionAutoBuildUpdate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isConversionAutoBuildUpdate</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isConversionAutoBuildUpdate()</pre>
</li>
</ul>
<a id="setConversionAutoBuildUpdate(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setConversionAutoBuildUpdate</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setConversionAutoBuildUpdate&#8203;(boolean&nbsp;conversionAutoBuildUpdate)</pre>
</li>
</ul>
<a id="getPictures()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPictures</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getPictures()</pre>
</li>
</ul>
<a id="setPictures(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPictures</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPictures&#8203;(java.lang.String&nbsp;pictures)</pre>
</li>
</ul>
<a id="isPicturesAutoBuildUpdate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPicturesAutoBuildUpdate</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isPicturesAutoBuildUpdate()</pre>
</li>
</ul>
<a id="setPicturesAutoBuildUpdate(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPicturesAutoBuildUpdate</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPicturesAutoBuildUpdate&#8203;(boolean&nbsp;picturesAutoBuildUpdate)</pre>
</li>
</ul>
<a id="getImageconversion()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImageconversion</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getImageconversion()</pre>
</li>
</ul>
<a id="setImageconversion(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setImageconversion</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setImageconversion&#8203;(java.lang.String&nbsp;imageconversion)</pre>
</li>
</ul>
<a id="isImageconversionAutoBuildUpdate()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isImageconversionAutoBuildUpdate</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isImageconversionAutoBuildUpdate()</pre>
</li>
</ul>
<a id="setImageconversionAutoBuildUpdate(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setImageconversionAutoBuildUpdate</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setImageconversionAutoBuildUpdate&#8203;(boolean&nbsp;imageconversionAutoBuildUpdate)</pre>
</li>
</ul>
<a id="getStatus()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatus</h4>
<pre class="methodSignature">public&nbsp;<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;getStatus()</pre>
</li>
</ul>
<a id="setStatus(graphikos.automation.model.BuildRequest.BuildStatus)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStatus</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setStatus&#8203;(<a href="BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</pre>
</li>
</ul>
<a id="getCreatedBy()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreatedBy</h4>
<pre class="methodSignature">public&nbsp;<a href="User.html" title="class in graphikos.automation.model">User</a>&nbsp;getCreatedBy()</pre>
</li>
</ul>
<a id="setCreatedBy(graphikos.automation.model.User)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreatedBy</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCreatedBy&#8203;(<a href="User.html" title="class in graphikos.automation.model">User</a>&nbsp;createdBy)</pre>
</li>
</ul>
<a id="getCreatedAt()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreatedAt</h4>
<pre class="methodSignature">public&nbsp;java.time.LocalDateTime&nbsp;getCreatedAt()</pre>
</li>
</ul>
<a id="setCreatedAt(java.time.LocalDateTime)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreatedAt</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setCreatedAt&#8203;(java.time.LocalDateTime&nbsp;createdAt)</pre>
</li>
</ul>
<a id="getUpdatedAt()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpdatedAt</h4>
<pre class="methodSignature">public&nbsp;java.time.LocalDateTime&nbsp;getUpdatedAt()</pre>
</li>
</ul>
<a id="setUpdatedAt(java.time.LocalDateTime)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUpdatedAt</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setUpdatedAt&#8203;(java.time.LocalDateTime&nbsp;updatedAt)</pre>
</li>
</ul>
<a id="getAutomationConfig()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutomationConfig</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getAutomationConfig()</pre>
</li>
</ul>
<a id="setAutomationConfig(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutomationConfig</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setAutomationConfig&#8203;(java.lang.String&nbsp;automationConfig)</pre>
</li>
</ul>
<a id="getAutomationReportUrl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutomationReportUrl</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getAutomationReportUrl()</pre>
</li>
</ul>
<a id="setAutomationReportUrl(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutomationReportUrl</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setAutomationReportUrl&#8203;(java.lang.String&nbsp;automationReportUrl)</pre>
</li>
</ul>
<a id="getManualTestcaseSheet()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManualTestcaseSheet</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getManualTestcaseSheet()</pre>
</li>
</ul>
<a id="setManualTestcaseSheet(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManualTestcaseSheet</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setManualTestcaseSheet&#8203;(java.lang.String&nbsp;manualTestcaseSheet)</pre>
</li>
</ul>
<a id="isReportReceived()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isReportReceived</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isReportReceived()</pre>
</li>
</ul>
<a id="setReportReceived(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReportReceived</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setReportReceived&#8203;(boolean&nbsp;reportReceived)</pre>
</li>
</ul>
<a id="isManualTestcaseConfirmed()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isManualTestcaseConfirmed</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isManualTestcaseConfirmed()</pre>
</li>
</ul>
<a id="setManualTestcaseConfirmed(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManualTestcaseConfirmed</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setManualTestcaseConfirmed&#8203;(boolean&nbsp;manualTestcaseConfirmed)</pre>
</li>
</ul>
<a id="isPreSanity()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPreSanity</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isPreSanity()</pre>
</li>
</ul>
<a id="setPreSanity(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreSanity</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPreSanity&#8203;(boolean&nbsp;preSanity)</pre>
</li>
</ul>
<a id="isPreAutomation()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPreAutomation</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isPreAutomation()</pre>
</li>
</ul>
<a id="setPreAutomation(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreAutomation</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setPreAutomation&#8203;(boolean&nbsp;preAutomation)</pre>
</li>
</ul>
<a id="isBuildDiffChecked()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBuildDiffChecked</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isBuildDiffChecked()</pre>
</li>
</ul>
<a id="setBuildDiffChecked(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuildDiffChecked</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setBuildDiffChecked&#8203;(boolean&nbsp;buildDiffChecked)</pre>
</li>
</ul>
<a id="isChangedFilesChecked()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isChangedFilesChecked</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isChangedFilesChecked()</pre>
</li>
</ul>
<a id="setChangedFilesChecked(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setChangedFilesChecked</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setChangedFilesChecked&#8203;(boolean&nbsp;changedFilesChecked)</pre>
</li>
</ul>
<a id="isZdcmFilesChecked()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isZdcmFilesChecked</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isZdcmFilesChecked()</pre>
</li>
</ul>
<a id="setZdcmFilesChecked(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZdcmFilesChecked</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setZdcmFilesChecked&#8203;(boolean&nbsp;zdcmFilesChecked)</pre>
</li>
</ul>
<a id="isMigrationFilesChecked()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMigrationFilesChecked</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isMigrationFilesChecked()</pre>
</li>
</ul>
<a id="setMigrationFilesChecked(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMigrationFilesChecked</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMigrationFilesChecked&#8203;(boolean&nbsp;migrationFilesChecked)</pre>
</li>
</ul>
<a id="getBuildDiffContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuildDiffContent</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getBuildDiffContent()</pre>
</li>
</ul>
<a id="setBuildDiffContent(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuildDiffContent</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setBuildDiffContent&#8203;(java.lang.String&nbsp;buildDiffContent)</pre>
</li>
</ul>
<a id="getChangedFilesContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChangedFilesContent</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getChangedFilesContent()</pre>
</li>
</ul>
<a id="setChangedFilesContent(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setChangedFilesContent</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setChangedFilesContent&#8203;(java.lang.String&nbsp;changedFilesContent)</pre>
</li>
</ul>
<a id="getZdcmFilesContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZdcmFilesContent</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getZdcmFilesContent()</pre>
</li>
</ul>
<a id="setZdcmFilesContent(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZdcmFilesContent</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setZdcmFilesContent&#8203;(java.lang.String&nbsp;zdcmFilesContent)</pre>
</li>
</ul>
<a id="getMigrationFilesContent()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMigrationFilesContent</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getMigrationFilesContent()</pre>
</li>
</ul>
<a id="setMigrationFilesContent(java.lang.String)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMigrationFilesContent</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setMigrationFilesContent&#8203;(java.lang.String&nbsp;migrationFilesContent)</pre>
</li>
</ul>
<a id="isBuildUpdatedToLive()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBuildUpdatedToLive</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isBuildUpdatedToLive()</pre>
</li>
</ul>
<a id="setBuildUpdatedToLive(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBuildUpdatedToLive</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setBuildUpdatedToLive&#8203;(boolean&nbsp;buildUpdatedToLive)</pre>
</li>
</ul>
<a id="isFinalSanityCompleted()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFinalSanityCompleted</h4>
<pre class="methodSignature">public&nbsp;boolean&nbsp;isFinalSanityCompleted()</pre>
</li>
</ul>
<a id="setFinalSanityCompleted(boolean)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinalSanityCompleted</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setFinalSanityCompleted&#8203;(boolean&nbsp;finalSanityCompleted)</pre>
</li>
</ul>
<a id="getSanityUrl()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSanityUrl</h4>
<pre class="methodSignature">public&nbsp;java.lang.String&nbsp;getSanityUrl()</pre>
</li>
</ul>
<a id="setSanityUrl(java.lang.String)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSanityUrl</h4>
<pre class="methodSignature">public&nbsp;void&nbsp;setSanityUrl&#8203;(java.lang.String&nbsp;sanityUrl)</pre>
</li>
</ul>
</li>
</ul>
</section>
</li>
</ul>
</div>
</div>
</main>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a id="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a id="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</nav>
</footer>
</body>
</html>
