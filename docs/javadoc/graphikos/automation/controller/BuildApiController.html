<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>BuildApiController (GQA 1.0-SNAPSHOT API)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BuildApiController (GQA 1.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../";
var useModuleDirectories = true;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a id="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a id="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
</nav>
</header>
<!-- ======== START OF CLASS DATA ======== -->
<main role="main">
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">graphikos.automation.controller</a></div>
<h2 title="Class BuildApiController" class="title">Class BuildApiController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>graphikos.automation.controller.BuildApiController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<pre>@RestController
@RequestMapping("/api/builds")
public class <span class="typeNameLabel">BuildApiController</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Constructor</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr class="altColor">
<th class="colConstructorName" scope="row"><code><span class="memberNameLink"><a href="#%3Cinit%3E()">BuildApiController</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
</li>
</ul>
</section>
<!-- ========== METHOD SUMMARY =========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#confirmReport(java.lang.Long)">confirmReport</a></span>&#8203;(java.lang.Long&nbsp;buildId)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>org.springframework.http.ResponseEntity&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuild(java.lang.Long)">getBuild</a></span>&#8203;(java.lang.Long&nbsp;id)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#getBuildJson(java.lang.Long)">getBuildJson</a></span>&#8203;(java.lang.Long&nbsp;id)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#startAutomation(java.lang.Long)">startAutomation</a></span>&#8203;(java.lang.Long&nbsp;id)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#submitBuild(graphikos.automation.model.BuildRequest)">submitBuild</a></span>&#8203;(<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;buildRequest)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#updateBuildStatus(java.lang.Long,java.util.Map)">updateBuildStatus</a></span>&#8203;(java.lang.Long&nbsp;id,
                 java.util.Map&lt;java.lang.String,&#8203;java.lang.String&gt;&nbsp;request)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#updateCheckboxes(java.lang.Long,java.util.Map)">updateCheckboxes</a></span>&#8203;(java.lang.Long&nbsp;id,
                java.util.Map&lt;java.lang.String,&#8203;java.lang.Boolean&gt;&nbsp;checkboxes)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#updatePrechecks(java.lang.Long,java.util.Map)">updatePrechecks</a></span>&#8203;(java.lang.Long&nbsp;id,
               java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;request)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a id="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</section>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a id="&lt;init&gt;()">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BuildApiController</h4>
<pre>public&nbsp;BuildApiController()</pre>
</li>
</ul>
</li>
</ul>
</section>
<!-- ============ METHOD DETAIL ========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a id="submitBuild(graphikos.automation.model.BuildRequest)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>submitBuild</h4>
<pre class="methodSignature">@PostMapping("/submit")
public&nbsp;org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;&nbsp;submitBuild&#8203;(@RequestBody
                                                                                                                   <a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&nbsp;buildRequest)</pre>
</li>
</ul>
<a id="startAutomation(java.lang.Long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startAutomation</h4>
<pre class="methodSignature">@PostMapping("/{id}/start-automation")
public&nbsp;org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;&nbsp;startAutomation&#8203;(@PathVariable
                                                                                                                       java.lang.Long&nbsp;id)</pre>
</li>
</ul>
<a id="updateBuildStatus(java.lang.Long,java.util.Map)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateBuildStatus</h4>
<pre class="methodSignature">@PostMapping("/{id}/update-status")
public&nbsp;org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;&nbsp;updateBuildStatus&#8203;(@PathVariable
                                                                                                                         java.lang.Long&nbsp;id,
                                                                                                                         @RequestBody
                                                                                                                         java.util.Map&lt;java.lang.String,&#8203;java.lang.String&gt;&nbsp;request)</pre>
</li>
</ul>
<a id="updateCheckboxes(java.lang.Long,java.util.Map)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateCheckboxes</h4>
<pre class="methodSignature">@PostMapping("/{id}/update-checkboxes")
public&nbsp;org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;&nbsp;updateCheckboxes&#8203;(@PathVariable
                                                                                                                        java.lang.Long&nbsp;id,
                                                                                                                        @RequestBody
                                                                                                                        java.util.Map&lt;java.lang.String,&#8203;java.lang.Boolean&gt;&nbsp;checkboxes)</pre>
</li>
</ul>
<a id="updatePrechecks(java.lang.Long,java.util.Map)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updatePrechecks</h4>
<pre class="methodSignature">@PostMapping("/{id}/update-prechecks")
@PreAuthorize("hasRole(\'ADMIN\')")
public&nbsp;org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;&nbsp;updatePrechecks&#8203;(@PathVariable
                                                                                                                       java.lang.Long&nbsp;id,
                                                                                                                       @RequestBody
                                                                                                                       java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&nbsp;request)</pre>
</li>
</ul>
<a id="getBuild(java.lang.Long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBuild</h4>
<pre class="methodSignature">@GetMapping("/{id}")
public&nbsp;org.springframework.http.ResponseEntity&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;&nbsp;getBuild&#8203;(@PathVariable
                                                                      java.lang.Long&nbsp;id)</pre>
</li>
</ul>
<a id="confirmReport(java.lang.Long)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmReport</h4>
<pre class="methodSignature">@PostMapping("/{buildId}/confirm-report")
public&nbsp;org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;&nbsp;confirmReport&#8203;(@PathVariable
                                                                                                                     java.lang.Long&nbsp;buildId)</pre>
</li>
</ul>
<a id="getBuildJson(java.lang.Long)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getBuildJson</h4>
<pre class="methodSignature">@GetMapping("/{id}/json")
public&nbsp;org.springframework.http.ResponseEntity&lt;java.util.Map&lt;java.lang.String,&#8203;java.lang.Object&gt;&gt;&nbsp;getBuildJson&#8203;(@PathVariable
                                                                                                                    java.lang.Long&nbsp;id)</pre>
</li>
</ul>
</li>
</ul>
</section>
</li>
</ul>
</div>
</div>
</main>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a id="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a id="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</nav>
</footer>
</body>
</html>
