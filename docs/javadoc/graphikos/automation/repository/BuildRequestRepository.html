<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>BuildRequestRepository (GQA 1.0-SNAPSHOT API)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="../../../jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="../../../jquery/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BuildRequestRepository (GQA 1.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
var data = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
var pathtoroot = "../../../";
var useModuleDirectories = true;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a id="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a id="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
</nav>
</header>
<!-- ======== START OF CLASS DATA ======== -->
<main role="main">
<div class="header">
<div class="subTitle"><span class="packageLabelInType">Package</span>&nbsp;<a href="package-summary.html">graphikos.automation.repository</a></div>
<h2 title="Interface BuildRequestRepository" class="title">Interface BuildRequestRepository</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><code>org.springframework.data.repository.CrudRepository&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>,&#8203;java.lang.Long&gt;</code>, <code>org.springframework.data.jpa.repository.JpaRepository&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>,&#8203;java.lang.Long&gt;</code>, <code>org.springframework.data.repository.PagingAndSortingRepository&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>,&#8203;java.lang.Long&gt;</code>, <code>org.springframework.data.repository.query.QueryByExampleExecutor&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;</code>, <code>org.springframework.data.repository.Repository&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>,&#8203;java.lang.Long&gt;</code></dd>
</dl>
<hr>
<pre>@Repository
public interface <span class="typeNameLabel">BuildRequestRepository</span>
extends org.springframework.data.jpa.repository.JpaRepository&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>,&#8203;java.lang.Long&gt;</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colSecond" scope="col">Method</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#countByCreatedBy(graphikos.automation.model.User)">countByCreatedBy</a></span>&#8203;(<a href="../model/User.html" title="class in graphikos.automation.model">User</a>&nbsp;user)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>long</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#countByStatus(graphikos.automation.model.BuildRequest.BuildStatus)">countByStatus</a></span>&#8203;(<a href="../model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findAllByOrderByCreatedAtDesc()">findAllByOrderByCreatedAtDesc</a></span>()</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findByCreatedByOrderByCreatedAtDesc(graphikos.automation.model.User)">findByCreatedByOrderByCreatedAtDesc</a></span>&#8203;(<a href="../model/User.html" title="class in graphikos.automation.model">User</a>&nbsp;user)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findByStatusOrderByCreatedAtDesc(graphikos.automation.model.BuildRequest.BuildStatus)">findByStatusOrderByCreatedAtDesc</a></span>&#8203;(<a href="../model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;</code></td>
<th class="colSecond" scope="row"><code><span class="memberNameLink"><a href="#findRecentBuilds(java.time.LocalDateTime)">findRecentBuilds</a></span>&#8203;(java.time.LocalDateTime&nbsp;startDate)</code></th>
<td class="colLast">&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a id="methods.inherited.from.class.org.springframework.data.repository.CrudRepository">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.springframework.data.repository.CrudRepository</h3>
<code>count, delete, deleteAll, deleteAll, deleteAllById, deleteById, existsById, findById, save</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a id="methods.inherited.from.class.org.springframework.data.jpa.repository.JpaRepository">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.springframework.data.jpa.repository.JpaRepository</h3>
<code>deleteAllByIdInBatch, deleteAllInBatch, deleteAllInBatch, deleteInBatch, findAll, findAll, findAll, findAll, findAllById, flush, getById, getOne, getReferenceById, saveAll, saveAllAndFlush, saveAndFlush</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a id="methods.inherited.from.class.org.springframework.data.repository.PagingAndSortingRepository">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.springframework.data.repository.PagingAndSortingRepository</h3>
<code>findAll</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a id="methods.inherited.from.class.org.springframework.data.repository.query.QueryByExampleExecutor">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;org.springframework.data.repository.query.QueryByExampleExecutor</h3>
<code>count, exists, findAll, findBy, findOne</code></li>
</ul>
</li>
</ul>
</section>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<section>
<ul class="blockList">
<li class="blockList"><a id="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a id="findByCreatedByOrderByCreatedAtDesc(graphikos.automation.model.User)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findByCreatedByOrderByCreatedAtDesc</h4>
<pre class="methodSignature">java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;&nbsp;findByCreatedByOrderByCreatedAtDesc&#8203;(<a href="../model/User.html" title="class in graphikos.automation.model">User</a>&nbsp;user)</pre>
</li>
</ul>
<a id="findAllByOrderByCreatedAtDesc()">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findAllByOrderByCreatedAtDesc</h4>
<pre class="methodSignature">java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;&nbsp;findAllByOrderByCreatedAtDesc()</pre>
</li>
</ul>
<a id="findByStatusOrderByCreatedAtDesc(graphikos.automation.model.BuildRequest.BuildStatus)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findByStatusOrderByCreatedAtDesc</h4>
<pre class="methodSignature">java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;&nbsp;findByStatusOrderByCreatedAtDesc&#8203;(<a href="../model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</pre>
</li>
</ul>
<a id="findRecentBuilds(java.time.LocalDateTime)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findRecentBuilds</h4>
<pre class="methodSignature">@Query("SELECT br FROM BuildRequest br WHERE br.createdAt &gt;= :startDate ORDER BY br.createdAt DESC")
java.util.List&lt;<a href="../model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a>&gt;&nbsp;findRecentBuilds&#8203;(java.time.LocalDateTime&nbsp;startDate)</pre>
</li>
</ul>
<a id="countByStatus(graphikos.automation.model.BuildRequest.BuildStatus)">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>countByStatus</h4>
<pre class="methodSignature">long&nbsp;countByStatus&#8203;(<a href="../model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a>&nbsp;status)</pre>
</li>
</ul>
<a id="countByCreatedBy(graphikos.automation.model.User)">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>countByCreatedBy</h4>
<pre class="methodSignature">long&nbsp;countByCreatedBy&#8203;(<a href="../model/User.html" title="class in graphikos.automation.model">User</a>&nbsp;user)</pre>
</li>
</ul>
</li>
</ul>
</section>
</li>
</ul>
</div>
</div>
</main>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a id="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a id="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</nav>
</footer>
</body>
</html>
