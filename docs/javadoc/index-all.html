<!DOCTYPE HTML>
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<title>Index (GQA 1.0-SNAPSHOT API)</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="jquery/jszip/dist/jszip.min.js"></script>
<script type="text/javascript" src="jquery/jszip-utils/dist/jszip-utils.min.js"></script>
<!--[if IE]>
<script type="text/javascript" src="jquery/jszip-utils/dist/jszip-utils-ie.min.js"></script>
<![endif]-->
<script type="text/javascript" src="jquery/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="jquery/jquery-ui.min.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Index (GQA 1.0-SNAPSHOT API)";
        }
    }
    catch(err) {
    }
//-->
var pathtoroot = "./";
var useModuleDirectories = true;
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<div class="fixedNav">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a id="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses.html">All&nbsp;Classes</a></li>
</ul>
<ul class="navListSearch">
<li><label for="search">SEARCH:</label>
<input type="text" id="search" value="search" disabled="disabled">
<input type="reset" id="reset" value="reset" disabled="disabled">
</li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a id="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
</div>
<div class="navPadding">&nbsp;</div>
<script type="text/javascript"><!--
$('.navPadding').css('padding-top', $('.fixedNav').css("height"));
//-->
</script>
</nav>
</header>
<main role="main">
<div class="contentContainer"><a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:Q">Q</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<br><a href="allclasses-index.html">All&nbsp;Classes</a>&nbsp;<a href="allpackages-index.html">All&nbsp;Packages</a><a id="I:A">
<!--   -->
</a>
<h2 class="title">A</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.Role.html#ADMIN">ADMIN</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/User.Role.html" title="enum in graphikos.automation.model">User.Role</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/controller/AdminController.html" title="class in graphikos.automation.controller"><span class="typeNameLink">AdminController</span></a> - Class in <a href="graphikos/automation/controller/package-summary.html">graphikos.automation.controller</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.html#%3Cinit%3E()">AdminController()</a></span> - Constructor for class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.html" title="class in graphikos.automation.controller">AdminController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller"><span class="typeNameLink">AdminController.UserCreateRequest</span></a> - Class in <a href="graphikos/automation/controller/package-summary.html">graphikos.automation.controller</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html" title="class in graphikos.automation.controller"><span class="typeNameLink">AdminController.UserRoleUpdate</span></a> - Class in <a href="graphikos/automation/controller/package-summary.html">graphikos.automation.controller</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminPageController.html#adminPage(org.springframework.ui.Model)">adminPage(Model)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminPageController.html" title="class in graphikos.automation.controller">AdminPageController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/controller/AdminPageController.html" title="class in graphikos.automation.controller"><span class="typeNameLink">AdminPageController</span></a> - Class in <a href="graphikos/automation/controller/package-summary.html">graphikos.automation.controller</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminPageController.html#%3Cinit%3E()">AdminPageController()</a></span> - Constructor for class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminPageController.html" title="class in graphikos.automation.controller">AdminPageController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/controller/AuthController.html" title="class in graphikos.automation.controller"><span class="typeNameLink">AuthController</span></a> - Class in <a href="graphikos/automation/controller/package-summary.html">graphikos.automation.controller</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AuthController.html#%3Cinit%3E()">AuthController()</a></span> - Constructor for class graphikos.automation.controller.<a href="graphikos/automation/controller/AuthController.html" title="class in graphikos.automation.controller">AuthController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/config/SecurityConfig.html#authenticationManagerBean()">authenticationManagerBean()</a></span> - Method in class graphikos.automation.config.<a href="graphikos/automation/config/SecurityConfig.html" title="class in graphikos.automation.config">SecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.BuildStatus.html#AUTOMATION_RUNNING">AUTOMATION_RUNNING</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:B">
<!--   -->
</a>
<h2 class="title">B</h2>
<dl>
<dt><a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller"><span class="typeNameLink">BuildApiController</span></a> - Class in <a href="graphikos/automation/controller/package-summary.html">graphikos.automation.controller</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/BuildApiController.html#%3Cinit%3E()">BuildApiController()</a></span> - Constructor for class graphikos.automation.controller.<a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller">BuildApiController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model"><span class="typeNameLink">BuildRequest</span></a> - Class in <a href="graphikos/automation/model/package-summary.html">graphikos.automation.model</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#%3Cinit%3E()">BuildRequest()</a></span> - Constructor for class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model"><span class="typeNameLink">BuildRequest.BuildStatus</span></a> - Enum in <a href="graphikos/automation/model/package-summary.html">graphikos.automation.model</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/repository/BuildRequestRepository.html" title="interface in graphikos.automation.repository"><span class="typeNameLink">BuildRequestRepository</span></a> - Interface in <a href="graphikos/automation/repository/package-summary.html">graphikos.automation.repository</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service"><span class="typeNameLink">BuildService</span></a> - Class in <a href="graphikos/automation/service/package-summary.html">graphikos.automation.service</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#%3Cinit%3E()">BuildService()</a></span> - Constructor for class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:C">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/MainController.html#checkStatus()">checkStatus()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/MainController.html" title="class in graphikos.automation.controller">MainController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.BuildStatus.html#COMPLETED">COMPLETED</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/config/SecurityConfig.html#configure(org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder)">configure(AuthenticationManagerBuilder)</a></span> - Method in class graphikos.automation.config.<a href="graphikos/automation/config/SecurityConfig.html" title="class in graphikos.automation.config">SecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/config/SecurityConfig.html#configure(org.springframework.security.config.annotation.web.builders.HttpSecurity)">configure(HttpSecurity)</a></span> - Method in class graphikos.automation.config.<a href="graphikos/automation/config/SecurityConfig.html" title="class in graphikos.automation.config">SecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/BuildApiController.html#confirmReport(java.lang.Long)">confirmReport(Long)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller">BuildApiController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/repository/BuildRequestRepository.html#countByCreatedBy(graphikos.automation.model.User)">countByCreatedBy(User)</a></span> - Method in interface graphikos.automation.repository.<a href="graphikos/automation/repository/BuildRequestRepository.html" title="interface in graphikos.automation.repository">BuildRequestRepository</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/repository/BuildRequestRepository.html#countByStatus(graphikos.automation.model.BuildRequest.BuildStatus)">countByStatus(BuildRequest.BuildStatus)</a></span> - Method in interface graphikos.automation.repository.<a href="graphikos/automation/repository/BuildRequestRepository.html" title="interface in graphikos.automation.repository">BuildRequestRepository</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#countByStatus(graphikos.automation.model.BuildRequest.BuildStatus)">countByStatus(BuildRequest.BuildStatus)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#countByUser(graphikos.automation.model.User)">countByUser(User)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#createBuildRequest(graphikos.automation.model.BuildRequest,graphikos.automation.model.User)">createBuildRequest(BuildRequest, User)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.html#createUser(graphikos.automation.controller.AdminController.UserCreateRequest)">createUser(AdminController.UserCreateRequest)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.html" title="class in graphikos.automation.controller">AdminController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/UserService.html#createUser(graphikos.automation.model.User)">createUser(User)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service">UserService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/service/CustomUserDetailsService.html" title="class in graphikos.automation.service"><span class="typeNameLink">CustomUserDetailsService</span></a> - Class in <a href="graphikos/automation/service/package-summary.html">graphikos.automation.service</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/CustomUserDetailsService.html#%3Cinit%3E()">CustomUserDetailsService()</a></span> - Constructor for class graphikos.automation.service.<a href="graphikos/automation/service/CustomUserDetailsService.html" title="class in graphikos.automation.service">CustomUserDetailsService</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:D">
<!--   -->
</a>
<h2 class="title">D</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/MainController.html#dashboard()">dashboard()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/MainController.html" title="class in graphikos.automation.controller">MainController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/config/DataInitializer.html" title="class in graphikos.automation.config"><span class="typeNameLink">DataInitializer</span></a> - Class in <a href="graphikos/automation/config/package-summary.html">graphikos.automation.config</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/config/DataInitializer.html#%3Cinit%3E()">DataInitializer()</a></span> - Constructor for class graphikos.automation.config.<a href="graphikos/automation/config/DataInitializer.html" title="class in graphikos.automation.config">DataInitializer</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/UserService.html#deleteUser(java.lang.Long)">deleteUser(Long)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service">UserService</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:E">
<!--   -->
</a>
<h2 class="title">E</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.Role.html#EDITOR">EDITOR</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/User.Role.html" title="enum in graphikos.automation.model">User.Role</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/repository/UserRepository.html#existsByEmail(java.lang.String)">existsByEmail(String)</a></span> - Method in interface graphikos.automation.repository.<a href="graphikos/automation/repository/UserRepository.html" title="interface in graphikos.automation.repository">UserRepository</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/UserService.html#existsByEmail(java.lang.String)">existsByEmail(String)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service">UserService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/repository/UserRepository.html#existsByUsername(java.lang.String)">existsByUsername(String)</a></span> - Method in interface graphikos.automation.repository.<a href="graphikos/automation/repository/UserRepository.html" title="interface in graphikos.automation.repository">UserRepository</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/UserService.html#existsByUsername(java.lang.String)">existsByUsername(String)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service">UserService</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:F">
<!--   -->
</a>
<h2 class="title">F</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.BuildStatus.html#FAILED">FAILED</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#findAllBuilds()">findAllBuilds()</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/repository/BuildRequestRepository.html#findAllByOrderByCreatedAtDesc()">findAllByOrderByCreatedAtDesc()</a></span> - Method in interface graphikos.automation.repository.<a href="graphikos/automation/repository/BuildRequestRepository.html" title="interface in graphikos.automation.repository">BuildRequestRepository</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/UserService.html#findAllUsers()">findAllUsers()</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service">UserService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#findBuildsByStatus(graphikos.automation.model.BuildRequest.BuildStatus)">findBuildsByStatus(BuildRequest.BuildStatus)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#findBuildsByUser(graphikos.automation.model.User)">findBuildsByUser(User)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/repository/BuildRequestRepository.html#findByCreatedByOrderByCreatedAtDesc(graphikos.automation.model.User)">findByCreatedByOrderByCreatedAtDesc(User)</a></span> - Method in interface graphikos.automation.repository.<a href="graphikos/automation/repository/BuildRequestRepository.html" title="interface in graphikos.automation.repository">BuildRequestRepository</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/repository/UserRepository.html#findByEmail(java.lang.String)">findByEmail(String)</a></span> - Method in interface graphikos.automation.repository.<a href="graphikos/automation/repository/UserRepository.html" title="interface in graphikos.automation.repository">UserRepository</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/UserService.html#findByEmail(java.lang.String)">findByEmail(String)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service">UserService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#findById(java.lang.Long)">findById(Long)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/UserService.html#findById(java.lang.Long)">findById(Long)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service">UserService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/repository/BuildRequestRepository.html#findByStatusOrderByCreatedAtDesc(graphikos.automation.model.BuildRequest.BuildStatus)">findByStatusOrderByCreatedAtDesc(BuildRequest.BuildStatus)</a></span> - Method in interface graphikos.automation.repository.<a href="graphikos/automation/repository/BuildRequestRepository.html" title="interface in graphikos.automation.repository">BuildRequestRepository</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/repository/UserRepository.html#findByUsername(java.lang.String)">findByUsername(String)</a></span> - Method in interface graphikos.automation.repository.<a href="graphikos/automation/repository/UserRepository.html" title="interface in graphikos.automation.repository">UserRepository</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/UserService.html#findByUsername(java.lang.String)">findByUsername(String)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service">UserService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/repository/BuildRequestRepository.html#findRecentBuilds(java.time.LocalDateTime)">findRecentBuilds(LocalDateTime)</a></span> - Method in interface graphikos.automation.repository.<a href="graphikos/automation/repository/BuildRequestRepository.html" title="interface in graphikos.automation.repository">BuildRequestRepository</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.html#getAllUsers()">getAllUsers()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.html" title="class in graphikos.automation.controller">AdminController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getAutomationConfig()">getAutomationConfig()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getAutomationReportUrl()">getAutomationReportUrl()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/BuildApiController.html#getBuild(java.lang.Long)">getBuild(Long)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller">BuildApiController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getBuildDiffContent()">getBuildDiffContent()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/BuildApiController.html#getBuildJson(java.lang.Long)">getBuildJson(Long)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller">BuildApiController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getBuildsetup()">getBuildsetup()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getChangedFilesContent()">getChangedFilesContent()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getConversion()">getConversion()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getCreatedAt()">getCreatedAt()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getCreatedBy()">getCreatedBy()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#getEmail()">getEmail()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#getEmail()">getEmail()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#getFullName()">getFullName()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#getFullName()">getFullName()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getGraphikosi18n()">getGraphikosi18n()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getGraphikosmedia()">getGraphikosmedia()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getId()">getId()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#getId()">getId()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getImageconversion()">getImageconversion()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getManualTestcaseSheet()">getManualTestcaseSheet()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getMigrationFilesContent()">getMigrationFilesContent()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#getPassword()">getPassword()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#getPassword()">getPassword()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getPictures()">getPictures()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getReleaseType()">getReleaseType()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#getRole()">getRole()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html#getRole()">getRole()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html" title="class in graphikos.automation.controller">AdminController.UserRoleUpdate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#getRole()">getRole()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getSanityUrl()">getSanityUrl()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getShapeframework()">getShapeframework()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getShowlistingdialog()">getShowlistingdialog()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getShowoffline()">getShowoffline()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getShowrenderingframework()">getShowrenderingframework()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getShowrightpanel()">getShowrightpanel()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getShowserver()">getShowserver()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getShowslideshowviews()">getShowslideshowviews()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getShowui()">getShowui()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getStatus()">getStatus()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getUpdatedAt()">getUpdatedAt()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html#getUserId()">getUserId()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html" title="class in graphikos.automation.controller">AdminController.UserRoleUpdate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#getUsername()">getUsername()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#getUsername()">getUsername()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getZdcmFilesContent()">getZdcmFilesContent()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getZohoshow()">getZohoshow()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#getZohoshowinput()">getZohoshowinput()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/package-summary.html">graphikos.automation</a> - package graphikos.automation</dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/config/package-summary.html">graphikos.automation.config</a> - package graphikos.automation.config</dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/controller/package-summary.html">graphikos.automation.controller</a> - package graphikos.automation.controller</dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/model/package-summary.html">graphikos.automation.model</a> - package graphikos.automation.model</dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/repository/package-summary.html">graphikos.automation.repository</a> - package graphikos.automation.repository</dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/service/package-summary.html">graphikos.automation.service</a> - package graphikos.automation.service</dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/GraphikosAutomationApplication.html" title="class in graphikos.automation"><span class="typeNameLink">GraphikosAutomationApplication</span></a> - Class in <a href="graphikos/automation/package-summary.html">graphikos.automation</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/GraphikosAutomationApplication.html#%3Cinit%3E()">GraphikosAutomationApplication()</a></span> - Constructor for class graphikos.automation.<a href="graphikos/automation/GraphikosAutomationApplication.html" title="class in graphikos.automation">GraphikosAutomationApplication</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:H">
<!--   -->
</a>
<h2 class="title">H</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/MainController.html#home()">home()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/MainController.html" title="class in graphikos.automation.controller">MainController</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:I">
<!--   -->
</a>
<h2 class="title">I</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.BuildStatus.html#IN_PROGRESS">IN_PROGRESS</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isBuildDiffChecked()">isBuildDiffChecked()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isBuildUpdatedToLive()">isBuildUpdatedToLive()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isChangedFilesChecked()">isChangedFilesChecked()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isConversionAutoBuildUpdate()">isConversionAutoBuildUpdate()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isEnableAutobuildUpdate()">isEnableAutobuildUpdate()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#isEnabled()">isEnabled()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isFinalSanityCompleted()">isFinalSanityCompleted()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isImageconversionAutoBuildUpdate()">isImageconversionAutoBuildUpdate()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isManualTestcaseConfirmed()">isManualTestcaseConfirmed()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isMigrationFilesChecked()">isMigrationFilesChecked()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isPicturesAutoBuildUpdate()">isPicturesAutoBuildUpdate()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isPreAutomation()">isPreAutomation()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isPreSanity()">isPreSanity()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isReportReceived()">isReportReceived()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#isZdcmFilesChecked()">isZdcmFilesChecked()</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:L">
<!--   -->
</a>
<h2 class="title">L</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.BuildStatus.html#LIVE_BUILD_UPDATED">LIVE_BUILD_UPDATED</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/CustomUserDetailsService.html#loadUserByUsername(java.lang.String)">loadUserByUsername(String)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/CustomUserDetailsService.html" title="class in graphikos.automation.service">CustomUserDetailsService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AuthController.html#loginPage(java.lang.String,java.lang.String,org.springframework.ui.Model)">loginPage(String, String, Model)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AuthController.html" title="class in graphikos.automation.controller">AuthController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/MainController.html#logout()">logout()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/MainController.html" title="class in graphikos.automation.controller">MainController</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:M">
<!--   -->
</a>
<h2 class="title">M</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/GraphikosAutomationApplication.html#main(java.lang.String%5B%5D)">main(String[])</a></span> - Static method in class graphikos.automation.<a href="graphikos/automation/GraphikosAutomationApplication.html" title="class in graphikos.automation">GraphikosAutomationApplication</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/MainController.html#main(org.springframework.ui.Model)">main(Model)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/MainController.html" title="class in graphikos.automation.controller">MainController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/controller/MainController.html" title="class in graphikos.automation.controller"><span class="typeNameLink">MainController</span></a> - Class in <a href="graphikos/automation/controller/package-summary.html">graphikos.automation.controller</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/MainController.html#%3Cinit%3E()">MainController()</a></span> - Constructor for class graphikos.automation.controller.<a href="graphikos/automation/controller/MainController.html" title="class in graphikos.automation.controller">MainController</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:N">
<!--   -->
</a>
<h2 class="title">N</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/MainController.html#nicChecks()">nicChecks()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/MainController.html" title="class in graphikos.automation.controller">MainController</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:P">
<!--   -->
</a>
<h2 class="title">P</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/config/SecurityConfig.html#passwordEncoder()">passwordEncoder()</a></span> - Method in class graphikos.automation.config.<a href="graphikos/automation/config/SecurityConfig.html" title="class in graphikos.automation.config">SecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.BuildStatus.html#PRE_BUILD_UPDATED">PRE_BUILD_UPDATED</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:Q">
<!--   -->
</a>
<h2 class="title">Q</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.Role.html#QA">QA</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/User.Role.html" title="enum in graphikos.automation.model">User.Role</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:R">
<!--   -->
</a>
<h2 class="title">R</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AuthController.html#registerPage(org.springframework.ui.Model)">registerPage(Model)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AuthController.html" title="class in graphikos.automation.controller">AuthController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AuthController.html#registerUser(graphikos.automation.model.User,org.springframework.validation.BindingResult,org.springframework.ui.Model)">registerUser(User, BindingResult, Model)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AuthController.html" title="class in graphikos.automation.controller">AuthController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/config/DataInitializer.html#run(java.lang.String...)">run(String...)</a></span> - Method in class graphikos.automation.config.<a href="graphikos/automation/config/DataInitializer.html" title="class in graphikos.automation.config">DataInitializer</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:S">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><a href="graphikos/automation/config/SecurityConfig.html" title="class in graphikos.automation.config"><span class="typeNameLink">SecurityConfig</span></a> - Class in <a href="graphikos/automation/config/package-summary.html">graphikos.automation.config</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/config/SecurityConfig.html#%3Cinit%3E()">SecurityConfig()</a></span> - Constructor for class graphikos.automation.config.<a href="graphikos/automation/config/SecurityConfig.html" title="class in graphikos.automation.config">SecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setAutomationConfig(java.lang.String)">setAutomationConfig(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setAutomationReportUrl(java.lang.String)">setAutomationReportUrl(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setBuildDiffChecked(boolean)">setBuildDiffChecked(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setBuildDiffContent(java.lang.String)">setBuildDiffContent(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setBuildsetup(java.lang.String)">setBuildsetup(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setBuildUpdatedToLive(boolean)">setBuildUpdatedToLive(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setChangedFilesChecked(boolean)">setChangedFilesChecked(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setChangedFilesContent(java.lang.String)">setChangedFilesContent(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setConversion(java.lang.String)">setConversion(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setConversionAutoBuildUpdate(boolean)">setConversionAutoBuildUpdate(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setCreatedAt(java.time.LocalDateTime)">setCreatedAt(LocalDateTime)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setCreatedBy(graphikos.automation.model.User)">setCreatedBy(User)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#setEmail(java.lang.String)">setEmail(String)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#setEmail(java.lang.String)">setEmail(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setEnableAutobuildUpdate(boolean)">setEnableAutobuildUpdate(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#setEnabled(boolean)">setEnabled(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setFinalSanityCompleted(boolean)">setFinalSanityCompleted(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#setFullName(java.lang.String)">setFullName(String)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#setFullName(java.lang.String)">setFullName(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setGraphikosi18n(java.lang.String)">setGraphikosi18n(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setGraphikosmedia(java.lang.String)">setGraphikosmedia(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setId(java.lang.Long)">setId(Long)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#setId(java.lang.Long)">setId(Long)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setImageconversion(java.lang.String)">setImageconversion(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setImageconversionAutoBuildUpdate(boolean)">setImageconversionAutoBuildUpdate(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setManualTestcaseConfirmed(boolean)">setManualTestcaseConfirmed(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setManualTestcaseSheet(java.lang.String)">setManualTestcaseSheet(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setMigrationFilesChecked(boolean)">setMigrationFilesChecked(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setMigrationFilesContent(java.lang.String)">setMigrationFilesContent(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#setPassword(java.lang.String)">setPassword(String)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#setPassword(java.lang.String)">setPassword(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setPictures(java.lang.String)">setPictures(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setPicturesAutoBuildUpdate(boolean)">setPicturesAutoBuildUpdate(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setPreAutomation(boolean)">setPreAutomation(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setPreSanity(boolean)">setPreSanity(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setReleaseType(java.lang.String)">setReleaseType(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setReportReceived(boolean)">setReportReceived(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#setRole(graphikos.automation.model.User.Role)">setRole(User.Role)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#setRole(java.lang.String)">setRole(String)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html#setRole(java.lang.String)">setRole(String)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html" title="class in graphikos.automation.controller">AdminController.UserRoleUpdate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setSanityUrl(java.lang.String)">setSanityUrl(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setShapeframework(java.lang.String)">setShapeframework(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setShowlistingdialog(java.lang.String)">setShowlistingdialog(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setShowoffline(java.lang.String)">setShowoffline(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setShowrenderingframework(java.lang.String)">setShowrenderingframework(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setShowrightpanel(java.lang.String)">setShowrightpanel(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setShowserver(java.lang.String)">setShowserver(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setShowslideshowviews(java.lang.String)">setShowslideshowviews(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setShowui(java.lang.String)">setShowui(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setStatus(graphikos.automation.model.BuildRequest.BuildStatus)">setStatus(BuildRequest.BuildStatus)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setUpdatedAt(java.time.LocalDateTime)">setUpdatedAt(LocalDateTime)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html#setUserId(java.lang.String)">setUserId(String)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html" title="class in graphikos.automation.controller">AdminController.UserRoleUpdate</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#setUsername(java.lang.String)">setUsername(String)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#setUsername(java.lang.String)">setUsername(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setZdcmFilesChecked(boolean)">setZdcmFilesChecked(boolean)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setZdcmFilesContent(java.lang.String)">setZdcmFilesContent(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setZohoshow(java.lang.String)">setZohoshow(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.html#setZohoshowinput(java.lang.String)">setZohoshowinput(String)</a></span> - Method in class graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.html" title="class in graphikos.automation.model">BuildRequest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/BuildApiController.html#startAutomation(java.lang.Long)">startAutomation(Long)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller">BuildApiController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#startAutomation(java.lang.Long,java.lang.String)">startAutomation(Long, String)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/BuildApiController.html#submitBuild(graphikos.automation.model.BuildRequest)">submitBuild(BuildRequest)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller">BuildApiController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.BuildStatus.html#SUBMITTED">SUBMITTED</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:U">
<!--   -->
</a>
<h2 class="title">U</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.Role.html#ULTIMATE">ULTIMATE</a></span> - graphikos.automation.model.<a href="graphikos/automation/model/User.Role.html" title="enum in graphikos.automation.model">User.Role</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/MainController.html#updateBuild()">updateBuild()</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/MainController.html" title="class in graphikos.automation.controller">MainController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#updateBuildRequest(graphikos.automation.model.BuildRequest)">updateBuildRequest(BuildRequest)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#updateBuildStatus(java.lang.Long,graphikos.automation.model.BuildRequest.BuildStatus)">updateBuildStatus(Long, BuildRequest.BuildStatus)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/BuildApiController.html#updateBuildStatus(java.lang.Long,java.util.Map)">updateBuildStatus(Long, Map&lt;String, String&gt;)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller">BuildApiController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/BuildApiController.html#updateCheckboxes(java.lang.Long,java.util.Map)">updateCheckboxes(Long, Map&lt;String, Boolean&gt;)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller">BuildApiController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/BuildService.html#updatePrechecks(java.lang.Long,java.lang.String,java.lang.String,java.lang.String,java.lang.String)">updatePrechecks(Long, String, String, String, String)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/BuildService.html" title="class in graphikos.automation.service">BuildService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/BuildApiController.html#updatePrechecks(java.lang.Long,java.util.Map)">updatePrechecks(Long, Map&lt;String, Object&gt;)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/BuildApiController.html" title="class in graphikos.automation.controller">BuildApiController</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/UserService.html#updateUser(graphikos.automation.model.User)">updateUser(User)</a></span> - Method in class graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service">UserService</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.html#updateUserRoles(java.util.List)">updateUserRoles(List&lt;AdminController.UserRoleUpdate&gt;)</a></span> - Method in class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.html" title="class in graphikos.automation.controller">AdminController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model"><span class="typeNameLink">User</span></a> - Class in <a href="graphikos/automation/model/package-summary.html">graphikos.automation.model</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#%3Cinit%3E()">User()</a></span> - Constructor for class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.html#%3Cinit%3E(java.lang.String,java.lang.String,java.lang.String,java.lang.String,graphikos.automation.model.User.Role)">User(String, String, String, String, User.Role)</a></span> - Constructor for class graphikos.automation.model.<a href="graphikos/automation/model/User.html" title="class in graphikos.automation.model">User</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/model/User.Role.html" title="enum in graphikos.automation.model"><span class="typeNameLink">User.Role</span></a> - Enum in <a href="graphikos/automation/model/package-summary.html">graphikos.automation.model</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserCreateRequest.html#%3Cinit%3E()">UserCreateRequest()</a></span> - Constructor for class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserCreateRequest.html" title="class in graphikos.automation.controller">AdminController.UserCreateRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/repository/UserRepository.html" title="interface in graphikos.automation.repository"><span class="typeNameLink">UserRepository</span></a> - Interface in <a href="graphikos/automation/repository/package-summary.html">graphikos.automation.repository</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html#%3Cinit%3E()">UserRoleUpdate()</a></span> - Constructor for class graphikos.automation.controller.<a href="graphikos/automation/controller/AdminController.UserRoleUpdate.html" title="class in graphikos.automation.controller">AdminController.UserRoleUpdate</a></dt>
<dd>&nbsp;</dd>
<dt><a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service"><span class="typeNameLink">UserService</span></a> - Class in <a href="graphikos/automation/service/package-summary.html">graphikos.automation.service</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/service/UserService.html#%3Cinit%3E()">UserService()</a></span> - Constructor for class graphikos.automation.service.<a href="graphikos/automation/service/UserService.html" title="class in graphikos.automation.service">UserService</a></dt>
<dd>&nbsp;</dd>
</dl>
<a id="I:V">
<!--   -->
</a>
<h2 class="title">V</h2>
<dl>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.BuildStatus.html#valueOf(java.lang.String)">valueOf(String)</a></span> - Static method in enum graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.Role.html#valueOf(java.lang.String)">valueOf(String)</a></span> - Static method in enum graphikos.automation.model.<a href="graphikos/automation/model/User.Role.html" title="enum in graphikos.automation.model">User.Role</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/BuildRequest.BuildStatus.html#values()">values()</a></span> - Static method in enum graphikos.automation.model.<a href="graphikos/automation/model/BuildRequest.BuildStatus.html" title="enum in graphikos.automation.model">BuildRequest.BuildStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="graphikos/automation/model/User.Role.html#values()">values()</a></span> - Static method in enum graphikos.automation.model.<a href="graphikos/automation/model/User.Role.html" title="enum in graphikos.automation.model">User.Role</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
</dl>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:Q">Q</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<br><a href="allclasses-index.html">All&nbsp;Classes</a>&nbsp;<a href="allpackages-index.html">All&nbsp;Packages</a></div>
</main>
<footer role="contentinfo">
<nav role="navigation">
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a id="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a id="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
</div>
<a id="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</nav>
</footer>
</body>
</html>
