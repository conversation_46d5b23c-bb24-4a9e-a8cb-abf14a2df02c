/* 
 * Javadoc style sheet
 */

@import url('resources/fonts/dejavu.css');

/*
 * Styles for individual HTML elements.
 *
 * These are styles that are specific to individual HTML elements. Changing them affects the style of a particular
 * HTML element throughout the page.
 */

body {
    background-color:#ffffff;
    color:#353833;
    font-family:'DejaVu Sans', Arial, Helvetica, sans-serif;
    font-size:14px;
    margin:0;
    padding:0;
    height:100%;
    width:100%;
}
iframe {
    margin:0;
    padding:0;
    height:100%;
    width:100%;
    overflow-y:scroll;
    border:none;
}
a:link, a:visited {
    text-decoration:none;
    color:#4A6782;
}
a[href]:hover, a[href]:focus {
    text-decoration:none;
    color:#bb7a2a;
}
a[name] {
    color:#353833;
}
a[name]:before, a[name]:target, a[id]:before, a[id]:target {
    content:"";
    display:inline-block;
    position:relative;
    padding-top:129px;
    margin-top:-129px;
}
pre {
    font-family:'DejaVu Sans Mono', monospace;
    font-size:14px;
}
h1 {
    font-size:20px;
}
h2 {
    font-size:18px;
}
h3 {
    font-size:16px;
    font-style:italic;
}
h4 {
    font-size:13px;
}
h5 {
    font-size:12px;
}
h6 {
    font-size:11px;
}
ul {
    list-style-type:disc;
}
code, tt {
    font-family:'DejaVu Sans Mono', monospace;
    font-size:14px;
    padding-top:4px;
    margin-top:8px;
    line-height:1.4em;
}
dt code {
    font-family:'DejaVu Sans Mono', monospace;
    font-size:14px;
    padding-top:4px;
}
table tr td dt code {
    font-family:'DejaVu Sans Mono', monospace;
    font-size:14px;
    vertical-align:top;
    padding-top:4px;
}
sup {
    font-size:8px;
}

/*
 * Styles for HTML generated by javadoc.
 *
 * These are style classes that are used by the standard doclet to generate HTML documentation.
 */

/*
 * Styles for document title and copyright.
 */
.clear {
    clear:both;
    height:0px;
    overflow:hidden;
}
.aboutLanguage {
    float:right;
    padding:0px 21px;
    font-size:11px;
    z-index:200;
    margin-top:-9px;
}
.legalCopy {
    margin-left:.5em;
}
.bar a, .bar a:link, .bar a:visited, .bar a:active {
    color:#FFFFFF;
    text-decoration:none;
}
.bar a:hover, .bar a:focus {
    color:#bb7a2a;
}
.tab {
    background-color:#0066FF;
    color:#ffffff;
    padding:8px;
    width:5em;
    font-weight:bold;
}
/*
 * Styles for navigation bar.
 */
.bar {
    background-color:#4D7A97;
    color:#FFFFFF;
    padding:.8em .5em .4em .8em;
    height:auto;/*height:1.8em;*/
    font-size:11px;
    margin:0;
}
.navPadding {
    padding-top: 107px;
}
.fixedNav {
    position:fixed;
    width:100%;
    z-index:999;
    background-color:#ffffff;
}
.topNav {
    background-color:#4D7A97;
    color:#FFFFFF;
    float:left;
    padding:0;
    width:100%;
    clear:right;
    height:2.8em;
    padding-top:10px;
    overflow:hidden;
    font-size:12px; 
}
.bottomNav {
    margin-top:10px;
    background-color:#4D7A97;
    color:#FFFFFF;
    float:left;
    padding:0;
    width:100%;
    clear:right;
    height:2.8em;
    padding-top:10px;
    overflow:hidden;
    font-size:12px;
}
.subNav {
    background-color:#dee3e9;
    float:left;
    width:100%;
    overflow:hidden;
    font-size:12px;
}
.subNav div {
    clear:left;
    float:left;
    padding:0 0 5px 6px;
    text-transform:uppercase;
}
ul.navList, ul.subNavList {
    float:left;
    margin:0 25px 0 0;
    padding:0;
}
ul.navList li{
    list-style:none;
    float:left;
    padding: 5px 6px;
    text-transform:uppercase;
}
ul.navListSearch {
    float:right;
    margin:0 0 0 0;
    padding:0;
}
ul.navListSearch li {
    list-style:none;
    float:right;
    padding: 5px 6px;
    text-transform:uppercase;
}
ul.navListSearch li label {
    position:relative;
    right:-16px;
}
ul.subNavList li {
    list-style:none;
    float:left;
}
.topNav a:link, .topNav a:active, .topNav a:visited, .bottomNav a:link, .bottomNav a:active, .bottomNav a:visited {
    color:#FFFFFF;
    text-decoration:none;
    text-transform:uppercase;
}
.topNav a:hover, .bottomNav a:hover {
    text-decoration:none;
    color:#bb7a2a;
    text-transform:uppercase;
}
.navBarCell1Rev {
    background-color:#F8981D;
    color:#253441;
    margin: auto 5px;
}
.skipNav {
    position:absolute;
    top:auto;
    left:-9999px;
    overflow:hidden;
}
/*
 * Styles for page header and footer.
 */
.header, .footer {
    clear:both;
    margin:0 20px;
    padding:5px 0 0 0;
}
.indexNav {
    position:relative;
    font-size:12px;
    background-color:#dee3e9;
}
.indexNav ul {
    margin-top:0;
    padding:5px;
}
.indexNav ul li {
    display:inline;
    list-style-type:none;
    padding-right:10px;
    text-transform:uppercase;
}
.indexNav h1 {
    font-size:13px;
}
.title {
    color:#2c4557;
    margin:10px 0;
}
.subTitle {
    margin:5px 0 0 0;
}
.header ul {
    margin:0 0 15px 0;
    padding:0;
}
.footer ul {
    margin:20px 0 5px 0;
}
.header ul li, .footer ul li {
    list-style:none;
    font-size:13px;
}
/*
 * Styles for headings.
 */
div.details ul.blockList ul.blockList ul.blockList li.blockList h4, div.details ul.blockList ul.blockList ul.blockListLast li.blockList h4 {
    background-color:#dee3e9;
    border:1px solid #d0d9e0;
    margin:0 0 6px -8px;
    padding:7px 5px;
}
ul.blockList ul.blockList ul.blockList li.blockList h3 {
    background-color:#dee3e9;
    border:1px solid #d0d9e0;
    margin:0 0 6px -8px;
    padding:7px 5px;
}
ul.blockList ul.blockList li.blockList h3 {
    padding:0;
    margin:15px 0;
}
ul.blockList li.blockList h2 {
    padding:0px 0 20px 0;
}
/*
 * Styles for page layout containers.
 */
.contentContainer, .sourceContainer, .classUseContainer, .serializedFormContainer, .constantValuesContainer,
.allClassesContainer, .allPackagesContainer {
    clear:both;
    padding:10px 20px;
    position:relative;
}
.indexContainer {
    margin:10px;
    position:relative;
    font-size:12px;
}
.indexContainer h2 {
    font-size:13px;
    padding:0 0 3px 0;
}
.indexContainer ul {
    margin:0;
    padding:0;
}
.indexContainer ul li {
    list-style:none;
    padding-top:2px;
}
.contentContainer .description dl dt, .contentContainer .details dl dt, .serializedFormContainer dl dt {
    font-size:12px;
    font-weight:bold;
    margin:10px 0 0 0;
    color:#4E4E4E;
}
.contentContainer .description dl dd, .contentContainer .details dl dd, .serializedFormContainer dl dd {
    margin:5px 0 10px 0px;
    font-size:14px;
    font-family:'DejaVu Serif', Georgia, "Times New Roman", Times, serif;
}
.serializedFormContainer dl.nameValue dt {
    margin-left:1px;
    font-size:1.1em;
    display:inline;
    font-weight:bold;
}
.serializedFormContainer dl.nameValue dd {
    margin:0 0 0 1px;
    font-size:1.1em;
    display:inline;
}
/*
 * Styles for lists.
 */
li.circle {
    list-style:circle;
}
ul.horizontal li {
    display:inline;
    font-size:0.9em;
}
ul.inheritance {
    margin:0;
    padding:0;
}
ul.inheritance li {
    display:inline;
    list-style:none;
}
ul.inheritance li ul.inheritance {
    margin-left:15px;
    padding-left:15px;
    padding-top:1px;
}
ul.blockList, ul.blockListLast {
    margin:10px 0 10px 0;
    padding:0;
}
ul.blockList li.blockList, ul.blockListLast li.blockList {
    list-style:none;
    margin-bottom:15px;
    line-height:1.4;
}
ul.blockList ul.blockList li.blockList, ul.blockList ul.blockListLast li.blockList {
    padding:0px 20px 5px 10px;
    border:1px solid #ededed; 
    background-color:#f8f8f8;
}
ul.blockList ul.blockList ul.blockList li.blockList, ul.blockList ul.blockList ul.blockListLast li.blockList {
    padding:0 0 5px 8px;
    background-color:#ffffff;
    border:none;
}
ul.blockList ul.blockList ul.blockList ul.blockList li.blockList {
    margin-left:0;
    padding-left:0;
    padding-bottom:15px;
    border:none;
}
ul.blockList ul.blockList ul.blockList ul.blockList li.blockListLast {
    list-style:none;
    border-bottom:none;
    padding-bottom:0;
}
table tr td dl, table tr td dl dt, table tr td dl dd {
    margin-top:0;
    margin-bottom:1px;
}
/*
 * Styles for tables.
 */
.overviewSummary, .memberSummary, .typeSummary, .useSummary, .constantsSummary, .deprecatedSummary,
.requiresSummary, .packagesSummary, .providesSummary, .usesSummary {
    width:100%;
    border-spacing:0;
    border-left:1px solid #EEE; 
    border-right:1px solid #EEE; 
    border-bottom:1px solid #EEE; 
}
.overviewSummary, .memberSummary, .requiresSummary, .packagesSummary, .providesSummary, .usesSummary  {
    padding:0px;
}
.overviewSummary caption, .memberSummary caption, .typeSummary caption,
.useSummary caption, .constantsSummary caption, .deprecatedSummary caption,
.requiresSummary caption, .packagesSummary caption, .providesSummary caption, .usesSummary caption {
    position:relative;
    text-align:left;
    background-repeat:no-repeat;
    color:#253441;
    font-weight:bold;
    clear:none;
    overflow:hidden;
    padding:0px;
    padding-top:10px;
    padding-left:1px;
    margin:0px;
    white-space:pre;
}
.constantsSummary caption a:link, .constantsSummary caption a:visited,
.useSummary caption a:link, .useSummary caption a:visited {
    color:#1f389c;
}
.overviewSummary caption a:link, .memberSummary caption a:link, .typeSummary caption a:link,
.deprecatedSummary caption a:link,
.requiresSummary caption a:link, .packagesSummary caption a:link, .providesSummary caption a:link,
.usesSummary caption a:link,
.overviewSummary caption a:hover, .memberSummary caption a:hover, .typeSummary caption a:hover,
.useSummary caption a:hover, .constantsSummary caption a:hover, .deprecatedSummary caption a:hover,
.requiresSummary caption a:hover, .packagesSummary caption a:hover, .providesSummary caption a:hover,
.usesSummary caption a:hover,
.overviewSummary caption a:active, .memberSummary caption a:active, .typeSummary caption a:active,
.useSummary caption a:active, .constantsSummary caption a:active, .deprecatedSummary caption a:active,
.requiresSummary caption a:active, .packagesSummary caption a:active, .providesSummary caption a:active,
.usesSummary caption a:active,
.overviewSummary caption a:visited, .memberSummary caption a:visited, .typeSummary caption a:visited,
.deprecatedSummary caption a:visited,
.requiresSummary caption a:visited, .packagesSummary caption a:visited, .providesSummary caption a:visited,
.usesSummary caption a:visited {
    color:#FFFFFF;
}
.overviewSummary caption span, .memberSummary caption span, .typeSummary caption span,
.useSummary caption span, .constantsSummary caption span, .deprecatedSummary caption span,
.requiresSummary caption span, .packagesSummary caption span, .providesSummary caption span,
.usesSummary caption span {
    white-space:nowrap;
    padding-top:5px;
    padding-left:12px;
    padding-right:12px;
    padding-bottom:7px;
    display:inline-block;
    float:left;
    background-color:#F8981D;
    border: none;
    height:16px;
}
.memberSummary caption span.activeTableTab span, .packagesSummary caption span.activeTableTab span,
.overviewSummary caption span.activeTableTab span, .typeSummary caption span.activeTableTab span {
    white-space:nowrap;
    padding-top:5px;
    padding-left:12px;
    padding-right:12px;
    margin-right:3px;
    display:inline-block;
    float:left;
    background-color:#F8981D;
    height:16px;
}
.memberSummary caption span.tableTab span, .packagesSummary caption span.tableTab span,
.overviewSummary caption span.tableTab span, .typeSummary caption span.tableTab span {
    white-space:nowrap;
    padding-top:5px;
    padding-left:12px;
    padding-right:12px;
    margin-right:3px;
    display:inline-block;
    float:left;
    background-color:#4D7A97;
    height:16px;
}
.memberSummary caption span.tableTab, .memberSummary caption span.activeTableTab,
.packagesSummary caption span.tableTab, .packagesSummary caption span.activeTableTab,
.overviewSummary caption span.tableTab, .overviewSummary caption span.activeTableTab,
.typeSummary caption span.tableTab, .typeSummary caption span.activeTableTab {
    padding-top:0px;
    padding-left:0px;
    padding-right:0px;
    background-image:none;
    float:none;
    display:inline;
}
.overviewSummary .tabEnd, .memberSummary .tabEnd, .typeSummary .tabEnd,
.useSummary .tabEnd, .constantsSummary .tabEnd, .deprecatedSummary .tabEnd,
.requiresSummary .tabEnd, .packagesSummary .tabEnd, .providesSummary .tabEnd, .usesSummary .tabEnd {
    display:none;
    width:5px;
    position:relative;
    float:left;
    background-color:#F8981D;
}
.memberSummary .activeTableTab .tabEnd, .packagesSummary .activeTableTab .tabEnd,
.overviewSummary .activeTableTab .tabEnd, .typeSummary .activeTableTab .tabEnd {
    display:none;
    width:5px;
    margin-right:3px;
    position:relative; 
    float:left;
    background-color:#F8981D;
}
.memberSummary .tableTab .tabEnd, .packagesSummary .tableTab .tabEnd,
.overviewSummary .tableTab .tabEnd, .typeSummary .tableTab .tabEnd {
    display:none;
    width:5px;
    margin-right:3px;
    position:relative;
    background-color:#4D7A97;
    float:left;
}
.rowColor th, .altColor th {
    font-weight:normal;
}
.overviewSummary td, .memberSummary td, .typeSummary td,
.useSummary td, .constantsSummary td, .deprecatedSummary td,
.requiresSummary td, .packagesSummary td, .providesSummary td, .usesSummary td {
    text-align:left;
    padding:0px 0px 12px 10px;
}
th.colFirst, th.colSecond, th.colLast, th.colConstructorName, th.colDeprecatedItemName, .useSummary th,
.constantsSummary th, .packagesSummary th, td.colFirst, td.colSecond, td.colLast, .useSummary td,
.constantsSummary td {
    vertical-align:top;
    padding-right:0px;
    padding-top:8px;
    padding-bottom:3px;
}
th.colFirst, th.colSecond, th.colLast, th.colConstructorName, th.colDeprecatedItemName, .constantsSummary th,
.packagesSummary th {
    background:#dee3e9;
    text-align:left;
    padding:8px 3px 3px 7px;
}
td.colFirst, th.colFirst {
    font-size:13px;
}
td.colSecond, th.colSecond, td.colLast, th.colConstructorName, th.colDeprecatedItemName, th.colLast {
    font-size:13px;
}
.constantsSummary th, .packagesSummary th {
    font-size:13px;
}
.providesSummary th.colFirst, .providesSummary th.colLast, .providesSummary td.colFirst,
.providesSummary td.colLast {
    white-space:normal;
    font-size:13px;
}
.overviewSummary td.colFirst, .overviewSummary th.colFirst,
.requiresSummary td.colFirst, .requiresSummary th.colFirst,
.packagesSummary td.colFirst, .packagesSummary td.colSecond, .packagesSummary th.colFirst, .packagesSummary th,
.usesSummary td.colFirst, .usesSummary th.colFirst,
.providesSummary td.colFirst, .providesSummary th.colFirst,
.memberSummary td.colFirst, .memberSummary th.colFirst,
.memberSummary td.colSecond, .memberSummary th.colSecond, .memberSummary th.colConstructorName,
.typeSummary td.colFirst, .typeSummary th.colFirst {
    vertical-align:top;
}
.packagesSummary th.colLast, .packagesSummary td.colLast {
    white-space:normal;
}
td.colFirst a:link, td.colFirst a:visited,
td.colSecond a:link, td.colSecond a:visited,
th.colFirst a:link, th.colFirst a:visited,
th.colSecond a:link, th.colSecond a:visited,
th.colConstructorName a:link, th.colConstructorName a:visited,
th.colDeprecatedItemName a:link, th.colDeprecatedItemName a:visited, 
.constantValuesContainer td a:link, .constantValuesContainer td a:visited, 
.allClassesContainer td a:link, .allClassesContainer td a:visited, 
.allPackagesContainer td a:link, .allPackagesContainer td a:visited {
    font-weight:bold;
}
.tableSubHeadingColor {
    background-color:#EEEEFF;
}
.altColor, .altColor th {
    background-color:#FFFFFF;
}
.rowColor, .rowColor th {
    background-color:#EEEEEF;
}
/*
 * Styles for contents.
 */
.description pre {
    margin-top:0;
}
.deprecatedContent {
    margin:0;
    padding:10px 0;
}
.docSummary {
    padding:0;
}
ul.blockList ul.blockList ul.blockList li.blockList h3 {
    font-style:normal;
}
div.block {
    font-size:14px;
    font-family:'DejaVu Serif', Georgia, "Times New Roman", Times, serif;
}
td.colLast div {
    padding-top:0px;
}
td.colLast a {
    padding-bottom:3px;
}
/*
 * Styles for formatting effect.
 */
.sourceLineNo {
    color:green;
    padding:0 30px 0 0;
}
h1.hidden {
    visibility:hidden;
    overflow:hidden;
    font-size:10px;
}
.block {
    display:block;
    margin:3px 10px 2px 0px;
    color:#474747;
}
.deprecatedLabel, .descfrmTypeLabel, .implementationLabel, .memberNameLabel, .memberNameLink,
.moduleLabelInPackage, .moduleLabelInType, .overrideSpecifyLabel, .packageLabelInType,
.packageHierarchyLabel, .paramLabel, .returnLabel, .seeLabel, .simpleTagLabel,
.throwsLabel, .typeNameLabel, .typeNameLink, .searchTagLink {
    font-weight:bold;
}
.deprecationComment, .emphasizedPhrase, .interfaceName {
    font-style:italic;
}
.deprecationBlock {
    font-size:14px;
    font-family:'DejaVu Serif', Georgia, "Times New Roman", Times, serif;
    border-style:solid;
    border-width:thin;
    border-radius:10px;
    padding:10px;
    margin-bottom:10px;
    margin-right:10px;
    display:inline-block;
}
div.block div.deprecationComment, div.block div.block span.emphasizedPhrase,
div.block div.block span.interfaceName {
    font-style:normal;
}
div.contentContainer ul.blockList li.blockList h2 {
    padding-bottom:0px;
}
/*
 * Styles for IFRAME.
 */
.mainContainer {
    margin:0 auto; 
    padding:0; 
    height:100%; 
    width:100%; 
    position:fixed; 
    top:0; 
    left:0;
}
.leftContainer {
    height:100%;
    position:fixed;
    width:320px;
}
.leftTop {
    position:relative;
    float:left;
    width:315px;
    top:0;
    left:0;
    height:30%;
    border-right:6px solid #ccc;
    border-bottom:6px solid #ccc;
}
.leftBottom {
    position:relative;
    float:left;
    width:315px;
    bottom:0;
    left:0;
    height:70%;
    border-right:6px solid #ccc;
    border-top:1px solid #000;
}
.rightContainer {
    position:absolute;
    left:320px;
    top:0;
    bottom:0;
    height:100%;
    right:0;
    border-left:1px solid #000;
}
.rightIframe {
    margin:0;
    padding:0;
    height:100%;
    right:30px;
    width:100%;
    overflow:visible;
    margin-bottom:30px;
}
/*
 * Styles specific to HTML5 elements.
 */
main, nav, header, footer, section {
    display:block;
}
/*
 * Styles for javadoc search.
 */
.ui-autocomplete-category {
    font-weight:bold;
    font-size:15px;
    padding:7px 0 7px 3px;
    background-color:#4D7A97;
    color:#FFFFFF;
}
.resultItem {
    font-size:13px;
}
.ui-autocomplete {
    max-height:85%;
    max-width:65%;
    overflow-y:scroll;
    overflow-x:scroll;
    white-space:nowrap;
    box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}
ul.ui-autocomplete {
    position:fixed;
    z-index:999999;
    background-color: #FFFFFF;
}
ul.ui-autocomplete  li {
    float:left;
    clear:both;
    width:100%;
}
.resultHighlight {
    font-weight:bold;
}
.ui-autocomplete .result-item {
    font-size: inherit;
}
#search {
    background-image:url('resources/glass.png');
    background-size:13px;
    background-repeat:no-repeat;
    background-position:2px 3px;
    padding-left:20px;
    position:relative;
    right:-18px;
}
#reset {
    background-color: rgb(255,255,255);
    background-image:url('resources/x.png');
    background-position:center;
    background-repeat:no-repeat;
    background-size:12px;
    border:0 none;
    width:16px;
    height:17px;
    position:relative;
    left:-4px;
    top:-4px;
    font-size:0px;
}
.watermark {
    color:#545454;
}
.searchTagDescResult {
    font-style:italic;
    font-size:11px;
}
.searchTagHolderResult {
    font-style:italic;
    font-size:12px;
}
.searchTagResult:before, .searchTagResult:target {
    color:red;
}
.moduleGraph span {
    display:none;
    position:absolute;
}
.moduleGraph:hover span {
    display:block;
    margin: -100px 0 0 100px;
    z-index: 1;
}
.methodSignature {
    white-space:normal;
}

/*
 * Styles for user-provided tables.
 *
 * borderless:
 *      No borders, vertical margins, styled caption.
 *      This style is provided for use with existing doc comments.
 *      In general, borderless tables should not be used for layout purposes.
 *
 * plain:
 *      Plain borders around table and cells, vertical margins, styled caption.
 *      Best for small tables or for complex tables for tables with cells that span
 *      rows and columns, when the "striped" style does not work well.
 *
 * striped:
 *      Borders around the table and vertical borders between cells, striped rows,
 *      vertical margins, styled caption.
 *      Best for tables that have a header row, and a body containing a series of simple rows.
 */

table.borderless,
table.plain,
table.striped {
    margin-top: 10px;
    margin-bottom: 10px;
}
table.borderless > caption,
table.plain > caption,
table.striped > caption {
    font-weight: bold;
    font-size: smaller;
}
table.borderless th, table.borderless td,
table.plain th, table.plain td,
table.striped th, table.striped td {
    padding: 2px 5px;
}
table.borderless,
table.borderless > thead > tr > th, table.borderless > tbody > tr > th, table.borderless > tr > th,
table.borderless > thead > tr > td, table.borderless > tbody > tr > td, table.borderless > tr > td {
    border: none;
}
table.borderless > thead > tr, table.borderless > tbody > tr, table.borderless > tr {
    background-color: transparent;
}
table.plain {
    border-collapse: collapse;
    border: 1px solid black;
}
table.plain > thead > tr, table.plain > tbody tr, table.plain > tr {
    background-color: transparent;
}
table.plain > thead > tr > th, table.plain > tbody > tr > th, table.plain > tr > th,
table.plain > thead > tr > td, table.plain > tbody > tr > td, table.plain > tr > td {
    border: 1px solid black;
}
table.striped {
    border-collapse: collapse;
    border: 1px solid black;
}
table.striped > thead {
    background-color: #E3E3E3;
}
table.striped > thead > tr > th, table.striped > thead > tr > td {
    border: 1px solid black;
}
table.striped > tbody > tr:nth-child(even) {
    background-color: #EEE
}
table.striped > tbody > tr:nth-child(odd) {
    background-color: #FFF
}
table.striped > tbody > tr > th, table.striped > tbody > tr > td {
    border-left: 1px solid black;
    border-right: 1px solid black;
}
table.striped > tbody > tr > th {
    font-weight: normal;
}
