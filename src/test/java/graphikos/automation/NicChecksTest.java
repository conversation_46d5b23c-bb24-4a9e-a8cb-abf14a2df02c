package graphikos.automation;

import io.qameta.allure.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@Epic("Build Management")
@Feature("NIC Checks")
public class NicChecksTest extends BaseTest {

    @BeforeEach
    void loginAsEditor() {
        login("editor", "editor123");
    }

    @Test
    @DisplayName("Complete NIC Checks workflow")
    @Description("Test the complete NIC Checks workflow from form submission to completion")
    @Severity(SeverityLevel.CRITICAL)
    @Story("Build Process")
    public void testCompleteNicChecksWorkflow() {
        // Navigate to NIC Checks
        navigateTo("/nic-checks");
        verifyTextVisible("NIC Checks");
        
        // Select release type
        selectOption("#releaseType", "HF");
        verifyElementVisible("#buildForm");
        
        // Fill build form
        fillInput("input[name='buildsetup']", "https://github.com/example/gc-build");
        fillInput("input[name='zohoshowinput']", "https://github.com/example/zohoshow-build");
        
        // Fill client inputs
        fillInput("input[name='shapeframework']", "shape-framework-v1.0");
        fillInput("input[name='graphikosmedia']", "graphikos-media-v2.1");
        fillInput("input[name='showrenderingframework']", "show-rendering-v3.0");
        fillInput("input[name='graphikosi18n']", "graphikos-i18n-v1.5");
        fillInput("input[name='showlistingdialog']", "show-listing-dialog-v2.0");
        fillInput("input[name='showrightpanel']", "show-right-panel-v1.8");
        fillInput("input[name='showslideshowviews']", "show-slideshow-views-v2.5");
        fillInput("input[name='showui']", "show-ui-v4.0");
        fillInput("input[name='showoffline']", "show-offline-v1.2");
        
        // Fill server configuration
        fillInput("input[name='showserver']", "show-server-v3.1");
        page.check("input[name='enableAutobuildUpdate']");
        
        // Fill other configurations
        fillInput("input[name='conversion']", "show-conversion-v2.0");
        fillInput("input[name='pictures']", "show-pictures-v1.5");
        fillInput("input[name='imageconversion']", "image-conversion-v1.0");
        
        takeScreenshot("Build form filled");
        
        // Submit build form
        clickElement("#buildForm button[type='submit']");
        
        // Wait for build submission success
        waitForElement("#automationSection");
        verifyTextVisible("Build submitted successfully!");
        
        takeScreenshot("Build submitted");
        
        // Start automation
        clickElement("#startAutomationBtn");
        
        // Wait for automation to start
        waitForElement("#automationReportSection");
        verifyElementVisible("#automationReportUrl");
        
        takeScreenshot("Automation started");
        
        // Confirm report received
        page.check("#reportReceivedCheckbox");
        
        // Fill manual testcase
        fillInput("#manualTestcaseSheet", "https://docs.google.com/spreadsheets/manual-testcases");
        page.check("#manualTestcaseCheckbox");
        
        // Verify pre-build update section appears
        verifyElementVisible("#preBuildUpdateSection");
        
        takeScreenshot("Ready for pre-build update");
        
        // Update build in pre
        clickElement("button:has-text('Update Build in Pre')");
        
        // Wait for pre checkboxes section
        waitForElement("#preCheckboxesSection");
        
        // Check pre-sanity
        page.check("#preSanityCheckbox");
        
        takeScreenshot("Pre-build checks completed");
    }

    @Test
    @DisplayName("Form validation for required fields")
    @Description("Test form validation for NIC Checks form")
    @Severity(SeverityLevel.NORMAL)
    @Story("Form Validation")
    public void testFormValidation() {
        navigateTo("/nic-checks");
        
        // Try to submit without selecting release type
        verifyElementVisible("#releaseType");
        
        // Select release type to show form
        selectOption("#releaseType", "Release");
        verifyElementVisible("#buildForm");
        
        // Try to submit with empty required fields
        clickElement("#buildForm button[type='submit']");
        
        takeScreenshot("Form validation check");
    }

    @Test
    @DisplayName("Release type selection functionality")
    @Description("Test release type dropdown functionality")
    @Severity(SeverityLevel.NORMAL)
    @Story("Form Interaction")
    public void testReleaseTypeSelection() {
        navigateTo("/nic-checks");
        
        // Initially form should be hidden
        assertTrue(!page.isVisible("#buildForm"));

        // Select HF release type
        selectOption("#releaseType", "HF");
        verifyElementVisible("#buildForm");

        takeScreenshot("HF release type selected");

        // Select Release type
        selectOption("#releaseType", "Release");
        verifyElementVisible("#buildForm");

        takeScreenshot("Release type selected");

        // Deselect (empty option)
        selectOption("#releaseType", "");
        assertTrue(!page.isVisible("#buildForm"));

        takeScreenshot("Release type deselected");
    }

    @Test
    @DisplayName("Automation configuration display")
    @Description("Test automation configuration section display")
    @Severity(SeverityLevel.NORMAL)
    @Story("Automation Setup")
    public void testAutomationConfiguration() {
        navigateTo("/nic-checks");
        
        // Select release type and fill minimal form
        selectOption("#releaseType", "HF");
        fillInput("input[name='buildsetup']", "test-build");
        
        // Submit form
        clickElement("#buildForm button[type='submit']");
        
        // Wait for automation section
        waitForElement("#automationSection");
        
        // Verify automation config is displayed
        verifyElementVisible(".automation-config");
        verifyTextVisible("domainSetup=gcautomation");
        verifyTextVisible("browser=googlechrome");
        verifyElementVisible("#startAutomationBtn");
        
        takeScreenshot("Automation configuration displayed");
    }
}
