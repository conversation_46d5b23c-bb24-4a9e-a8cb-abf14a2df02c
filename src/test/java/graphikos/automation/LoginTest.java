package graphikos.automation;

import io.qameta.allure.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@Epic("Authentication")
@Feature("Login Functionality")
public class LoginTest extends BaseTest {

    @Test
    @DisplayName("Successful login with admin credentials")
    @Description("Test successful login flow with admin user")
    @Severity(SeverityLevel.CRITICAL)
    @Story("User Authentication")
    public void testSuccessfulAdminLogin() {
        navigateTo("/login");
        
        // Verify login page elements
        verifyElementVisible("#username");
        verifyElementVisible("#password");
        verifyElementVisible("button[type='submit']");
        verifyTextVisible("Graphikos Automation");
        
        // Perform login
        fillInput("#username", "admin");
        fillInput("#password", "admin123");
        clickElement("button[type='submit']");
        
        // Verify successful login
        page.waitForURL(BASE_URL + "/dashboard");
        verifyTextVisible("Welcome back, System Administrator!");
        verifyElementVisible(".nav-menu");
        
        takeScreenshot("Admin dashboard loaded");
    }

    @Test
    @DisplayName("Successful login with editor credentials")
    @Description("Test successful login flow with editor user")
    @Severity(SeverityLevel.CRITICAL)
    @Story("User Authentication")
    public void testSuccessfulEditorLogin() {
        navigateTo("/login");
        
        // Perform login
        fillInput("#username", "editor");
        fillInput("#password", "editor123");
        clickElement("button[type='submit']");
        
        // Verify successful login
        page.waitForURL(BASE_URL + "/dashboard");
        verifyTextVisible("Welcome back, QA Editor!");
        verifyElementVisible(".nav-menu");
        
        takeScreenshot("Editor dashboard loaded");
    }

    @Test
    @DisplayName("Failed login with invalid credentials")
    @Description("Test login failure with incorrect credentials")
    @Severity(SeverityLevel.NORMAL)
    @Story("User Authentication")
    public void testFailedLogin() {
        navigateTo("/login");
        
        // Attempt login with invalid credentials
        fillInput("#username", "invalid");
        fillInput("#password", "invalid");
        clickElement("button[type='submit']");
        
        // Verify error message
        verifyTextVisible("Invalid username or password");
        
        // Verify still on login page
        assertTrue(page.url().contains("/login"));
        
        takeScreenshot("Login error displayed");
    }

    @Test
    @DisplayName("Login form validation")
    @Description("Test form validation for empty fields")
    @Severity(SeverityLevel.NORMAL)
    @Story("Form Validation")
    public void testLoginFormValidation() {
        navigateTo("/login");
        
        // Try to submit empty form
        clickElement("button[type='submit']");
        
        // Verify HTML5 validation prevents submission
        assertTrue(page.url().contains("/login"));
        
        takeScreenshot("Form validation active");
    }

    @Test
    @DisplayName("Navigation to registration page")
    @Description("Test navigation from login to registration page")
    @Severity(SeverityLevel.MINOR)
    @Story("Navigation")
    public void testNavigationToRegistration() {
        navigateTo("/login");
        
        // Click registration link
        clickElement("a[href*='/register']");
        
        // Verify navigation to registration page
        page.waitForURL(BASE_URL + "/register");
        verifyTextVisible("Create Account");
        verifyElementVisible("#fullName");
        verifyElementVisible("#role");
        
        takeScreenshot("Registration page loaded");
    }

    @Test
    @DisplayName("Logout functionality")
    @Description("Test user logout flow")
    @Severity(SeverityLevel.NORMAL)
    @Story("User Authentication")
    public void testLogout() {
        // Login first
        login("admin", "admin123");
        
        // Perform logout
        clickElement(".logout-btn");
        
        // Verify redirect to login page
        page.waitForURL(BASE_URL + "/login");
        verifyTextVisible("You have been logged out successfully");
        
        takeScreenshot("Logout successful");
    }
}
