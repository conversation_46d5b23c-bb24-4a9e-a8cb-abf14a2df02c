package graphikos.automation;

import com.microsoft.playwright.*;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;

import java.nio.file.Paths;

public class BaseTest {
    
    protected static Playwright playwright;
    protected static Browser browser;
    protected BrowserContext context;
    protected Page page;
    
    protected static final String BASE_URL = "http://localhost:8080/gqa";
    
    @BeforeAll
    static void launchBrowser() {
        playwright = Playwright.create();
        browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                .setHeadless(false)
                .setSlowMo(100));
    }
    
    @AfterAll
    static void closeBrowser() {
        if (browser != null) {
            browser.close();
        }
        if (playwright != null) {
            playwright.close();
        }
    }
    
    @BeforeEach
    void createContextAndPage() {
        context = browser.newContext(new Browser.NewContextOptions()
                .setViewportSize(1920, 1080)
                .setRecordVideoDir(Paths.get("test-results/videos")));
        
        page = context.newPage();
        
        // Enable console logging
        page.onConsoleMessage(msg -> {
            System.out.println("Console: " + msg.text());
        });
        
        // Enable request/response logging
        page.onRequest(request -> {
            System.out.println("Request: " + request.method() + " " + request.url());
        });
        
        page.onResponse(response -> {
            System.out.println("Response: " + response.status() + " " + response.url());
        });
    }
    
    @AfterEach
    void closeContext() {
        if (context != null) {
            context.close();
        }
    }
    
    @Step("Navigate to {url}")
    protected void navigateTo(String url) {
        page.navigate(BASE_URL + url);
        takeScreenshot("Navigation to " + url);
    }
    
    @Step("Login with username: {username}")
    protected void login(String username, String password) {
        navigateTo("/login");
        
        page.fill("#username", username);
        page.fill("#password", password);
        
        takeScreenshot("Login form filled");
        
        page.click("button[type='submit']");
        
        // Wait for navigation to dashboard
        page.waitForURL(BASE_URL + "/dashboard");
        
        takeScreenshot("Login successful");
    }
    
    @Step("Take screenshot: {description}")
    protected void takeScreenshot(String description) {
        byte[] screenshot = page.screenshot(new Page.ScreenshotOptions()
                .setFullPage(true));
        
        Allure.addAttachment(description, "image/png", 
                new java.io.ByteArrayInputStream(screenshot), "png");
    }
    
    @Step("Wait for element: {selector}")
    protected void waitForElement(String selector) {
        page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                .setTimeout(10000));
    }
    
    @Step("Click element: {selector}")
    protected void clickElement(String selector) {
        page.click(selector);
        takeScreenshot("Clicked " + selector);
    }
    
    @Step("Fill input field: {selector} with value: {value}")
    protected void fillInput(String selector, String value) {
        page.fill(selector, value);
        takeScreenshot("Filled " + selector + " with " + value);
    }
    
    @Step("Select option: {value} from dropdown: {selector}")
    protected void selectOption(String selector, String value) {
        page.selectOption(selector, value);
        takeScreenshot("Selected " + value + " from " + selector);
    }
    
    @Step("Verify text is visible: {text}")
    protected void verifyTextVisible(String text) {
        page.waitForSelector("text=" + text, new Page.WaitForSelectorOptions()
                .setTimeout(5000));
        takeScreenshot("Verified text: " + text);
    }
    
    @Step("Verify element is visible: {selector}")
    protected void verifyElementVisible(String selector) {
        page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                .setTimeout(5000));
        takeScreenshot("Verified element visible: " + selector);
    }
    
    @Step("Wait for page load")
    protected void waitForPageLoad() {
        page.waitForLoadState();
    }
}
