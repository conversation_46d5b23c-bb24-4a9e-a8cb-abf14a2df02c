# Server Configuration
server.port=7777
server.servlet.context-path=/gqa

# Database Configuration (H2 in-memory for development)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Logging Configuration
logging.level.graphikos.automation=DEBUG
logging.level.org.springframework.security=DEBUG

# Application Configuration
app.name=Graphikos Automation
app.version=1.0.0
app.description=Internal QA Team Automation Tool

# Automation Configuration
automation.gc.domain-setup=gcautomation
automation.gc.local-url=gcautomation.localzoho.com
automation.gc.build-details-api=show/buildDetails
automation.notification.status=automation
automation.browser=googlechrome
automation.cliq.bot.url=https://cliq.zoho.com/company/*********/api/v2/bots/showreport/incoming
