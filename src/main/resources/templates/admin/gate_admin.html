<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    <title>GATE - Admin Panel</title>
    <link th:href="@{/css/main.css}" rel="stylesheet">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .breadcrumb {
            margin-bottom: 2rem;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .admin-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .admin-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }
        
        .admin-card h2 {
            margin: 0 0 1.5rem 0;
            color: #2c3e50;
            font-size: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }
        
        .user-table th,
        .user-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .user-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .user-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .role-select {
            padding: 0.5rem;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            background-color: white;
            min-width: 120px;
        }
        
        .role-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .test-reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
        
        .report-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .report-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
        }
        
        .report-card p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }
        
        .back-button:hover {
            background-color: #5a6268;
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .current-user-info {
            display: inline-flex;
            align-items: center;
            margin-right: 1rem;
            color: white;
            font-size: 0.9rem;
        }

        .user-name {
            margin-right: 0.5rem;
            font-weight: 600;
        }

        .user-role-badge {
            background: rgba(255,255,255,0.2);
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .user-actions {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
            border: none;
            cursor: pointer;
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
        }
    </style>
</head>
<body class="admin-page" th:data-user-role="${currentUser.role.name()}">
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <img th:src="@{/images/logo.svg}" alt="GATE" class="logo-img">
            </div>

            <div class="user-actions">
                <div class="current-user-info">
                    <span class="user-name">👤 <span th:text="${currentUser.username}">Current User</span></span>
                    <span class="user-role-badge" th:text="'(' + ${currentUser.role.name()} + ')'">Role</span>
                </div>
                <a th:href="@{/logout}" class="logout-btn">🚪 Logout</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-container">
        <!-- Page Title -->
        <h1 style="color: #2c3e50; margin-bottom: 2rem;">Admin Panel</h1>

        <!-- Admin Grid -->
        <div class="admin-grid">
            <!-- User Management Section -->
            <div class="admin-card">
                <h2>User Management</h2>
                <table class="user-table">
                    <thead>
                        <tr>
                            <th>Full Name</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Current Role</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="user : ${users}">
                            <td th:text="${user.fullName}">John Doe</td>
                            <td th:text="${user.username}">john.doe</td>
                            <td th:text="${user.email}"><EMAIL></td>
                            <td>
                                <select class="form-select role-select" th:data-user-id="${user.id}" th:data-original-role="${user.role}">
                                    <option value="EDITOR" th:selected="${user.role.name() == 'EDITOR'}">Editor</option>
                                    <option value="ADMIN" th:selected="${user.role.name() == 'ADMIN'}">Admin</option>
                                    <option value="QA" th:selected="${user.role.name() == 'QA'}">QA</option>
                                    <option value="ULTIMATE" th:selected="${user.role.name() == 'ULTIMATE'}">Ultimate</option>
                                </select>
                            </td>
                            <td>
                                <button class="btn btn-primary btn-sm" onclick="updateUserRole(this)" th:data-user-id="${user.id}">
                                    Update
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <button onclick="saveAllRoleChanges()" class="btn btn-success">Save All Changes</button>
            </div>

            <!-- Add New User Section -->
            <div class="admin-card">
                <h2>Add New User</h2>
                <form id="addUserForm">
                    <div class="form-row">
                        <label class="form-label">Full Name</label>
                        <input type="text" name="fullName" class="form-input" required>
                    </div>
                    <div class="form-row">
                        <label class="form-label">Username</label>
                        <input type="text" name="username" class="form-input" required>
                    </div>
                    <div class="form-row">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-input" required>
                    </div>
                    <div class="form-row">
                        <label class="form-label">Password</label>
                        <input type="password" name="password" class="form-input" required>
                    </div>
                    <div class="form-row">
                        <label class="form-label">Role</label>
                        <select name="role" class="form-select" required>
                            <option value="">Select Role</option>
                            <option value="EDITOR">Editor</option>
                            <option value="QA">QA</option>
                            <option value="ULTIMATE">Ultimate</option>
                            <option value="ADMIN">Admin</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success">Create User</button>
                </form>
            </div>

            <!-- Clear State Section (Ultimate Role Only) -->
            <div class="admin-card" id="clearStateSection" sec:authorize="hasRole('ROLE_ULTIMATE')" style="border-left: 4px solid #dc3545;">
                <h2 style="color: #dc3545;">🔄 Clear Current Workflow State</h2>
                <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; padding: 1rem; margin-bottom: 1rem;">
                    <p style="margin: 0; color: #721c24; font-weight: 600;">⚠️ TARGETED RESET</p>
                    <p style="margin: 0.5rem 0 0 0; color: #721c24; font-size: 0.9rem;">
                        This action will clear current active NIC Check workflow state only. Historical data and completed records will be preserved.
                    </p>
                </div>

                <div style="margin-bottom: 1rem;">
                    <h4 style="color: #495057; margin-bottom: 0.5rem;">This will clear:</h4>
                    <ul style="color: #6c757d; margin: 0; padding-left: 1.5rem;">
                        <li>Current active NIC Check form data</li>
                        <li>Current workflow progress and states</li>
                        <li>Prechecks management data for current session</li>
                        <li>Temporary form validation states</li>
                    </ul>
                    <h4 style="color: #28a745; margin: 1rem 0 0.5rem 0;">This will NOT affect:</h4>
                    <ul style="color: #6c757d; margin: 0; padding-left: 1.5rem;">
                        <li>Historical NIC Check entries in Check Status</li>
                        <li>Completed workflow records in database</li>
                        <li>Other users' collaborative state data</li>
                    </ul>
                </div>

                <button onclick="clearCurrentWorkflowState()" class="btn" style="
                    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                    border: none;
                    color: white;
                    padding: 0.75rem 1.5rem;
                    border-radius: 6px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                ">
                    🔄 Clear Current Workflow State
                </button>

                <p style="margin: 1rem 0 0 0; font-size: 0.8rem; color: #dc3545; font-style: italic;">
                    This action clears current workflow only. Historical data is preserved.
                </p>
            </div>

            <!-- Test Reports Section -->
            <div class="admin-card">
                <h2>Test Reports</h2>
                <div class="test-reports-grid">
                    <a href="/allure-report/nic-check/index.html" target="_blank" class="report-card">
                        <h3>NIC Check Test Report</h3>
                        <p>Complete workflow testing for NIC Checks functionality</p>
                    </a>
                    <a href="/allure-report/update-build/index.html" target="_blank" class="report-card">
                        <h3>Update Build Test Report</h3>
                        <p>Automation configuration and triggering tests</p>
                    </a>
                    <a href="/allure-report/role-based/index.html" target="_blank" class="report-card">
                        <h3>Role-Based UI Test Report</h3>
                        <p>User interface visibility and permission tests</p>
                    </a>
                    <a href="/allure-report/admin/index.html" target="_blank" class="report-card">
                        <h3>Admin Panel Test Report</h3>
                        <p>Admin functionality and user management tests</p>
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script th:src="@{/js/notifications.js}"></script>
    <script th:src="@{/js/admin-page.js}"></script>
</body>
</html>
