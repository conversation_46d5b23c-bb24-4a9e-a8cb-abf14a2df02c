<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Graphikos Automation</title>
    <link rel="stylesheet" th:href="@{/css/main.css}">
    <link rel="stylesheet" th:href="@{/css/responsive.css}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            padding: 1rem 0;
        }
        
        .register-container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 450px;
            margin: 1rem;
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .register-logo-img {
            width: 120px;
            height: 50px;
            margin: 0 auto 1rem;
            display: block;
            border-radius: 6px;
        }
        
        .register-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .register-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .register-btn {
            width: 100%;
            padding: 0.875rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .register-footer {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        
        .register-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .register-footer a:hover {
            text-decoration: underline;
        }
        
        .alert {
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .field-error {
            color: #e74c3c;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <img th:src="@{/images/logo.svg}" alt="GATE" class="register-logo-img">
            <h1 class="register-title">Create Account</h1>
            <p class="register-subtitle">Join GATE - QA Testing Environment</p>
        </div>
        
        <div th:if="${error}" class="alert alert-error">
            <span th:text="${error}">Registration error</span>
        </div>
        
        <form th:action="@{/register}" method="post" th:object="${user}">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" th:field="*{username}" required 
                       placeholder="Choose a username" autocomplete="username">
                <div th:if="${#fields.hasErrors('username')}" class="field-error">
                    <span th:errors="*{username}">Username error</span>
                </div>
            </div>
            
            <div class="form-group">
                <label for="fullName">Full Name</label>
                <input type="text" id="fullName" th:field="*{fullName}" required 
                       placeholder="Enter your full name" autocomplete="name">
                <div th:if="${#fields.hasErrors('fullName')}" class="field-error">
                    <span th:errors="*{fullName}">Full name error</span>
                </div>
            </div>
            
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" th:field="*{email}" required 
                       placeholder="Enter your email" autocomplete="email">
                <div th:if="${#fields.hasErrors('email')}" class="field-error">
                    <span th:errors="*{email}">Email error</span>
                </div>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" th:field="*{password}" required 
                       placeholder="Create a password" autocomplete="new-password">
                <div th:if="${#fields.hasErrors('password')}" class="field-error">
                    <span th:errors="*{password}">Password error</span>
                </div>
            </div>
            
            <div class="form-group">
                <label for="role">Role</label>
                <select id="role" th:field="*{role}" required>
                    <option value="">Select Role</option>
                    <option value="EDITOR">Editor</option>
                    <option value="ADMIN">Admin</option>
                </select>
                <div th:if="${#fields.hasErrors('role')}" class="field-error">
                    <span th:errors="*{role}">Role error</span>
                </div>
            </div>
            
            <button type="submit" class="register-btn">Create Account</button>
        </form>
        
        <div class="register-footer">
            <p>Already have an account? <a th:href="@{/login}">Sign in here</a></p>
        </div>
    </div>
</body>
</html>
