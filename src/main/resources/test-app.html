<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GQA Test Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="email"], select, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="email"]:focus, select:focus, textarea:focus {
            border-color: #4CAF50;
            outline: none;
        }
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        input[type="checkbox"] {
            width: 18px;
            height: 18px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 30px;
        }
        button {
            padding: 12px 30px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        .btn-primary:hover {
            background-color: #45a049;
        }
        .btn-secondary {
            background-color: #f44336;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #da190b;
        }
        .result-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #e8f5e8;
            border-radius: 5px;
            display: none;
        }
        .navigation {
            margin-bottom: 20px;
        }
        .nav-button {
            background-color: #2196F3;
            color: white;
            padding: 10px 20px;
            margin-right: 10px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
        }
        .nav-button:hover {
            background-color: #1976D2;
        }
        .page {
            display: none;
        }
        .page.active {
            display: block;
        }
        .admin-controls {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>GQA Test Application</h1>
        
        <!-- Navigation -->
        <div class="navigation">
            <a href="#" class="nav-button" onclick="showPage('home')">Home</a>
            <a href="#" class="nav-button" onclick="showPage('form')">Form Page</a>
            <a href="#" class="nav-button" onclick="showPage('admin')">Admin Panel</a>
        </div>

        <!-- Home Page -->
        <div id="home" class="page active">
            <h2>Welcome to GQA Test Application</h2>
            <p>This is a multi-page web application designed for automation testing with Playwright.</p>
            <ul>
                <li>Navigate between different pages</li>
                <li>Fill out forms with various input types</li>
                <li>Test dropdown selections and checkboxes</li>
                <li>Admin-controlled prechecks functionality</li>
            </ul>
        </div>

        <!-- Form Page -->
        <div id="form" class="page">
            <h2>User Information Form</h2>
            <form id="userForm">
                <div class="form-group">
                    <label for="firstName">First Name:</label>
                    <input type="text" id="firstName" name="firstName" required>
                </div>

                <div class="form-group">
                    <label for="lastName">Last Name:</label>
                    <input type="text" id="lastName" name="lastName" required>
                </div>

                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="country">Country:</label>
                    <select id="country" name="country" required>
                        <option value="">Select Country</option>
                        <option value="us">United States</option>
                        <option value="uk">United Kingdom</option>
                        <option value="ca">Canada</option>
                        <option value="au">Australia</option>
                        <option value="in">India</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Interests:</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="tech" name="interests" value="technology">
                            <label for="tech">Technology</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="sports" name="interests" value="sports">
                            <label for="sports">Sports</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="music" name="interests" value="music">
                            <label for="music">Music</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="travel" name="interests" value="travel">
                            <label for="travel">Travel</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="comments">Comments:</label>
                    <textarea id="comments" name="comments" rows="4" placeholder="Enter your comments here..."></textarea>
                </div>

                <div class="button-group">
                    <button type="submit" class="btn-primary">Submit</button>
                    <button type="button" class="btn-secondary" onclick="resetForm()">Reset</button>
                </div>
            </form>

            <div id="result" class="result-section">
                <h3>Form Submitted Successfully!</h3>
                <div id="resultContent"></div>
            </div>
        </div>

        <!-- Admin Page -->
        <div id="admin" class="page">
            <h2>Admin Panel</h2>
            
            <div class="admin-controls">
                <h3>Precheck Controls</h3>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="enableValidation" checked>
                        <label for="enableValidation">Enable Form Validation</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="enableSubmission" checked>
                        <label for="enableSubmission">Enable Form Submission</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="enableNotifications">
                        <label for="enableNotifications">Enable Notifications</label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="maxUsers">Maximum Users:</label>
                <input type="text" id="maxUsers" value="100">
            </div>

            <div class="button-group">
                <button type="button" class="btn-primary" onclick="saveAdminSettings()">Save Settings</button>
                <button type="button" class="btn-secondary" onclick="resetAdminSettings()">Reset to Default</button>
            </div>

            <div id="adminResult" class="result-section">
                <h3>Settings Updated!</h3>
                <p>Admin settings have been saved successfully.</p>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));
            
            // Show selected page
            document.getElementById(pageId).classList.add('active');
        }

        function resetForm() {
            document.getElementById('userForm').reset();
            document.getElementById('result').style.display = 'none';
        }

        function saveAdminSettings() {
            const adminResult = document.getElementById('adminResult');
            adminResult.style.display = 'block';
            setTimeout(() => {
                adminResult.style.display = 'none';
            }, 3000);
        }

        function resetAdminSettings() {
            document.getElementById('enableValidation').checked = true;
            document.getElementById('enableSubmission').checked = true;
            document.getElementById('enableNotifications').checked = false;
            document.getElementById('maxUsers').value = '100';
        }

        // Form submission handler
        document.getElementById('userForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {};
            
            // Get regular form fields
            for (let [key, value] of formData.entries()) {
                if (data[key]) {
                    if (Array.isArray(data[key])) {
                        data[key].push(value);
                    } else {
                        data[key] = [data[key], value];
                    }
                } else {
                    data[key] = value;
                }
            }
            
            // Get checkbox values
            const interests = [];
            document.querySelectorAll('input[name="interests"]:checked').forEach(cb => {
                interests.push(cb.value);
            });
            data.interests = interests;
            
            // Display result
            const resultContent = document.getElementById('resultContent');
            resultContent.innerHTML = `
                <p><strong>Name:</strong> ${data.firstName} ${data.lastName}</p>
                <p><strong>Email:</strong> ${data.email}</p>
                <p><strong>Country:</strong> ${data.country}</p>
                <p><strong>Interests:</strong> ${interests.join(', ')}</p>
                <p><strong>Comments:</strong> ${data.comments || 'None'}</p>
            `;
            
            document.getElementById('result').style.display = 'block';
        });
    </script>
</body>
</html>
