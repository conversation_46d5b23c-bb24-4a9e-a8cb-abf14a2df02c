<svg width="120" height="50" viewBox="0 0 120 50" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background Rectangle -->
  <rect x="0" y="0" width="120" height="50" fill="url(#logoGradient)" rx="6"/>

  <!-- Inspection Icon (Magnifying Glass with Checkmark) -->
  <g transform="translate(8, 10)">
    <!-- Magnifying glass circle -->
    <circle cx="12" cy="12" r="8" fill="none" stroke="#fff" stroke-width="2"/>
    <!-- Magnifying glass handle -->
    <line x1="18" y1="18" x2="24" y2="24" stroke="#fff" stroke-width="2" stroke-linecap="round"/>
    <!-- Checkmark inside -->
    <path d="M8 12 L11 15 L16 9" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </g>

  <!-- GATE Text -->
  <text x="40" y="32" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#fff">GATE</text>
</svg>
