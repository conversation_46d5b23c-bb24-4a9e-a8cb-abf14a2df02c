// Single Page Application functionality for Graphikos Automation

document.addEventListener('DOMContentLoaded', function() {
    initializeSPA();
});

function initializeSPA() {
    console.log('🚀 GATE Application initializing...');

    // Initialize navigation handlers
    initializeNavigation();

    // Initialize section-specific functionality
    initializeNicChecksSection();

    // Load initial content
    loadSectionContent('nic-checks');

    // Load any saved workflow state for collaborative functionality (only if valid state exists)
    setTimeout(() => {
        const hasValidState = localStorage.getItem('globalWorkflowState') || localStorage.getItem('currentWorkflowState');
        if (hasValidState) {
            console.log('📋 Loading saved workflow state...');
            loadWorkflowState();
        } else {
            console.log('📋 No saved state found - starting fresh');
            // Ensure build form is enabled for fresh start
            ensureBuildFormEnabled();
            // Ensure release type starts empty
            ensureReleaseTypeEmpty();
        }
    }, 1000);

    // Set up auto-save for collaborative functionality
    setupAutoSave();

    // Set up broadcast channel listeners for state clearing
    setupBroadcastChannels();

    // Initialize enhanced form state management
    initializeEnhancedFormStateManagement();

    console.log('✅ GATE Application initialized successfully');
}

// Setup Auto-Save for Collaborative Functionality
function setupAutoSave() {
    console.log('🔄 Setting up auto-save for collaborative functionality');

    // Auto-save every 30 seconds if there are changes
    setInterval(() => {
        const currentBuildId = sessionStorage.getItem('currentBuildId');
        if (currentBuildId) {
            saveWorkflowState();
        }
    }, 30000);

    // Save on form changes
    document.addEventListener('input', function(e) {
        if (e.target.matches('input, select, textarea')) {
            // Debounce the save operation
            clearTimeout(window.autoSaveTimeout);
            window.autoSaveTimeout = setTimeout(() => {
                const currentBuildId = sessionStorage.getItem('currentBuildId');
                if (currentBuildId) {
                    saveWorkflowState();
                    console.log('💾 Auto-saved workflow state due to form changes');
                }
            }, 2000);
        }
    });

    // Save on checkbox changes
    document.addEventListener('change', function(e) {
        if (e.target.matches('input[type="checkbox"]')) {
            const currentBuildId = sessionStorage.getItem('currentBuildId');
            if (currentBuildId) {
                saveWorkflowState();
                console.log('💾 Auto-saved workflow state due to checkbox changes');
            }
        }
    });

    console.log('✅ Auto-save setup completed');
}

// Enhanced Form State Management
function initializeEnhancedFormStateManagement() {
    console.log('🔧 Initializing enhanced form state management');

    // Monitor form interactions to disable previous sections
    document.addEventListener('input', function(e) {
        if (e.target.matches('input, select, textarea')) {
            handleFormInteraction(e.target);
        }
    });

    document.addEventListener('change', function(e) {
        if (e.target.matches('input[type="checkbox"], select')) {
            handleFormInteraction(e.target);
        }
    });

    console.log('✅ Enhanced form state management initialized');
}

// Handle Form Interaction
function handleFormInteraction(element) {
    const currentBuildId = sessionStorage.getItem('currentBuildId');
    if (!currentBuildId) return;

    // Determine which section the element belongs to
    const section = element.closest('.form-section, form');
    if (!section) return;

    const sectionId = section.id;

    // Disable previous sections based on current section
    switch (sectionId) {
        case 'buildForm':
            disablePreviousFormsUpTo('releaseType');
            break;
        case 'automationSection':
            disablePreviousFormsUpTo('buildForm');
            break;
        case 'automationReportSection':
            disablePreviousFormsUpTo('automationSection');
            break;
        case 'manualTestcaseSection':
            disablePreviousFormsUpTo('automationReportSection');
            break;
        case 'preBuildUpdateSection':
            disablePreviousFormsUpTo('manualTestcaseSection');
            break;
        case 'preBuildVerificationSection':
            disablePreviousFormsUpTo('preBuildUpdateSection');
            break;
        case 'liveBuildUpdateSection':
            disablePreviousFormsUpTo('preBuildVerificationSection');
            break;
        case 'sanityInLiveSection':
            disablePreviousFormsUpTo('liveBuildUpdateSection');
            break;
    }

    // Save state after interaction
    saveWorkflowState();
}

// Disable Previous Forms Up To Specified Section
function disablePreviousFormsUpTo(currentSectionId) {
    const sectionOrder = [
        'releaseType',
        'buildForm',
        'automationSection',
        'automationReportSection',
        'manualTestcaseSection',
        'preBuildUpdateSection',
        'preBuildVerificationSection',
        'liveBuildUpdateSection',
        'sanityInLiveSection'
    ];

    const currentIndex = sectionOrder.indexOf(currentSectionId);
    if (currentIndex === -1) return;

    // Disable all sections before current
    for (let i = 0; i < currentIndex; i++) {
        const sectionId = sectionOrder[i];
        disableSection(sectionId);
    }

    console.log(`🔒 Disabled sections up to: ${currentSectionId}`);
}

// Disable Specific Section
function disableSection(sectionId) {
    if (sectionId === 'releaseType') {
        const releaseTypeSelect = document.getElementById('releaseType');
        if (releaseTypeSelect) {
            releaseTypeSelect.disabled = true;
            releaseTypeSelect.classList.add('form-disabled');
        }
        return;
    }

    const section = document.getElementById(sectionId);
    if (!section) return;

    // Disable all inputs in the section
    const inputs = section.querySelectorAll('input, select, textarea, button');
    inputs.forEach(input => {
        input.disabled = true;
        input.classList.add('form-disabled');
    });

    // Add visual indicator
    section.classList.add('section-disabled');
}

// Reset Form State for New Workflow
function resetFormStateForNewWorkflow() {
    console.log('🔄 Resetting form state for new workflow');

    // Enable all forms
    enableAllForms();

    // Remove disabled classes
    const disabledElements = document.querySelectorAll('.form-disabled, .section-disabled');
    disabledElements.forEach(element => {
        element.classList.remove('form-disabled', 'section-disabled');
    });

    console.log('✅ Form state reset for new workflow');
}

// Ensure Build Form is Enabled (for fresh starts)
function ensureBuildFormEnabled() {
    console.log('🔓 Ensuring build form is enabled for fresh start...');

    // Enable all form inputs in build form
    const buildForm = document.getElementById('buildForm');
    if (buildForm) {
        const inputs = buildForm.querySelectorAll('input, select, textarea, button');
        inputs.forEach(input => {
            input.disabled = false;
            input.classList.remove('form-disabled');
        });

        // Remove any disabled classes from the form itself
        buildForm.classList.remove('section-disabled');

        // Remove any lock indicators
        const lockMessages = buildForm.querySelectorAll('.section-lock-message');
        lockMessages.forEach(msg => msg.remove());

        console.log('✅ Build form enabled for fresh start');
    }

    // Also ensure release type dropdown is enabled
    const releaseTypeSelect = document.getElementById('releaseType');
    if (releaseTypeSelect) {
        releaseTypeSelect.disabled = false;
        releaseTypeSelect.classList.remove('form-disabled');
        console.log('✅ Release type dropdown enabled');
    }
}

// Ensure Release Type Starts Empty (for fresh starts)
function ensureReleaseTypeEmpty() {
    console.log('🔄 Ensuring release type dropdown starts empty...');

    const releaseTypeSelect = document.getElementById('releaseType');
    if (releaseTypeSelect) {
        // Force empty value regardless of any cached state
        releaseTypeSelect.value = '';
        releaseTypeSelect.selectedIndex = 0; // Select first option (empty)

        // Hide build form if it was showing
        const buildFormContainer = document.getElementById('buildFormContainer');
        if (buildFormContainer) {
            buildFormContainer.style.display = 'none';
        }

        console.log('✅ Release type dropdown reset to empty state');
    }
}

// Comprehensive Form Reset After Submission
function resetFormAfterSubmission() {
    console.log('🔄 Performing comprehensive form reset after submission...');

    try {
        // 1. Clear all input fields
        const allInputs = document.querySelectorAll('input[type="text"], input[type="url"], input[type="email"], textarea');
        allInputs.forEach(input => {
            input.value = '';
            input.classList.remove('error', 'success');
        });

        // 2. Reset all dropdowns to default empty state
        const allSelects = document.querySelectorAll('select');
        allSelects.forEach(select => {
            select.selectedIndex = 0; // Select first option (usually empty)
            select.classList.remove('error', 'success');
        });

        // 3. Uncheck all checkboxes
        const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
        allCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // 4. Hide conditional sections
        hideAllConditionalSections();

        // 5. Clear state storage
        localStorage.removeItem('currentWorkflowState');
        localStorage.removeItem('globalWorkflowState');
        sessionStorage.clear();

        // 6. Reset validation states
        const allFormElements = document.querySelectorAll('.form-input, .form-select, .form-control');
        allFormElements.forEach(element => {
            element.classList.remove('error', 'success', 'form-disabled');
        });

        // 7. Hide workflow sections
        const workflowSections = document.querySelectorAll('.workflow-step, .status-area');
        workflowSections.forEach(section => {
            section.style.display = 'none';
        });

        console.log('✅ Comprehensive form reset completed');

    } catch (error) {
        console.error('❌ Error during form reset:', error);
    }
}

// Setup Broadcast Channels for Cross-Tab Communication
function setupBroadcastChannels() {
    if (typeof BroadcastChannel !== 'undefined') {
        // Listen for state clearing events
        const adminChannel = new BroadcastChannel('gate-admin');
        adminChannel.onmessage = function(event) {
            if (event.data.type === 'STATE_CLEARED') {
                console.log('📢 Received state clearing broadcast');
                // Clear local state immediately
                localStorage.clear();
                sessionStorage.clear();
                // Show notification
                showNotification('🔄 Application state cleared by administrator', 'info');
                // Reload after delay
                setTimeout(() => {
                    window.location.href = '/gqa?cleared=true';
                }, 1000);
            }
        };

        // Listen for force reload events
        const reloadChannel = new BroadcastChannel('gate-reload');
        reloadChannel.onmessage = function(event) {
            if (event.data.type === 'FORCE_RELOAD') {
                console.log('📢 Received force reload broadcast');
                window.location.reload();
            }
        };

        console.log('📡 Broadcast channels set up for cross-tab communication');
    }
}

// Reset NIC Check Flow (Ultimate Role Only)
function resetNicCheckFlow() {
    const userRole = document.body.dataset.userRole;

    // Only allow Ultimate role to reset
    if (userRole !== 'ULTIMATE') {
        showNotification('Only Ultimate role users can reset the NIC Check flow', 'error');
        return;
    }

    // Show confirmation dialog
    const confirmed = confirm(
        '⚠️ RESET NIC CHECK FLOW\n\n' +
        'This action will:\n' +
        '• Clear all current workflow progress\n' +
        '• Reset all form fields and checkboxes\n' +
        '• Remove all saved state data\n' +
        '• Start the workflow from scratch\n\n' +
        'This action cannot be undone. Are you sure you want to continue?'
    );

    if (!confirmed) {
        return;
    }

    console.log('🔄 Resetting NIC Check flow by Ultimate user');

    try {
        console.log('🔄 Starting comprehensive NIC Check flow reset...');

        // Clear ALL localStorage data (not just specific keys)
        const localStorageKeys = Object.keys(localStorage);
        console.log('📋 Clearing localStorage keys:', localStorageKeys);
        localStorage.clear();

        // Clear ALL sessionStorage data
        const sessionStorageKeys = Object.keys(sessionStorage);
        console.log('📋 Clearing sessionStorage keys:', sessionStorageKeys);
        sessionStorage.clear();

        // Reset all form fields
        const allInputs = document.querySelectorAll('input, textarea, select');
        console.log('📋 Resetting', allInputs.length, 'form elements');
        allInputs.forEach(input => {
            if (input.type === 'checkbox' || input.type === 'radio') {
                input.checked = false;
            } else {
                input.value = '';
            }

            // Re-enable disabled inputs
            input.disabled = false;
            input.classList.remove('form-disabled');
        });

        // Hide all conditional sections
        hideAllConditionalSections();

        // Remove section disabled classes
        const disabledSections = document.querySelectorAll('.section-disabled');
        disabledSections.forEach(section => {
            section.classList.remove('section-disabled');
        });

        // Remove lock indicators
        const lockIndicators = document.querySelectorAll('.lock-indicator, .section-lock-message');
        lockIndicators.forEach(indicator => {
            indicator.remove();
        });

        // Reset workflow steps
        const workflowSteps = document.querySelectorAll('.workflow-step');
        workflowSteps.forEach(step => {
            step.classList.remove('completed', 'active');
        });

        // Clear any cached data or temporary variables
        if (window.workflowData) {
            window.workflowData = null;
        }

        // Show success notification
        showNotification('🔄 NIC Check flow has been completely reset. Starting fresh!', 'success');

        console.log('✅ Comprehensive NIC Check flow reset completed');

        // Reload the page to ensure completely clean state
        setTimeout(() => {
            window.location.href = '/gqa?reset=true';
        }, 2000);

    } catch (error) {
        console.error('❌ Error resetting NIC Check flow:', error);
        showNotification('Error occurred while resetting. Please refresh the page.', 'error');
    }
}

// Lock Earlier Sections (Controlled Section Visibility)
function lockEarlierSections(sectionsToLock) {
    console.log('🔒 Locking earlier sections:', sectionsToLock);

    sectionsToLock.forEach(sectionId => {
        if (sectionId === 'releaseType') {
            const releaseTypeSelect = document.getElementById('releaseType');
            if (releaseTypeSelect) {
                releaseTypeSelect.disabled = true;
                releaseTypeSelect.classList.add('form-disabled');

                // Add lock indicator
                const lockIndicator = document.createElement('span');
                lockIndicator.className = 'lock-indicator';
                lockIndicator.innerHTML = '🔒 Locked';
                lockIndicator.style.cssText = 'margin-left: 10px; color: #6c757d; font-size: 0.8rem;';

                if (!releaseTypeSelect.parentNode.querySelector('.lock-indicator')) {
                    releaseTypeSelect.parentNode.appendChild(lockIndicator);
                }
            }
        } else if (sectionId === 'buildForm') {
            const buildForm = document.getElementById('buildForm');
            if (buildForm) {
                // Disable all form inputs
                const inputs = buildForm.querySelectorAll('input, select, textarea, button');
                inputs.forEach(input => {
                    input.disabled = true;
                    input.classList.add('form-disabled');
                });

                // Add section lock indicator
                buildForm.classList.add('section-disabled');

                // Add lock message
                const lockMessage = document.createElement('div');
                lockMessage.className = 'section-lock-message';
                lockMessage.innerHTML = '🔒 Build form locked - workflow in progress';
                lockMessage.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    background: rgba(108, 117, 125, 0.9);
                    color: white;
                    padding: 0.5rem;
                    border-radius: 4px;
                    font-size: 0.8rem;
                    z-index: 10;
                `;

                if (!buildForm.querySelector('.section-lock-message')) {
                    buildForm.style.position = 'relative';
                    buildForm.appendChild(lockMessage);
                }
            }
        } else if (sectionId === 'automationSection') {
            const automationSection = document.getElementById('automationSection');
            if (automationSection) {
                // Disable all form inputs
                const inputs = automationSection.querySelectorAll('input, select, textarea, button');
                inputs.forEach(input => {
                    input.disabled = true;
                    input.classList.add('form-disabled');
                });

                // Add section lock indicator
                automationSection.classList.add('section-disabled');

                // Add lock message
                const lockMessage = document.createElement('div');
                lockMessage.className = 'section-lock-message';
                lockMessage.innerHTML = '🔒 Automation section locked - automation in progress';
                lockMessage.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    background: rgba(108, 117, 125, 0.9);
                    color: white;
                    padding: 0.5rem;
                    border-radius: 4px;
                    font-size: 0.8rem;
                    z-index: 10;
                `;

                if (!automationSection.querySelector('.section-lock-message')) {
                    automationSection.style.position = 'relative';
                    automationSection.appendChild(lockMessage);
                }
            }
        }
    });

    console.log('✅ Earlier sections locked successfully');
}

function initializeNavigation() {
    // Handle navigation clicks
    document.querySelectorAll('.nav-link, .action-btn, .table-action').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const section = this.dataset.section;
            if (section) {
                switchToSection(section);
                
                // Update active nav link
                document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
                document.querySelectorAll(`.nav-link[data-section="${section}"]`).forEach(nav => nav.classList.add('active'));
            }
        });
    });
}

function switchToSection(sectionName) {
    console.log(`🔄 Attempting to switch to section: ${sectionName}`);

    // Check role-based access control
    if (!checkSectionAccess(sectionName)) {
        console.log(`❌ Access denied for section: ${sectionName}`);
        return;
    }

    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });

    // Show target section
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');

        // Load section content if needed
        loadSectionContent(sectionName);

        console.log(`✅ Successfully switched to section: ${sectionName}`);
    } else {
        console.error(`❌ Target section not found: ${sectionName}-section`);
    }
}

function loadSectionContent(sectionName) {
    switch(sectionName) {
        case 'dashboard':
            loadDashboardContent();
            break;
        case 'nic-checks':
            loadNicChecksContent();
            break;
        case 'update-build':
            loadUpdateBuildContent();
            break;
        case 'check-status':
            loadCheckStatusContent();
            break;
    }
}

function loadDashboardContent() {
    const section = document.getElementById('dashboard-section');
    if (section.dataset.loaded) return;

    const content = `
        <div class="dashboard-container">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">BUILDS</div>
                    <div class="stat-content">
                        <h3 class="stat-number">12</h3>
                        <p class="stat-label">Total Builds</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">QUEUE</div>
                    <div class="stat-content">
                        <h3 class="stat-number">3</h3>
                        <p class="stat-label">In Queue</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">ACTIVE</div>
                    <div class="stat-content">
                        <h3 class="stat-number">2</h3>
                        <p class="stat-label">Running</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">DONE</div>
                    <div class="stat-content">
                        <h3 class="stat-number">7</h3>
                        <p class="stat-label">Completed</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2 class="section-title">Quick Actions</h2>
                <div class="action-buttons">
                    <a href="#" class="action-btn primary" data-section="nic-checks">
                        <div class="action-icon">CHECK</div>
                        <div class="action-content">
                            <h3>Start NIC Checks</h3>
                            <p>Begin a new build validation process</p>
                        </div>
                    </a>

                    <a href="#" class="action-btn secondary" data-section="update-build">
                        <div class="action-icon">BUILD</div>
                        <div class="action-content">
                            <h3>Update Build</h3>
                            <p>Configure and trigger automation</p>
                        </div>
                    </a>

                    <a href="#" class="action-btn tertiary" data-section="check-status">
                        <div class="action-icon">STATUS</div>
                        <div class="action-content">
                            <h3>Check Status</h3>
                            <p>View all build summaries</p>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="recent-activity">
                <h2 class="section-title">Recent Activity</h2>
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon">BUILD</div>
                        <div class="activity-content">
                            <h4>NIC Check Completed</h4>
                            <p>Build HF-2024-001 passed all validations</p>
                            <span class="activity-time">2 hours ago</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">AUTO</div>
                        <div class="activity-content">
                            <h4>Automation Started</h4>
                            <p>Test suite running for Release-2024-Q1</p>
                            <span class="activity-time">4 hours ago</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">APPROVE</div>
                        <div class="activity-content">
                            <h4>Admin Approval</h4>
                            <p>Build diff approved by System Administrator</p>
                            <span class="activity-time">6 hours ago</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    section.innerHTML = section.querySelector('.page-header').outerHTML + content;
    section.dataset.loaded = 'true';

    // Reattach event listeners to dashboard action buttons
    section.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionName = this.dataset.section;
            if (sectionName) {
                switchToSection(sectionName);
                // Update active nav link
                document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
                document.querySelectorAll(`.nav-link[data-section="${sectionName}"]`).forEach(nav => nav.classList.add('active'));
            }
        });
    });
}

function generateAutomationConfigForm() {
    return `
        <div class="automation-config-form">
            <h3 class="section-title">Automation Configuration</h3>

            <!-- GC Launch Configurations -->
            <div class="config-group">
                <h4 class="config-group-title">GC Launch Configurations</h4>
                <div class="form-row">
                    <label class="form-label">Domain Setup</label>
                    <input type="text" name="domainSetup" value="" class="form-input" placeholder="Enter domain setup">
                </div>
                <div class="form-row">
                    <label class="form-label">Local URL</label>
                    <input type="text" name="localUrl" value="" class="form-input" placeholder="Enter local URL">
                </div>
                <div class="form-row">
                    <label class="form-label">Build Details API</label>
                    <input type="text" name="buildDetailsAPI" value="" class="form-input" placeholder="Enter build details API">
                </div>
            </div>

            <!-- Report and Account Configurations -->
            <div class="config-group">
                <h4 class="config-group-title">Report and Account Configurations</h4>
                <div class="form-row">
                    <label class="form-label">Local Server Machine Name</label>
                    <input type="text" name="localServerMachineName" value="" class="form-input">
                </div>
            </div>

            <!-- Notification Configurations -->
            <div class="config-group">
                <h4 class="config-group-title">Notification Configurations</h4>
                <div class="form-row">
                    <label class="form-label">Automation Status</label>
                    <input type="text" name="automationStatus" value="" class="form-input" placeholder="Enter automation status">
                    <small class="form-help">Note: if debug, use "debug", else keep "automation"</small>
                </div>
            </div>

            <!-- Cliq Bot Properties -->
            <div class="config-group">
                <h4 class="config-group-title">Cliq Bot Properties</h4>
                <div class="form-row">
                    <label class="form-label">Tomcat Path</label>
                    <input type="text" name="tomcatPath" value="" class="form-input">
                </div>
                <div class="form-row">
                    <label class="form-label">Report Subject For Bot</label>
                    <input type="text" name="reportSubjectForBot" value="" class="form-input" placeholder="Enter report subject">
                </div>
                <div class="form-row">
                    <label class="form-label">Send Report In Bot</label>
                    <input type="text" name="sendReportInBot" value="" class="form-input" placeholder="Enter yes/no">
                </div>
                <div class="form-row">
                    <label class="form-label">Cliq Report Bot URL</label>
                    <input type="text" name="cliqReportBotURL" value="" class="form-input" placeholder="Enter Cliq report bot URL">
                </div>
            </div>

            <!-- Browser Configurations -->
            <div class="config-group">
                <h4 class="config-group-title">Browser Configurations</h4>
                <div class="form-row">
                    <label class="form-label">Browser</label>
                    <select name="browser" class="form-select">
                        <option value="">Select Browser</option>
                        <option value="googlechrome">Google Chrome</option>
                        <option value="chromeheadless">Chrome Headless</option>
                        <option value="firefox">Firefox</option>
                        <option value="edge">Edge</option>
                        <option value="internetexplorer">Internet Explorer</option>
                        <option value="safari">Safari</option>
                        <option value="phantomj">PhantomJS</option>
                    </select>
                </div>
                <div class="form-row checkbox-row">
                    <label class="checkbox-label">
                        <input type="checkbox" name="addExtension">
                        Add Extension
                    </label>
                </div>
                <div class="form-row checkbox-row">
                    <label class="checkbox-label">
                        <input type="checkbox" name="idc">
                        IDC
                    </label>
                </div>
                <div class="form-row checkbox-row">
                    <label class="checkbox-label">
                        <input type="checkbox" name="gridEnabled">
                        Grid Enabled
                    </label>
                </div>
                <div class="form-row checkbox-row">
                    <label class="checkbox-label">
                        <input type="checkbox" name="enableConsoleLogs" checked>
                        Enable Console Logs
                    </label>
                </div>
                <div class="form-row checkbox-row">
                    <label class="checkbox-label">
                        <input type="checkbox" name="enableBiDiLogging" checked>
                        Enable BiDi Logging
                    </label>
                </div>
                <div class="form-row checkbox-row">
                    <label class="checkbox-label">
                        <input type="checkbox" name="testOpVideo" checked>
                        Test Op Video
                    </label>
                </div>
            </div>

            <div class="form-row">
                <button type="button" class="btn btn-success" onclick="startAutomation()">Start Automation</button>
            </div>
        </div>
    `;
}

function loadNicChecksContent() {
    const section = document.getElementById('nic-checks-section');
    if (section.dataset.loaded) return;
    
    const content = `
        <div id="mainContent">
            <!-- Left Panel - Workflow -->
            <div class="left-panel">
                <!-- Release Type Selection -->
                <div class="form-container">
                    <div class="form-row">
                        <label for="releaseType" class="form-label">Release Type</label>
                        <select id="releaseType" name="releaseType" class="form-select" required>
                            <option value="">Select Release Type</option>
                            <option value="HF">HF</option>
                            <option value="Release">Release</option>
                        </select>
                    </div>
                </div>

                <!-- Build Form (Hidden initially) -->
                <div class="form-container" id="buildFormContainer" style="display: none;">
                    <form id="buildForm" method="POST" action="/gqa/build-update-process">
                        <div class="form-row">
                            <label class="form-label">GC <span class="required">*</span></label>
                            <input type="text" name="buildsetup" id="gcInput" class="form-input" placeholder="Branch or Build url" required>
                        </div>

                        <div class="form-row">
                            <label class="form-label">Zoho Show <span class="required">*</span></label>
                            <div class="input-with-checkbox">
                                <input type="text" name="zohoshowinput" id="zohoshowInput" class="form-input" placeholder="Build URL or Branch Name" required>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="applyBranchToAllCheckbox" class="checkbox-input">
                                    Apply same branch name to all sub-products
                                </label>
                            </div>
                        </div>

                        <div class="form-row">
                            <label class="form-label">Client Sub-Products</label>
                            <div class="form-inputs" id="clientSubProducts">
                                <input type="text" name="shapeframework" class="form-input client-input" placeholder="shapeframework">
                                <input type="text" name="graphikosmedia" class="form-input client-input" placeholder="graphikosmedia">
                                <input type="text" name="showrenderingframework" class="form-input client-input" placeholder="showrenderingframework">
                                <input type="text" name="graphikosi18n" class="form-input client-input" placeholder="graphikosi18n">
                                <input type="text" name="showlistingdialog" class="form-input client-input" placeholder="showlistingdialog">
                                <input type="text" name="showrightpanel" class="form-input client-input" placeholder="showrightpanel">
                                <input type="text" name="showslideshowviews" class="form-input client-input" placeholder="showslideshowviews">
                                <input type="text" name="showui" class="form-input client-input" placeholder="showui">
                                <input type="text" name="showoffline" class="form-input client-input" placeholder="showoffline">
                            </div>
                        </div>
                    
                    <div class="form-row">
                        <label class="form-label">Server</label>
                        <div class="combo-row">
                            <input type="text" name="showserver" class="form-input" placeholder="">
                            <label>Auto Build Update <input type="checkbox" name="enableAutobuildUpdate" class="checkbox-row"></label>
                            <select name="Zohoshow" class="form-select" style="display: none;">
                                <option value="showclient">showclient</option>
                                <option value="showclient1">showclient1</option>
                                <option value="showclient2">showclient2</option>
                                <option value="showweb">showweb</option>
                                <option value="showz">showz</option>
                                <option value="ShowTest1">showtest1</option>
                                <option value="ShowTest2">showtest2</option>
                                <option value="SHOWAUTOMATION1">showautomation1</option>
                                <option value="showrelease">showrelease</option>
                                <option value="Show3">show3</option>
                                <option value="GC_AUTOMATION">GC_AUTOMATION</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <label class="form-label">Conversion</label>
                        <div class="combo-row">
                            <input type="text" name="conversion" class="form-input" placeholder="">
                            <label>Auto Build Update <input type="checkbox" name="conversionAutoBuildUpdate" class="checkbox-row"></label>
                            <select name="conversion" class="form-select" style="display: none;">
                                <option value="Showconversion">Showconversion</option>
                                <option value="Conversion_test">Conversion_test</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <label class="form-label">Pictures</label>
                        <div class="combo-row">
                            <input type="text" name="pictures" class="form-input" placeholder="">
                            <label>Auto Build Update <input type="checkbox" name="picturesAutoBuildUpdate" class="checkbox-row"></label>
                            <select name="pictures" class="form-select" style="display: none;">
                                <option value="Showpictures">Showpictures</option>
                                <option value="pictures_test">pictures_test</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <label class="form-label">Image Conversion</label>
                        <div class="combo-row">
                            <input type="text" name="imageconversion" class="form-input" placeholder="">
                            <label>Auto Build Update <input type="checkbox" name="imageconversionAutoBuildUpdate" class="checkbox-row"></label>
                            <select name="imageconversion" class="form-select" style="display: none;">
                                <option value="imageconversion">imageconversion</option>
                            </select>
                        </div>
                    </div>

                        <div class="form-row">
                            <label class="form-label">Release Notes</label>
                            <textarea name="releaseNotes" id="releaseNotesInput" class="form-control" rows="3" placeholder="Enter release notes..."></textarea>
                        </div>

                        <div class="form-row">
                            <label class="form-label"></label>
                            <button type="submit" id="submitBuildBtn" class="btn btn-primary" disabled>Submit Build</button>
                        </div>
                    </form>
                </div>

                <!-- Build Status Area -->
                <div class="status-area" style="display: none;">
                    <div class="status-title">Build Status</div>
                    <div class="status-content" id="buildStatus">Waiting for build submission...</div>
                </div>

                <!-- Automation Configuration Section -->
                <div id="automationSection" class="form-section" style="display: none;">
                    ${generateAutomationConfigForm()}
                </div>

                <!-- Additional sections for automation workflow -->
                <div id="automationReportSection" class="form-section" style="display: none;">
                    <h3 class="section-title">Automation Report Confirmation</h3>

                    <!-- Sample Automation Report -->
                    <div class="sample-report-container">
                        <h4>Sample Automation Report</h4>
                        <div class="form-row">
                            <label class="form-label">Automation Report URL:</label>
                            <input type="text" id="automationReportUrl" class="form-input"
                                   placeholder="Enter automation report URL..." />
                        </div>
                        <div class="sample-report">
                            <p><strong>Test Execution Summary:</strong></p>
                            <ul>
                                <li>Total Tests: 25</li>
                                <li>Passed: 23</li>
                                <li>Failed: 2</li>
                                <li>Success Rate: 92%</li>
                                <li>Duration: 15m 32s</li>
                            </ul>
                            <p><strong>Failed Tests:</strong></p>
                            <ul>
                                <li>Login validation test - Timeout error</li>
                                <li>Database connection test - Connection refused</li>
                            </ul>
                        </div>
                        <label class="checkbox-label">
                            <input type="checkbox" id="reportReceivedCheckbox">
                            Confirm Automation Report Received
                        </label>
                    </div>

                    <!-- Manual Sanity Report -->
                    <div class="manual-sanity-container">
                        <h4>Manual Sanity Report</h4>
                        <div class="form-row">
                            <label class="form-label">Manual Sanity Sheet URL:</label>
                            <input type="text" id="manualSanitySheetUrl" class="form-input"
                                   placeholder="Enter manual sanity sheet URL..." />
                        </div>
                        <textarea id="manualSanityReport" class="form-control" rows="4"
                                  placeholder="Enter manual sanity test results and observations...">Sample Manual Sanity Report:

✅ Login functionality verified across all user roles
✅ Build form validation working correctly
✅ Automation triggers functioning properly
⚠️ Minor UI alignment issue in Firefox browser
✅ Database connections stable
✅ All critical workflows operational</textarea>
                        <label class="checkbox-label">
                            <input type="checkbox" id="manualSanityCheckbox">
                            Confirm Manual Sanity Report
                        </label>
                    </div>
                </div>

                <div id="manualTestcaseSection" class="form-section" style="display: none;">
                    <div class="form-row">
                        <label class="checkbox-label">
                            <input type="checkbox" id="manualTestcaseCheckbox">
                            Creator NIC Form Completed
                        </label>
                    </div>
                    <div class="form-row">
                        <label class="form-label">Creator NIC Form URL:</label>
                        <input type="text" id="creatorNicFormUrl" class="form-input"
                               placeholder="Enter Creator NIC Form URL..." />
                    </div>
                </div>



                <div id="preBuildUpdateSection" class="form-section" style="display: none;">
                    <button onclick="updateBuildInPre()" class="btn btn-warning">Update Build in Pre</button>
                    <div class="notification-text">Build is started and once completed automation will be triggered</div>
                </div>



                <div id="preBuildVerificationSection" class="form-section" style="display: none;">
                    <h3 class="section-title">Pre-Build Verification</h3>
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="preSanityCheckbox">
                            Pre Sanity
                        </label>
                        <div class="form-row">
                            <label class="form-label">Pre Sanity Details:</label>
                            <input type="text" id="preSanityDetails" class="form-input"
                                   placeholder="Enter pre sanity details..." />
                        </div>

                        <label class="checkbox-label">
                            <input type="checkbox" id="preAutomationCheckbox">
                            Pre Automation
                        </label>
                        <div class="form-row">
                            <label class="form-label">Pre Automation Details:</label>
                            <input type="text" id="preAutomationDetails" class="form-input"
                                   placeholder="Enter pre automation details..." />
                        </div>
                    </div>
                </div>

                <div id="liveBuildUpdateSection" class="form-section" style="display: none;">
                    <h3 class="section-title">Update to Live</h3>
                    <div class="final-step-container">
                        <p class="completion-message">🎉 All pre-checks completed! Ready to update to Live environment.</p>
                        <button onclick="updateToLive()" class="btn btn-success btn-lg" id="updateToLiveBtn">
                            Update to Live
                        </button>
                    </div>
                </div>

                <div id="sanityInLiveSection" class="form-section" style="display: none;">
                    <h3 class="section-title">Sanity in Live</h3>
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="sanityInLiveCheckbox">
                            Sanity in Live Completed
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="milestoneCheckbox">
                            Milestone
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="reverseMergeCheckbox">
                            Reverse Merge
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="learnDocCheckbox">
                            Learn Doc
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="connectPostCheckbox">
                            Connect Post
                        </label>
                    </div>
                </div>

                <div id="completeReleaseSection" class="form-section" style="display: none;">
                    <h3 class="section-title">Complete Release</h3>
                    <div class="final-step-container">
                        <p class="completion-message">🎉 All steps completed! Ready to finalize the release.</p>
                        <button onclick="completeRelease()" class="btn btn-primary btn-lg" id="completeReleaseBtn">
                            Complete Release
                        </button>
                    </div>
                </div>

                <div id="liveBuildUpdateSection" class="form-section" style="display: none;">
                    <button onclick="updateBuildToLive()" id="updateToLiveBtn" class="btn btn-danger">Update Build to Live</button>
                    <div class="build-status-label">
                        <strong>Build Update Status: </strong>
                        <span id="liveUpdateStatus">Pending</span>
                    </div>
                </div>

                <div id="finalSanitySection" class="form-section" style="display: none;">
                    <h3 class="section-title">Final Sanity Check</h3>
                    <div class="form-row">
                        <label class="form-label">Sanity URL</label>
                        <input type="text" id="sanityUrl" placeholder="Enter sanity URL">
                        <label class="checkbox-label">
                            <input type="checkbox" id="finalSanityCheckbox">
                            Final Sanity Completed
                        </label>
                    </div>
                    <button onclick="completeBuild()" id="completeButton" class="btn btn-success" style="display: none;">Complete</button>
                </div>
            </div>

            <!-- Right Panel - Prechecks Management -->
            <div class="right-panel" id="prechecksPanelAdmin">
                <h3 class="prechecks-title">Prechecks Management</h3>

                <!-- SD Build Diff -->
                <div class="precheck-container">
                    <label class="precheck-label">SD Build Diff</label>
                    <textarea id="sdBuildDiffContent" class="precheck-textarea" rows="4"
                              placeholder="Enter SD build diff information..."></textarea>
                    <div class="precheck-checkboxes">
                        <label class="checkbox-label">
                            <input type="checkbox" id="sdBuildDiff_qa" class="precheck-admin-checkbox">
                            QA
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="sdBuildDiff_server" class="precheck-admin-checkbox">
                            Server
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="sdBuildDiff_client" class="precheck-admin-checkbox">
                            Client
                        </label>
                    </div>
                </div>

                <!-- Change File Diff -->
                <div class="precheck-container">
                    <label class="precheck-label">Change File Diff</label>
                    <textarea id="changeFileDiffContent" class="precheck-textarea" rows="4"
                              placeholder="Enter change file diff information..."></textarea>
                    <div class="precheck-checkboxes">
                        <label class="checkbox-label">
                            <input type="checkbox" id="changeFileDiff_qa" class="precheck-admin-checkbox">
                            QA
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="changeFileDiff_server" class="precheck-admin-checkbox">
                            Server
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="changeFileDiff_client" class="precheck-admin-checkbox">
                            Client
                        </label>
                    </div>
                </div>

                <!-- ZDCM Diff -->
                <div class="precheck-container">
                    <label class="precheck-label">ZDCM Diff</label>
                    <textarea id="zdcmDiffContent" class="precheck-textarea" rows="4"
                              placeholder="Enter ZDCM diff information..."></textarea>
                    <div class="precheck-checkboxes">
                        <label class="checkbox-label">
                            <input type="checkbox" id="zdcmDiff_qa" class="precheck-admin-checkbox">
                            QA
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="zdcmDiff_server" class="precheck-admin-checkbox">
                            Server
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="zdcmDiff_client" class="precheck-admin-checkbox">
                            Client
                        </label>
                    </div>
                </div>

                <!-- Migration Diff -->
                <div class="precheck-container">
                    <label class="precheck-label">Migration Diff</label>
                    <textarea id="migrationDiffContent" class="precheck-textarea" rows="4"
                              placeholder="Enter migration diff information..."></textarea>
                    <div class="precheck-checkboxes">
                        <label class="checkbox-label">
                            <input type="checkbox" id="migrationDiff_qa" class="precheck-admin-checkbox">
                            QA
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="migrationDiff_server" class="precheck-admin-checkbox">
                            Server
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="migrationDiff_client" class="precheck-admin-checkbox">
                            Client
                        </label>
                    </div>
                </div>


            </div>
        </div>
    `;
    
    section.innerHTML = content;
    section.dataset.loaded = 'true';

    // Initialize NIC Checks functionality after content is loaded
    initializeNicChecksSection();

    // Ensure release type dropdown is enabled by default
    setTimeout(() => {
        const releaseTypeSelect = document.getElementById('releaseType');
        if (releaseTypeSelect) {
            releaseTypeSelect.disabled = false;
            releaseTypeSelect.classList.remove('form-disabled');
            console.log('✅ Release type dropdown enabled by default');
        }
    }, 100);
}

function initializeNicChecksSection() {
    console.log('🚀 Initializing NIC Checks section with prechecks panel...');

    // Show prechecks panel to all users, but only allow Admin and Ultimate to edit
    const userRole = document.body.dataset.userRole;
    console.log('👤 Current user role:', userRole);

    const adminPanel = document.getElementById('prechecksPanelAdmin');
    if (adminPanel) {
        // Always show the prechecks panel (it's in the right column)
        adminPanel.style.display = 'block';
        adminPanel.style.visibility = 'visible';

        console.log('✅ Prechecks panel made visible');

        // Disable checkboxes and textareas for non-Admin/Ultimate users
        if (userRole !== 'ADMIN' && userRole !== 'ULTIMATE') {
            const checkboxes = adminPanel.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.disabled = true;
                checkbox.title = 'Only Admin and Ultimate users can toggle this';
            });

            const textareas = adminPanel.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.disabled = true;
                textarea.title = 'Only Admin and Ultimate users can edit this';
                textarea.style.backgroundColor = '#f8f9fa';
                textarea.style.cursor = 'not-allowed';
            });

            console.log('🔒 Prechecks panel set to read-only for', userRole, 'role');
        } else {
            console.log('✏️ Prechecks panel enabled for editing by', userRole, 'role');
        }



        // Validate all 4 precheck containers are present
        const containers = [
            'sdBuildDiffContent',
            'changeFileDiffContent',
            'zdcmDiffContent',
            'migrationDiffContent'
        ];

        containers.forEach(containerId => {
            const container = document.getElementById(containerId);
            if (container) {
                console.log('✅ Found precheck container:', containerId);
            } else {
                console.error('❌ Missing precheck container:', containerId);
            }
        });

        console.log('✅ Prechecks Management panel fully initialized and visible');
    } else {
        console.error('❌ prechecksPanelAdmin element not found in DOM');
        // Try to find it with different selectors
        const alternativePanel = document.querySelector('.right-panel');
        if (alternativePanel) {
            console.log('🔍 Found alternative right panel element');
        } else {
            console.error('❌ No right panel found at all');
        }
    }
    
    // Initialize release type dropdown handler
    const releaseTypeSelect = document.getElementById('releaseType');
    if (releaseTypeSelect) {
        releaseTypeSelect.addEventListener('change', function() {
            const buildForm = document.getElementById('buildForm');
            const buildFormContainer = document.getElementById('buildFormContainer');
            if (this.value && buildForm && buildFormContainer) {
                buildFormContainer.style.display = 'block';
                buildForm.style.display = 'block';
                showNotification('Release type selected: ' + this.value, 'success');
            } else if (buildForm && buildFormContainer) {
                buildFormContainer.style.display = 'none';
                buildForm.style.display = 'none';
            }
        });
    }

    // Initialize build form submission
    const buildForm = document.getElementById('buildForm');
    if (buildForm) {
        buildForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitBuildForm();
        });
    }

    // Initialize enhanced form functionality
    initializeEnhancedBuildForm();

    // Initialize automation form submission
    const automationConfigForm = document.getElementById('automationConfigForm');
    if (automationConfigForm) {
        automationConfigForm.addEventListener('submit', function(e) {
            e.preventDefault();
            startAutomation();
        });
    }

    // Initialize checkbox handlers
    initializeCheckboxHandlers();

    // Initialize precheck handlers for admin users
    initializePrecheckHandlers();

    // Initialize report received checkbox handler
    initializeReportReceivedHandler();

    // Initialize workflow management
    initializeWorkflow();
}

// Initialize Enhanced Build Form Functionality
function initializeEnhancedBuildForm() {
    console.log('🚀 Initializing enhanced build form functionality...');

    const gcInput = document.getElementById('gcInput');
    const zohoshowInput = document.getElementById('zohoshowInput');
    const submitBtn = document.getElementById('submitBuildBtn');
    const clientInputs = document.querySelectorAll('.client-input');

    // Function to validate mandatory fields and enable/disable submit button
    function validateMandatoryFields() {
        const gcValue = gcInput ? gcInput.value.trim() : '';
        const zohoshowValue = zohoshowInput ? zohoshowInput.value.trim() : '';

        if (submitBtn) {
            if (gcValue && zohoshowValue) {
                submitBtn.disabled = false;
                submitBtn.classList.remove('btn-disabled');
            } else {
                submitBtn.disabled = true;
                submitBtn.classList.add('btn-disabled');
            }
        }
    }

    // Function to handle Zoho Show input changes
    function handleZohoshowInputChange() {
        if (!zohoshowInput) return;

        const value = zohoshowInput.value.trim();
        const isBuildUrl = value.endsWith('.zip');

        if (isBuildUrl) {
            // If it's a build URL (.zip), disable the input and client sub-product inputs
            zohoshowInput.disabled = true;
            zohoshowInput.classList.add('form-disabled');

            // Disable all client sub-product inputs
            clientInputs.forEach(input => {
                input.disabled = true;
                input.classList.add('form-disabled');
                input.style.backgroundColor = '#f8f9fa';
            });

            console.log('🔒 Build URL detected - disabled Zoho Show and Client Sub-Product inputs');
        } else if (value && !isBuildUrl) {
            // If it's a branch name, enable inputs and auto-copy to client fields
            zohoshowInput.disabled = false;
            zohoshowInput.classList.remove('form-disabled');

            // Enable all client sub-product inputs
            clientInputs.forEach(input => {
                input.disabled = false;
                input.classList.remove('form-disabled');
                input.style.backgroundColor = '';
                // Auto-copy branch name to all client sub-product fields
                input.value = value;
            });

            console.log('🔄 Branch name detected - auto-copied to all Client Sub-Product fields:', value);
        } else {
            // Empty value - enable inputs but don't auto-fill
            zohoshowInput.disabled = false;
            zohoshowInput.classList.remove('form-disabled');

            clientInputs.forEach(input => {
                input.disabled = false;
                input.classList.remove('form-disabled');
                input.style.backgroundColor = '';
            });
        }

        validateMandatoryFields();
    }

    // Add event listeners
    if (gcInput) {
        gcInput.addEventListener('input', validateMandatoryFields);
        gcInput.addEventListener('blur', validateMandatoryFields);
    }

    if (zohoshowInput) {
        // Enable by default
        zohoshowInput.disabled = false;

        zohoshowInput.addEventListener('input', handleZohoshowInputChange);
        zohoshowInput.addEventListener('blur', handleZohoshowInputChange);
    }

    // Initial validation
    validateMandatoryFields();

    // Set up periodic validation to ensure button state is correct
    setInterval(validateMandatoryFields, 1000);

    console.log('✅ Enhanced build form functionality initialized');
}

function loadUpdateBuildContent() {
    const section = document.getElementById('update-build-section');
    if (section.dataset.loaded) return;

    const content = `
        <div class="update-build-container">
            ${generateAutomationConfigForm()}
        </div>
    `;

    section.innerHTML = section.querySelector('.page-header').outerHTML + content;
    section.dataset.loaded = 'true';
}

function loadCheckStatusContent() {
    const section = document.getElementById('check-status-section');
    if (section.dataset.loaded) return;

    const content = `
        <div class="status-container">
            <div class="filter-controls">
                <div class="filter-group">
                    <label for="statusTypeFilter">View Type:</label>
                    <select id="statusTypeFilter" onchange="switchStatusView()">
                        <option value="nic-checks" selected>NIC Checks</option>
                        <option value="recent-builds" id="updateBuildOption" style="display: none;">Update Build</option>
                    </select>
                    <button onclick="refreshStatusData()" class="refresh-icon-btn" title="Refresh">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="23 4 23 10 17 10"></polyline>
                            <polyline points="1 20 1 14 7 14"></polyline>
                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="builds-table-container" id="statusTableContainer">
                <!-- NIC Checks Table -->
                <div id="nicChecksTableWrapper">
                    <div class="table-controls">
                        <input type="text" id="nicChecksSearch" class="search-input" placeholder="Search NIC Checks..." onkeyup="searchTable('nicChecksTable', this.value)">
                        <button class="clear-search-btn" onclick="clearSearch('nicChecks')">Clear</button>
                        <div class="table-info">
                            Showing <span id="nicChecksVisibleCount">0</span> of <span id="nicChecksTotalCount">0</span> entries
                        </div>
                    </div>
                    <table class="builds-table" id="nicChecksTable">
                        <thead>
                            <tr>
                                <th onclick="sortTable('nicChecksTable', 0)">ID <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('nicChecksTable', 1)">Release Type <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('nicChecksTable', 2)">Build Setup <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('nicChecksTable', 3)">Zoho Show <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('nicChecksTable', 4)">Status <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('nicChecksTable', 5)">Completed <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('nicChecksTable', 6)">Actions <span class="sort-arrow">⇅</span></th>
                            </tr>
                        </thead>
                    <tbody id="nicChecksTableBody">
                        <tr>
                            <td colspan="7" class="empty-row">No NIC Check forms completed yet</td>
                        </tr>
                    </tbody>
                </table>
                </div>

                <!-- Recent Builds Table -->
                <div id="recentBuildsTableWrapper" style="display: none;">
                    <div class="table-controls">
                        <input type="text" id="recentBuildsSearch" class="search-input" placeholder="Search Recent Builds..." onkeyup="searchTable('recentBuildsTable', this.value)">
                        <button class="clear-search-btn" onclick="clearSearch('recentBuilds')">Clear</button>
                        <div class="table-info">
                            Showing <span id="recentBuildsVisibleCount">0</span> of <span id="recentBuildsTotalCount">0</span> entries
                        </div>
                    </div>
                    <table class="builds-table" id="recentBuildsTable">
                        <thead>
                            <tr>
                                <th onclick="sortTable('recentBuildsTable', 0)">ID <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('recentBuildsTable', 1)">Domain Setup <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('recentBuildsTable', 2)">Browser <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('recentBuildsTable', 3)">Automation Status <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('recentBuildsTable', 4)">Report URL <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('recentBuildsTable', 5)">Status <span class="sort-arrow">⇅</span></th>
                                <th onclick="sortTable('recentBuildsTable', 6)">Created <span class="sort-arrow">⇅</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" class="empty-row">No Update Build forms completed yet</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Right Panel for Build Details -->
        <div id="buildDetailsPanel" class="right-panel">
            <div class="panel-header">
                <h3>Build Details</h3>
                <button onclick="closeBuildDetailsPanel()" class="close-panel-btn" title="Close Panel">×</button>
            </div>
            <div class="panel-content" id="buildDetailsPanelContent">
                <!-- Content will be populated dynamically -->
            </div>
        </div>

        <!-- Panel Overlay -->
        <div id="panelOverlay" class="panel-overlay" onclick="closeBuildDetailsPanel()"></div>
    `;

    section.innerHTML = section.querySelector('.page-header').outerHTML + content;
    section.dataset.loaded = 'true';

    // Initialize status view switching
    initializeStatusView();

    // Load completed builds
    loadCompletedBuilds();

    // Check for new automation data and highlight
    checkForNewAutomation();
}

// Load Completed Builds into Check Status Table
function loadCompletedBuilds() {
    const completedBuilds = JSON.parse(localStorage.getItem('completedBuilds') || '[]');
    const tableBody = document.getElementById('nicChecksTableBody');

    if (!tableBody) return;

    if (completedBuilds.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="empty-row">No NIC Check forms completed yet</td></tr>';
        return;
    }

    const rows = completedBuilds.map(build => {
        const completedDate = new Date(build.timestamp).toLocaleString();
        const releaseType = build.buildFormDetails?.releaseType || build.releaseType || 'Unknown';
        const buildSetup = build.buildFormDetails?.buildSetup || build.buildSetup || 'Unknown';
        const zohoShow = build.buildFormDetails?.zohoShow || build.zohoShow || 'Unknown';

        return `
            <tr onclick="openBuildDetailsPanel('${build.id}')" class="clickable-row">
                <td>${build.id}</td>
                <td><span class="status-badge ${releaseType.toLowerCase()}">${releaseType}</span></td>
                <td>${buildSetup}</td>
                <td>${zohoShow}</td>
                <td><span class="status-badge completed">${build.status}</span></td>
                <td>${completedDate}</td>
                <td onclick="event.stopPropagation()">
                    <button onclick="downloadCompleteReport('${build.id}')" class="btn btn-sm btn-primary download-report-btn" title="Download Complete Report">
                        Download Report
                    </button>
                </td>
            </tr>
        `;
    }).join('');

    tableBody.innerHTML = rows;

    // Update table info
    updateTableInfo('nicChecks');
}

// Download Build Data Function
function downloadBuildData(buildId) {
    const completedBuilds = JSON.parse(localStorage.getItem('completedBuilds') || '[]');
    const build = completedBuilds.find(b => b.id === buildId);

    if (!build) {
        showNotification('Build data not found', 'error');
        return;
    }

    // Create comprehensive build data
    const buildData = {
        buildId: build.id,
        releaseType: build.releaseType,
        buildSetup: build.buildSetup,
        zohoShow: build.zohoShow,
        status: build.status,
        completedAt: build.timestamp,
        workflowSteps: [
            'Release Type Selection',
            'Build Form Configuration',
            'Automation Configuration',
            'Start Automation',
            'Report Confirmation',
            'Pre-Build Update',
            'Pre-Build Verification',
            'Update to Live',
            'Sanity in Live',
            'Release Completion'
        ],
        downloadedAt: new Date().toISOString()
    };

    // Convert to JSON string
    const dataStr = JSON.stringify(buildData, null, 2);

    // Create and trigger download
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `build-${buildId}-data.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);

    showNotification(`Build ${buildId} data downloaded successfully`, 'success');
    console.log('✅ Build data downloaded:', buildData);
}

// Open Build Details Panel
function openBuildDetailsPanel(buildId) {
    console.log('🔍 Opening build details panel for build:', buildId);

    const completedBuilds = JSON.parse(localStorage.getItem('completedBuilds') || '[]');
    const build = completedBuilds.find(b => b.id === buildId);

    if (!build) {
        showNotification('Build data not found', 'error');
        return;
    }

    // Populate panel content
    populateBuildDetailsPanel(build);

    // Show panel with animation
    const panel = document.getElementById('buildDetailsPanel');
    const overlay = document.getElementById('panelOverlay');

    if (panel && overlay) {
        overlay.style.display = 'block';
        panel.classList.add('panel-open');

        // Add animation delay
        setTimeout(() => {
            overlay.classList.add('overlay-visible');
        }, 10);

        // Add keyboard support for closing with Escape key
        const handleKeyPress = (e) => {
            if (e.key === 'Escape') {
                closeBuildDetailsPanel();
                document.removeEventListener('keydown', handleKeyPress);
            }
        };
        document.addEventListener('keydown', handleKeyPress);
    }
}

// Close Build Details Panel
function closeBuildDetailsPanel() {
    console.log('🔄 Closing build details panel...');

    const panel = document.getElementById('buildDetailsPanel');
    const overlay = document.getElementById('panelOverlay');

    if (panel && overlay) {
        panel.classList.remove('panel-open');
        overlay.classList.remove('overlay-visible');

        // Hide after animation
        setTimeout(() => {
            overlay.style.display = 'none';
            console.log('✅ Build details panel closed successfully');
        }, 300);
    } else {
        console.error('❌ Panel or overlay elements not found');
    }
}

// Populate Build Details Panel
function populateBuildDetailsPanel(build) {
    const panelContent = document.getElementById('buildDetailsPanelContent');
    if (!panelContent) return;

    const buildFormDetails = build.buildFormDetails || {};
    const automationDetails = build.automationDetails || {};
    const preChecksDetails = build.preChecksDetails || {};
    const preBuildUpdateDetails = build.preBuildUpdateDetails || {};
    const preBuildVerificationDetails = build.preBuildVerificationDetails || {};
    const liveUpdateDetails = build.liveUpdateDetails || {};
    const workflowTimeline = build.workflowTimeline || [];

    const content = `
        <div class="panel-section">
            <h4 class="panel-section-title">📋 Build Form Details</h4>
            <div class="detail-grid">
                <div class="detail-item"><strong>Release Type:</strong> ${buildFormDetails.releaseType || 'Unknown'}</div>
                <div class="detail-item"><strong>Build Setup:</strong> ${buildFormDetails.buildSetup || 'Unknown'}</div>
                <div class="detail-item"><strong>Zoho Show:</strong> ${buildFormDetails.zohoShow || 'Unknown'}</div>
                <div class="detail-item"><strong>Shape Framework:</strong> ${buildFormDetails.shapeFramework || 'Unknown'}</div>
                <div class="detail-item"><strong>Graphikos Media:</strong> ${buildFormDetails.graphikosMedia || 'Unknown'}</div>
                <div class="detail-item"><strong>Show Rendering Framework:</strong> ${buildFormDetails.showRenderingFramework || 'Unknown'}</div>
                <div class="detail-item"><strong>Show Server:</strong> ${buildFormDetails.showServer || 'Unknown'}</div>
                <div class="detail-item"><strong>Conversion:</strong> ${buildFormDetails.conversion || 'Unknown'}</div>
                <div class="detail-item"><strong>Pictures:</strong> ${buildFormDetails.pictures || 'Unknown'}</div>
                <div class="detail-item"><strong>Image Conversion:</strong> ${buildFormDetails.imageConversion || 'Unknown'}</div>
            </div>
        </div>

        <div class="panel-section">
            <h4 class="panel-section-title">🤖 Automation Details</h4>
            <div class="detail-grid">
                <div class="detail-item"><strong>Config Completed:</strong> ${automationDetails.configCompleted ? '✅ Yes' : '❌ No'}</div>
                <div class="detail-item"><strong>Start Time:</strong> ${new Date(automationDetails.startTime || build.timestamp).toLocaleString()}</div>
                <div class="detail-item"><strong>Report Received:</strong> ${automationDetails.reportReceived ? '✅ Yes' : '❌ No'}</div>
                <div class="detail-item"><strong>Completion Status:</strong> ${automationDetails.completionStatus || 'Unknown'}</div>
            </div>
        </div>

        <div class="panel-section">
            <h4 class="panel-section-title">✅ Pre-Checks Details</h4>

            <div class="detail-item"><strong>Manual Testcase:</strong> ${preChecksDetails.manualTestcase ? '✅ Completed' : '❌ Not Completed'}</div>
            <div class="detail-item"><strong>Manual Testcase Sheet:</strong> ${preChecksDetails.manualTestcaseSheet || 'Not provided'}</div>

            <h5 style="margin: 1rem 0 0.5rem 0; color: #495057;">Prechecks Details</h5>
            <div class="detail-grid">
                <div class="detail-item"><strong>Show Build Diff:</strong> ${preChecksDetails.showBuildDiff || 'Not provided'}</div>
                <div class="detail-item"><strong>Show Migration Files:</strong> ${preChecksDetails.showMigrationFiles || 'Not provided'}</div>
                <div class="detail-item"><strong>Database Changes:</strong> ${preChecksDetails.databaseChanges || 'Not provided'}</div>
                <div class="detail-item"><strong>Configuration Updates:</strong> ${preChecksDetails.configurationUpdates || 'Not provided'}</div>
                <div class="detail-item"><strong>Dependencies Check:</strong> ${preChecksDetails.dependenciesCheck || 'Not provided'}</div>
            </div>
        </div>

        <div class="panel-section">
            <h4 class="panel-section-title">🔄 Pre-Build Update Details</h4>
            <div class="detail-grid">
                <div class="detail-item"><strong>Update Status:</strong> ${preBuildUpdateDetails.updateStatus || 'Unknown'}</div>
                <div class="detail-item"><strong>Update Timestamp:</strong> ${new Date(preBuildUpdateDetails.updateTimestamp || build.timestamp).toLocaleString()}</div>
            </div>
        </div>

        <div class="panel-section">
            <h4 class="panel-section-title">🔍 Pre-Build Verification Details</h4>
            <div class="detail-grid">
                <div class="detail-item"><strong>Pre Sanity:</strong> ${preBuildVerificationDetails.preSanity ? '✅ Completed' : '❌ Not Completed'}</div>
                <div class="detail-item"><strong>Pre Automation:</strong> ${preBuildVerificationDetails.preAutomation ? '✅ Completed' : '❌ Not Completed'}</div>
            </div>
        </div>

        <div class="panel-section">
            <h4 class="panel-section-title">🚀 Live Update Details</h4>
            <div class="detail-grid">
                <div class="detail-item"><strong>Live Update Status:</strong> ${liveUpdateDetails.liveUpdateStatus || 'Unknown'}</div>
                <div class="detail-item"><strong>Sanity in Live:</strong> ${liveUpdateDetails.sanityInLive ? '✅ Completed' : '❌ Not Completed'}</div>
                <div class="detail-item"><strong>Live Update Timestamp:</strong> ${new Date(liveUpdateDetails.liveUpdateTimestamp || build.timestamp).toLocaleString()}</div>
            </div>
        </div>

        <div class="panel-section">
            <h4 class="panel-section-title">⏱️ Workflow Timeline</h4>
            <div class="timeline">
                ${workflowTimeline.map(step => `
                    <div class="timeline-item">
                        <div class="timeline-marker">${step.step}</div>
                        <div class="timeline-content">
                            <div class="timeline-title">${step.name}</div>
                            <div class="timeline-time">${new Date(step.timestamp).toLocaleString()}</div>
                            <div class="timeline-status status-${step.status.toLowerCase()}">${step.status}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>

        <div class="panel-actions">
            <button onclick="downloadCompleteReport('${build.id}')" class="btn btn-primary">
                📄 Download Complete Report
            </button>
            <button onclick="closeBuildDetailsPanel()" class="btn btn-secondary">
                Close
            </button>
        </div>
    `;

    panelContent.innerHTML = content;
}

// Download Complete Report as Markdown
function downloadCompleteReport(buildId) {
    const completedBuilds = JSON.parse(localStorage.getItem('completedBuilds') || '[]');
    const build = completedBuilds.find(b => b.id === buildId);

    if (!build) {
        showNotification('Build data not found', 'error');
        return;
    }

    // Generate Markdown report
    const markdownReport = generateMarkdownReport(build);

    // Create and trigger download
    const blob = new Blob([markdownReport], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `GATE-Build-Report-${buildId}-${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);

    showNotification(`Markdown report for build ${buildId} downloaded successfully`, 'success');
    console.log('✅ Markdown report downloaded for build:', buildId);
}

// Generate Markdown Report
function generateMarkdownReport(build) {
    const buildFormDetails = build.buildFormDetails || {};
    const automationDetails = build.automationDetails || {};
    const preChecksDetails = build.preChecksDetails || {};
    const preBuildUpdateDetails = build.preBuildUpdateDetails || {};
    const preBuildVerificationDetails = build.preBuildVerificationDetails || {};
    const liveUpdateDetails = build.liveUpdateDetails || {};
    const workflowTimeline = build.workflowTimeline || [];

    const currentUser = sessionStorage.getItem('currentUser') || 'Unknown User';
    const generatedAt = new Date().toLocaleString();
    const completedAt = new Date(build.timestamp).toLocaleString();

    return `# GATE Application - Build Activity Report

## Build Information
- **Build ID**: ${build.id}
- **Status**: ${build.status}
- **Completed At**: ${completedAt}
- **Report Generated**: ${generatedAt}
- **Generated By**: ${currentUser}

---

## 📋 Build Form Configuration

### Release Details
- **Release Type**: ${buildFormDetails.releaseType || 'Not specified'}
- **Build Setup URL**: ${buildFormDetails.buildSetup || 'Not provided'}
- **Zoho Show URL**: ${buildFormDetails.zohoShow || 'Not provided'}

### Framework Versions
- **Shape Framework**: ${buildFormDetails.shapeFramework || 'Not specified'}
- **Graphikos Media**: ${buildFormDetails.graphikosMedia || 'Not specified'}
- **Show Rendering Framework**: ${buildFormDetails.showRenderingFramework || 'Not specified'}
- **Show Server**: ${buildFormDetails.showServer || 'Not specified'}
- **Conversion**: ${buildFormDetails.conversion || 'Not specified'}
- **Pictures**: ${buildFormDetails.pictures || 'Not specified'}
- **Image Conversion**: ${buildFormDetails.imageConversion || 'Not specified'}

**Action**: Build form configured and submitted
**When**: ${completedAt}
**By**: ${currentUser}

---

## 🤖 Automation Process

### Automation Configuration
- **Config Completed**: ${automationDetails.configCompleted ? '✅ Yes' : '❌ No'}
- **Start Time**: ${new Date(automationDetails.startTime || build.timestamp).toLocaleString()}
- **Report Received**: ${automationDetails.reportReceived ? '✅ Confirmed' : '❌ Not Confirmed'}
- **Completion Status**: ${automationDetails.completionStatus || 'Unknown'}

**Action**: Automation process executed and report confirmed
**When**: ${new Date(automationDetails.startTime || build.timestamp).toLocaleString()}
**By**: ${currentUser}

---

## ✅ Pre-Checks Verification

### Manual Testing
- **Manual Testcase**: ${preChecksDetails.manualTestcase ? '✅ Completed' : '❌ Not Completed'}
- **Manual Testcase Sheet**: ${preChecksDetails.manualTestcaseSheet || 'Not provided'}

### Prechecks Details
- **Show Build Diff**: ${preChecksDetails.showBuildDiff || 'Not provided'}
- **Show Migration Files**: ${preChecksDetails.showMigrationFiles || 'Not provided'}
- **Database Changes**: ${preChecksDetails.databaseChanges || 'Not provided'}
- **Configuration Updates**: ${preChecksDetails.configurationUpdates || 'Not provided'}
- **Dependencies Check**: ${preChecksDetails.dependenciesCheck || 'Not provided'}

**Action**: All pre-checks verified and completed
**When**: ${completedAt}
**By**: ${currentUser}

---

## 🔄 Pre-Build Update Process

- **Update Status**: ${preBuildUpdateDetails.updateStatus || 'Unknown'}
- **Update Timestamp**: ${new Date(preBuildUpdateDetails.updateTimestamp || build.timestamp).toLocaleString()}

**Action**: Build updated in Pre environment
**When**: ${new Date(preBuildUpdateDetails.updateTimestamp || build.timestamp).toLocaleString()}
**By**: ${currentUser}

---

## 🔍 Pre-Build Verification

- **Pre Sanity**: ${preBuildVerificationDetails.preSanity ? '✅ Completed' : '❌ Not Completed'}
- **Pre Automation**: ${preBuildVerificationDetails.preAutomation ? '✅ Completed' : '❌ Not Completed'}

**Action**: Pre-build verification completed
**When**: ${completedAt}
**By**: ${currentUser}

---

## 🚀 Live Environment Update

- **Live Update Status**: ${liveUpdateDetails.liveUpdateStatus || 'Unknown'}
- **Sanity in Live**: ${liveUpdateDetails.sanityInLive ? '✅ Completed' : '❌ Not Completed'}
- **Live Update Timestamp**: ${new Date(liveUpdateDetails.liveUpdateTimestamp || build.timestamp).toLocaleString()}

**Action**: Build deployed to Live environment and sanity testing completed
**When**: ${new Date(liveUpdateDetails.liveUpdateTimestamp || build.timestamp).toLocaleString()}
**By**: ${currentUser}

---

## ⏱️ Complete Workflow Timeline

${workflowTimeline.map((step, index) => `
### Step ${step.step}: ${step.name}
- **Status**: ${step.status}
- **Completed**: ${new Date(step.timestamp).toLocaleString()}
- **Action**: ${step.name} executed successfully
- **By**: ${currentUser}
`).join('')}

---

## 📊 Summary

This report documents the complete NIC Checks workflow execution for Build ID ${build.id}. All steps were completed successfully from initial configuration through live deployment.

### Key Metrics
- **Total Workflow Steps**: ${workflowTimeline.length}
- **Completion Time**: ${completedAt}
- **Final Status**: ${build.status}
- **Responsible User**: ${currentUser}

### System Activity Log
- **Build Configuration**: ✅ Completed
- **Automation Execution**: ✅ Completed
- **Pre-Checks Verification**: ✅ Completed
- **Pre-Build Update**: ✅ Completed
- **Pre-Build Verification**: ✅ Completed
- **Live Deployment**: ✅ Completed
- **Sanity Testing**: ✅ Completed
- **Release Finalization**: ✅ Completed

---

*Report generated by GATE Application on ${generatedAt}*
*Build ID: ${build.id} | User: ${currentUser}*
`;
}

function switchStatusView() {
    const typeFilter = document.getElementById('statusTypeFilter');
    const nicChecksWrapper = document.getElementById('nicChecksTableWrapper');
    const recentBuildsWrapper = document.getElementById('recentBuildsTableWrapper');

    if (typeFilter.value === 'nic-checks') {
        nicChecksWrapper.style.display = 'block';
        recentBuildsWrapper.style.display = 'none';
        updateTableInfo('nicChecks');
    } else {
        nicChecksWrapper.style.display = 'none';
        recentBuildsWrapper.style.display = 'block';
        updateTableInfo('recentBuilds');
    }
}

function refreshStatusData() {
    // Placeholder for refreshing status data
    showNotification('Status data refreshed', 'success');
}

function initializeStatusView() {
    // Show Update Build option only for QA and Ultimate roles
    const userRole = document.body.dataset.userRole;
    if (userRole === 'QA' || userRole === 'ULTIMATE') {
        const updateBuildOption = document.getElementById('updateBuildOption');
        if (updateBuildOption) {
            updateBuildOption.style.display = 'block';
        }
    }

    // Initialize the status view functionality
    switchStatusView();
}

// Start Automation functionality
function startAutomation() {
    const automationForm = document.querySelector('.automation-config-form');
    if (!automationForm) {
        showNotification('Automation form not found', 'error');
        return;
    }

    // Collect all form values
    const formData = new FormData();
    const inputs = automationForm.querySelectorAll('input, select');

    inputs.forEach(input => {
        if (input.type === 'checkbox') {
            formData.append(input.name, input.checked);
        } else {
            formData.append(input.name, input.value);
        }
    });

    // Convert FormData to object for easier handling
    const configData = {};
    for (let [key, value] of formData.entries()) {
        configData[key] = value;
    }

    // Show loading state
    const startButton = document.querySelector('button[onclick="startAutomation()"]') ||
                       document.querySelector('.btn:contains("Start Automation")') ||
                       Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Start Automation'));
    if (startButton) {
        startButton.disabled = true;
        startButton.textContent = 'Starting...';
    }

    // Mock automation start with realistic delay
    setTimeout(() => {
        // Generate mock automation response
        const mockResponse = {
            success: true,
            automationId: 'AUTO-' + Date.now(),
            status: 'RUNNING',
            reportUrl: `http://automation-server.com/reports/${Date.now()}`,
            estimatedDuration: '15-20 minutes',
            browser: configData.browser || 'googlechrome',
            environment: configData.domainSetup || 'gcautomation'
        };

        // Update UI with automation status
        updateAutomationStatus(mockResponse);

        // Re-enable button
        if (startButton) {
            startButton.disabled = false;
            startButton.textContent = 'Start Automation';
        }

        showNotification(`Automation started successfully! ID: ${mockResponse.automationId}`, 'success');

        // Lock earlier sections after automation starts
        lockEarlierSections(['releaseType', 'buildForm', 'automationSection']);

        // Show automation report section
        const reportSection = document.getElementById('automationReportSection');
        if (reportSection) {
            reportSection.style.display = 'block';

            // Enable the report received checkbox
            const reportReceivedCheckbox = document.getElementById('reportReceivedCheckbox');
            if (reportReceivedCheckbox) {
                reportReceivedCheckbox.disabled = false;
            }
        }

        // DO NOT overwrite the real buildId - keep the database ID
        // sessionStorage.setItem('currentBuildId', mockResponse.automationId.replace('AUTO-', ''));
        console.log('🔍 Keeping real buildId, not overwriting with mock automation ID');

        // Capture ALL form data for Update Build section
        let automationData = {
            id: mockResponse.automationId,
            status: mockResponse.status,
            browser: mockResponse.browser,
            environment: mockResponse.environment,
            timestamp: new Date().toISOString(),
            reportUrl: mockResponse.reportUrl
        };

        // If this is from Update Build section, capture complete form data
        if (isUpdateBuildSection) {
            const automationForm = document.querySelector('.automation-config-form');
            if (automationForm) {
                const formData = new FormData(automationForm);

                // Add all form fields to automation data
                for (let [key, value] of formData.entries()) {
                    automationData[key] = value;
                }

                // Override with specific mappings
                automationData.domainSetup = formData.get('domainSetup') || 'gcautomation';
                automationData.localUrl = formData.get('localUrl') || 'http://localhost:8080';
                automationData.buildDetailsAPI = formData.get('buildDetailsAPI') || '';
                automationData.localServerMachineName = formData.get('localServerMachineName') || '';
                automationData.automationStatus = formData.get('automationStatus') || 'enabled';
                automationData.tomcatPath = formData.get('tomcatPath') || '';
                automationData.reportSubjectForBot = formData.get('reportSubjectForBot') || '';
                automationData.sendReportInBot = formData.get('sendReportInBot') === 'on';
                automationData.cliqReportBotURL = formData.get('cliqReportBotURL') || '';
            }
        }

        sessionStorage.setItem('newAutomation', JSON.stringify(automationData));

        // Detect current section to determine redirect behavior
        const currentSection = document.querySelector('.content-section.active');
        console.log('Current section:', currentSection ? currentSection.id : 'none');
        const isUpdateBuildSection = currentSection && currentSection.id === 'update-build-section';
        console.log('Is Update Build section:', isUpdateBuildSection);

        if (isUpdateBuildSection) {
            // Only redirect from Update Build section
            let countdown = 3;
            const countdownInterval = setInterval(() => {
                showNotification(`Automation started! Redirecting to Check Status in ${countdown}...`, 'info');
                countdown--;

                if (countdown < 0) {
                    clearInterval(countdownInterval);

                    // Force reload of Check Status content for proper UI re-rendering
                    const checkStatusSection = document.getElementById('check-status-section');
                    if (checkStatusSection) {
                        checkStatusSection.dataset.loaded = 'false';
                    }

                    // Switch to Check Status section
                    switchToSection('check-status');

                    // Update active navigation
                    document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
                    document.querySelectorAll('.nav-link[data-section="check-status"]').forEach(nav => nav.classList.add('active'));

                    // Focus on top of Check Status page and refresh table
                    setTimeout(() => {
                        // Scroll to top of Check Status section
                        const checkStatusSection = document.getElementById('check-status-section');
                        if (checkStatusSection) {
                            checkStatusSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }

                        // Refresh the completed builds table to show new entry
                        loadCompletedBuilds();

                        // Reset form for next submission
                        resetFormAfterSubmission();
                    }, 500);

                    showNotification('New NIC Check submitted successfully! Ready for next submission.', 'success');
                }
            }, 1000);
        } else {
            // For NIC Checks section, show inline status without redirect
            showNotification(`Automation started successfully! ID: ${mockResponse.automationId}`, 'success');
        }

    }, 2000); // 2 second delay to simulate processing
}

function updateAutomationStatus(response) {
    // Update status display
    const statusArea = document.querySelector('.status-area');
    if (statusArea) {
        statusArea.style.display = 'block';
        const statusContent = document.getElementById('buildStatus');
        if (statusContent) {
            statusContent.innerHTML = `
                <div class="automation-status">
                    <h4>Automation Status: ${response.status}</h4>
                    <p><strong>ID:</strong> ${response.automationId}</p>
                    <p><strong>Browser:</strong> ${response.browser}</p>
                    <p><strong>Environment:</strong> ${response.environment}</p>
                    <p><strong>Estimated Duration:</strong> ${response.estimatedDuration}</p>
                    <p><strong>Report URL:</strong> <a href="${response.reportUrl}" target="_blank">${response.reportUrl}</a></p>
                </div>
            `;
        }
    }
}

// Initialize Report Received Checkbox Handler
function initializeReportReceivedHandler() {
    const reportReceivedCheckbox = document.getElementById('reportReceivedCheckbox');
    if (reportReceivedCheckbox) {
        reportReceivedCheckbox.addEventListener('change', function() {
            if (this.checked) {
                confirmReportReceived();
            }
        });
    }
}

// Confirm Report Received Function
function confirmReportReceived() {
    console.log('🚀 Starting confirmReportReceived process');

    const checkbox = document.getElementById('reportReceivedCheckbox');
    const buildId = getCurrentBuildId();

    console.log('📋 Confirm report - buildId retrieved:', buildId);

    if (!buildId) {
        console.error('❌ No buildId available for report confirmation');
        showNotification('Build ID not found. Please refresh and try again.', 'error');
        checkbox.checked = false;
        return;
    }

    console.log('📡 Making API call to:', `/gqa/api/builds/${buildId}/confirm-report`);

    // Show loading state
    checkbox.disabled = true;
    const originalText = checkbox.nextSibling.textContent;
    checkbox.nextSibling.textContent = ' Confirming...';

    // Add spinner
    const spinner = document.createElement('span');
    spinner.className = 'spinner';
    spinner.innerHTML = ' ⟳';
    checkbox.parentNode.appendChild(spinner);

    // Send AJAX request
    fetch(`/gqa/api/builds/${buildId}/confirm-report`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        // Remove spinner
        if (spinner.parentNode) {
            spinner.parentNode.removeChild(spinner);
        }

        if (data.success) {
            // Show success notification
            showNotification(data.message, 'success');

            // Restore text
            checkbox.nextSibling.textContent = originalText;

            // Complete workflow step
            completeWorkflowStep(5, 'Report Confirmation');

            // Show pre-build update section (Step 6)
            showPreBuildUpdateSection();

            console.log('✅ Step 5 completed: Report confirmed, showing pre-build update section');
        } else {
            // Show error notification
            showNotification('Error: ' + data.message, 'error');
            checkbox.checked = false;
            checkbox.nextSibling.textContent = originalText;
        }
    })
    .catch(error => {
        // Remove spinner
        if (spinner.parentNode) {
            spinner.parentNode.removeChild(spinner);
        }

        console.error('Error confirming report:', error);
        showNotification('Network error occurred', 'error');
        checkbox.checked = false;
        checkbox.nextSibling.textContent = originalText;
    })
    .finally(() => {
        checkbox.disabled = false;
    });
}

// Submit Build Form Function
function submitBuildForm() {
    console.log('🚀 Step 2: Starting build form submission');

    const form = document.getElementById('buildForm');
    const releaseTypeSelect = document.getElementById('releaseType');

    // Validate required fields
    if (!releaseTypeSelect || !releaseTypeSelect.value) {
        console.error('❌ Release type validation failed');
        showNotification('Please select a release type', 'error');
        return;
    }

    // Validate mandatory GC field
    const gcInput = document.getElementById('gcInput');
    if (!gcInput || !gcInput.value.trim()) {
        console.error('❌ GC field validation failed');
        showNotification('GC field is mandatory', 'error');
        return;
    }

    // Validate mandatory Zoho Show field
    const zohoshowInput = document.getElementById('zohoshowInput');
    if (!zohoshowInput || !zohoshowInput.value.trim()) {
        console.error('❌ Zoho Show field validation failed');
        showNotification('Zoho Show field is mandatory', 'error');
        return;
    }

    console.log('✅ All mandatory fields validated');
    console.log('✅ Release type selected:', releaseTypeSelect.value);
    console.log('✅ GC value:', gcInput.value.trim());
    console.log('✅ Zoho Show value:', zohoshowInput.value.trim());

    const formData = new FormData(form);

    // Convert FormData to object
    const buildData = {};
    for (let [key, value] of formData.entries()) {
        buildData[key] = value;
    }

    // Add release type from separate select
    buildData.releaseType = releaseTypeSelect.value;

    // Log form data for debugging
    console.log('📋 Build data prepared:', buildData);
    console.log('📊 Form fields count:', Object.keys(buildData).length);

    // Show loading state
    const submitButton = form.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.textContent = 'Submitting...';
    }

    fetch('/gqa/api/builds/submit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(buildData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('📥 Build submission response:', data);

        if (data.success && data.buildId) {
            // Store the actual build ID from the response, or generate unique one for multiple submissions
            const uniqueBuildId = data.buildId ? data.buildId.toString() : generateUniqueBuildId();
            sessionStorage.setItem('currentBuildId', uniqueBuildId);
            console.log('✅ Unique buildId stored for this submission:', uniqueBuildId);

            completeWorkflowStep(2, 'Build Form Configuration');
            showNotification('Build submitted successfully!', 'success');

            // Lock earlier sections after Start Build
            lockEarlierSections(['releaseType', 'buildForm']);

            // Show automation configuration section
            const automationSection = document.getElementById('automationSection');
            if (automationSection) {
                automationSection.style.display = 'block';
                automationSection.scrollIntoView({ behavior: 'smooth' });

                // Add visual step indicator
                showWorkflowStep(3, 'Automation Configuration');

                console.log('✅ Step 3: Automation configuration section displayed, earlier sections locked');
            } else {
                console.error('❌ automationSection element not found');
            }

            // Save workflow state
            saveWorkflowState();
        } else {
            console.error('❌ Build submission failed or no buildId returned:', data);
            showNotification('Error: ' + (data.message || 'Build submission failed'), 'error');
        }
    })
    .catch(error => {
        console.error('Error submitting build:', error);
        showNotification('Network error occurred', 'error');
    })
    .finally(() => {
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = 'Submit Build';
        }
    });
}

// Get current build ID (stored when build is submitted)
function getCurrentBuildId() {
    const buildId = sessionStorage.getItem('currentBuildId');
    console.log('🔍 Retrieved buildId from sessionStorage:', buildId);
    console.log('🔍 SessionStorage contents:', {
        currentBuildId: sessionStorage.getItem('currentBuildId'),
        allKeys: Object.keys(sessionStorage)
    });

    if (!buildId) {
        console.error('❌ No buildId found in sessionStorage');
        return null;
    }

    // Check if it's a timestamp (13 digits) vs database ID (smaller number)
    const numericId = parseInt(buildId);
    if (isNaN(numericId) || numericId <= 0) {
        console.error('❌ Invalid buildId format:', buildId);
        return null;
    }

    // Warn if it looks like a timestamp
    if (buildId.length >= 13) {
        console.warn('⚠️ BuildId looks like a timestamp, not a database ID:', buildId);
    } else {
        console.log('✅ BuildId appears to be a valid database ID:', buildId);
    }

    return buildId;
}

// Check for new automation and highlight in Check Status
function checkForNewAutomation() {
    const newAutomationData = sessionStorage.getItem('newAutomation');
    if (newAutomationData) {
        try {
            const automation = JSON.parse(newAutomationData);

            // Switch to Recent Builds view automatically
            const statusTypeFilter = document.getElementById('statusTypeFilter');
            if (statusTypeFilter) {
                statusTypeFilter.value = 'recent-builds';
                switchStatusView();
            }

            // Add the new automation entry to the Recent Builds table
            addNewAutomationEntry(automation);

            // Clear the session storage
            sessionStorage.removeItem('newAutomation');

        } catch (error) {
            console.error('Error parsing automation data:', error);
        }
    }
}

// Add new automation entry to Recent Builds table
function addNewAutomationEntry(automation) {
    const recentBuildsTable = document.getElementById('recentBuildsTable');
    if (recentBuildsTable) {
        const tbody = recentBuildsTable.querySelector('tbody');

        // Remove empty row if it exists
        const emptyRow = tbody.querySelector('.empty-row');
        if (emptyRow) {
            emptyRow.parentNode.removeChild(emptyRow);
        }

        // Create new row with comprehensive data
        const newRow = document.createElement('tr');
        newRow.className = 'new-entry-highlight';
        newRow.innerHTML = `
            <td>${automation.id}</td>
            <td>${automation.domainSetup || automation.environment || 'gcautomation'}</td>
            <td>${automation.browser || 'googlechrome'}</td>
            <td>${automation.status}</td>
            <td><a href="${automation.reportUrl}" target="_blank">View Report</a></td>
            <td><span class="status-badge running">${automation.status}</span></td>
            <td>${new Date(automation.timestamp).toLocaleString()}</td>
        `;

        // Add click handler for row details
        newRow.addEventListener('click', function() {
            showAutomationDetails(automation);
        });

        // Insert at the top of the table
        tbody.insertBefore(newRow, tbody.firstChild);

        // Update table info counter
        updateTableInfo('recentBuilds');

        // Scroll to the new entry
        newRow.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Remove highlight after 5 seconds
        setTimeout(() => {
            newRow.classList.remove('new-entry-highlight');
        }, 5000);
    }
}

// Show automation details in modal
function showAutomationDetails(automation) {
    const modalContent = `
        <div class="automation-details-modal">
            <h3>Automation Details - ${automation.id}</h3>
            <div class="details-grid">
                <div class="detail-item"><strong>Status:</strong> ${automation.status}</div>
                <div class="detail-item"><strong>Browser:</strong> ${automation.browser || 'googlechrome'}</div>
                <div class="detail-item"><strong>Domain Setup:</strong> ${automation.domainSetup || 'gcautomation'}</div>
                <div class="detail-item"><strong>Local URL:</strong> ${automation.localUrl || 'N/A'}</div>
                <div class="detail-item"><strong>Build Details API:</strong> ${automation.buildDetailsAPI || 'N/A'}</div>
                <div class="detail-item"><strong>Server Machine:</strong> ${automation.localServerMachineName || 'N/A'}</div>
                <div class="detail-item"><strong>Tomcat Path:</strong> ${automation.tomcatPath || 'N/A'}</div>
                <div class="detail-item"><strong>Report Subject:</strong> ${automation.reportSubjectForBot || 'N/A'}</div>
                <div class="detail-item"><strong>Bot Reporting:</strong> ${automation.sendReportInBot ? 'Enabled' : 'Disabled'}</div>
                <div class="detail-item"><strong>Bot URL:</strong> ${automation.cliqReportBotURL || 'N/A'}</div>
                <div class="detail-item"><strong>Report URL:</strong> <a href="${automation.reportUrl}" target="_blank">${automation.reportUrl}</a></div>
                <div class="detail-item"><strong>Created:</strong> ${new Date(automation.timestamp).toLocaleString()}</div>
            </div>
        </div>
    `;

    // Create and show modal (simplified version)
    const existingModal = document.querySelector('.automation-details-modal-overlay');
    if (existingModal) {
        existingModal.remove();
    }

    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'automation-details-modal-overlay';
    modalOverlay.innerHTML = `
        <div class="modal-content">
            ${modalContent}
            <button onclick="this.closest('.automation-details-modal-overlay').remove()" class="btn btn-secondary">Close</button>
        </div>
    `;

    document.body.appendChild(modalOverlay);
}

// Workflow Step Management Functions
function showWorkflowStep(stepNumber, stepName) {
    console.log(`📍 Workflow Step ${stepNumber}: ${stepName}`);

    // Create or update step indicator
    let stepIndicator = document.getElementById('workflowStepIndicator');
    if (!stepIndicator) {
        stepIndicator = document.createElement('div');
        stepIndicator.id = 'workflowStepIndicator';
        stepIndicator.className = 'workflow-step-indicator';

        // Insert at the top of the NIC Checks section
        const nicChecksSection = document.getElementById('nic-checks-section');
        if (nicChecksSection) {
            nicChecksSection.insertBefore(stepIndicator, nicChecksSection.firstChild);
        }
    }

    stepIndicator.innerHTML = `
        <div class="step-progress">
            <div class="step-number">${stepNumber}</div>
            <div class="step-name">${stepName}</div>
            <div class="step-status">In Progress...</div>
        </div>
    `;
}

function completeWorkflowStep(stepNumber, stepName) {
    console.log(`✅ Completed Step ${stepNumber}: ${stepName}`);

    const stepIndicator = document.getElementById('workflowStepIndicator');
    if (stepIndicator) {
        stepIndicator.innerHTML = `
            <div class="step-progress completed">
                <div class="step-number">✓</div>
                <div class="step-name">${stepName}</div>
                <div class="step-status">Completed</div>
            </div>
        `;
    }
}

// Get current user role from body data attribute
function getCurrentUserRole() {
    const body = document.body;
    const userRole = body.getAttribute('data-user-role');
    console.log('👤 Current user role:', userRole);
    return userRole;
}

// Role-based access control for section switching
function checkSectionAccess(sectionName) {
    const userRole = getCurrentUserRole();

    // Define role access matrix
    const roleAccess = {
        'ADMIN': ['nic-checks', 'check-status'],
        'EDITOR': ['nic-checks', 'check-status'],
        'QA': ['nic-checks', 'check-status'],
        'ULTIMATE': ['nic-checks', 'check-status', 'update-build', 'dashboard']
    };

    const allowedSections = roleAccess[userRole] || [];
    const hasAccess = allowedSections.includes(sectionName);

    console.log(`🔐 Access check for ${sectionName}: ${hasAccess ? 'GRANTED' : 'DENIED'} (Role: ${userRole})`);

    if (!hasAccess) {
        showNotification(`Access denied: ${sectionName.replace('-', ' ')} requires ULTIMATE role`, 'error');
        return false;
    }

    return true;
}

// Initialize workflow when release type is selected
function initializeWorkflow() {
    console.log('🎯 Initializing NIC Checks Workflow');
    showWorkflowStep(1, 'Release Type Selection');

    const releaseTypeSelect = document.getElementById('releaseType');
    if (releaseTypeSelect) {
        releaseTypeSelect.addEventListener('change', function() {
            if (this.value) {
                completeWorkflowStep(1, 'Release Type Selection');
                showWorkflowStep(2, 'Build Form Configuration');

                // Show the build form container and form
                const buildForm = document.getElementById('buildForm');
                const buildFormContainer = document.getElementById('buildFormContainer');
                if (buildForm && buildFormContainer) {
                    buildFormContainer.style.display = 'block';
                    buildForm.style.display = 'block';
                    buildForm.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });
    }
}

// Show Pre-Build Update Section (Step 6)
function showPreBuildUpdateSection() {
    const preBuildSection = document.getElementById('preBuildUpdateSection');
    if (preBuildSection) {
        preBuildSection.style.display = 'block';
        preBuildSection.scrollIntoView({ behavior: 'smooth' });

        // Add highlight animation
        preBuildSection.classList.add('section-highlight');
        setTimeout(() => {
            preBuildSection.classList.remove('section-highlight');
        }, 3000);

        showWorkflowStep(6, 'Pre-Build Update');

        // Show both manual testcase and pre-checks sections simultaneously
        showSimultaneousSections();

        console.log('✅ Step 6: Pre-Build Update section displayed with simultaneous sections');
    }
}

// Update Build in Pre (called when button is clicked)
function updateBuildInPre() {
    console.log('🚀 Starting pre-build update process');

    const button = document.querySelector('#preBuildUpdateSection button');
    if (button) {
        button.disabled = true;
        button.textContent = 'Updating Build in Pre...';
        button.classList.add('btn-loading');
    }

    // Simulate build update process
    setTimeout(() => {
        // Show success notification
        showNotification('🎉 Build updated in Pre environment successfully!', 'success');

        // Complete this step
        completeWorkflowStep(6, 'Pre-Build Update Completed');

        // Show Pre-Build Verification section immediately
        showPreBuildVerificationSection();

        // Update button state
        if (button) {
            button.disabled = false;
            button.textContent = '✅ Pre-Build Update Complete';
            button.classList.remove('btn-loading');
            button.classList.add('btn-success');
            button.style.display = 'none'; // Hide the button after completion
        }

        // Save workflow state
        saveWorkflowState();

        console.log('✅ Step 6 completed: Pre-build update finished, verification section displayed');
    }, 2000);
}

// Show Pre-Build Verification Section
function showPreBuildVerificationSection() {
    const preBuildVerificationSection = document.getElementById('preBuildVerificationSection');
    if (preBuildVerificationSection) {
        preBuildVerificationSection.style.display = 'block';
        preBuildVerificationSection.scrollIntoView({ behavior: 'smooth' });

        // Add highlight animation
        preBuildVerificationSection.classList.add('section-highlight');
        setTimeout(() => {
            preBuildVerificationSection.classList.remove('section-highlight');
        }, 3000);

        showWorkflowStep(7, 'Pre-Build Verification');

        // Initialize validation for Update to Live
        initializePreBuildVerificationValidation();

        console.log('✅ Step 7: Pre-Build Verification section displayed');
    }
}

// Show Manual Testcase and Pre-Checks Sections (Step 7)
function showSimultaneousSections() {
    console.log('🚀 Step 7: Showing manual testcase and pre-checks sections');

    // Show manual testcase section
    const manualTestcaseSection = document.getElementById('manualTestcaseSection');
    if (manualTestcaseSection) {
        manualTestcaseSection.style.display = 'block';

        // Add highlight animation
        manualTestcaseSection.classList.add('section-highlight');
        setTimeout(() => {
            manualTestcaseSection.classList.remove('section-highlight');
        }, 3000);
    }

    // Show pre-checks panel
    const prechecksPanelAdmin = document.getElementById('prechecksPanelAdmin');
    if (prechecksPanelAdmin) {
        prechecksPanelAdmin.style.display = 'block';

        // Add highlight animation
        prechecksPanelAdmin.classList.add('section-highlight');
        setTimeout(() => {
            prechecksPanelAdmin.classList.remove('section-highlight');
        }, 3000);
    }

    showWorkflowStep(7, 'Complete Manual Testcase and Pre-Checks');

    // Initialize checkbox validation for pre-build update
    initializePreBuildUpdateValidation();

    // Scroll to manual testcase section
    if (manualTestcaseSection) {
        manualTestcaseSection.scrollIntoView({ behavior: 'smooth' });
    }

    console.log('✅ Step 7: Manual testcase and pre-checks sections displayed');
}

// Initialize Validation for Pre-Build Update
function initializePreBuildUpdateValidation() {
    const requiredElements = [
        '#reportReceivedCheckbox',  // Automation report confirmed
        '#manualSanityCheckbox',    // Manual sanity report confirmed
        '#manualTestcaseCheckbox',  // Manual testcase confirmed
        // Admin pre-checks (all QA, Server, Client checkboxes)
        '#sdBuildDiff_qa', '#sdBuildDiff_server', '#sdBuildDiff_client',
        '#changeFileDiff_qa', '#changeFileDiff_server', '#changeFileDiff_client',
        '#zdcmDiff_qa', '#zdcmDiff_server', '#zdcmDiff_client',
        '#migrationDiff_qa', '#migrationDiff_server', '#migrationDiff_client'
    ];

    // Add event listeners to all required elements
    requiredElements.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
            element.addEventListener('change', validatePreBuildUpdateRequirements);
        }
    });

    // Initial validation
    validatePreBuildUpdateRequirements();
}

// Validate Pre-Build Update Requirements
function validatePreBuildUpdateRequirements() {
    const basicCheckboxes = ['#reportReceivedCheckbox', '#manualSanityCheckbox', '#manualTestcaseCheckbox'];
    const adminPreChecks = [
        '#sdBuildDiff_qa', '#sdBuildDiff_server', '#sdBuildDiff_client',
        '#changeFileDiff_qa', '#changeFileDiff_server', '#changeFileDiff_client',
        '#zdcmDiff_qa', '#zdcmDiff_server', '#zdcmDiff_client',
        '#migrationDiff_qa', '#migrationDiff_server', '#migrationDiff_client'
    ];

    const allRequiredCheckboxes = [...basicCheckboxes, ...adminPreChecks];

    // Check all checkboxes
    const allValid = allRequiredCheckboxes.every(selector => {
        const checkbox = document.querySelector(selector);
        return checkbox && checkbox.checked;
    });

    // Count completed items for detailed logging
    const checkedCount = allRequiredCheckboxes.filter(selector => {
        const checkbox = document.querySelector(selector);
        return checkbox && checkbox.checked;
    }).length;

    const basicCheckedCount = basicCheckboxes.filter(selector => {
        const checkbox = document.querySelector(selector);
        return checkbox && checkbox.checked;
    }).length;

    const adminCheckedCount = adminPreChecks.filter(selector => {
        const checkbox = document.querySelector(selector);
        return checkbox && checkbox.checked;
    }).length;

    console.log('🔍 Pre-Build Update validation:', {
        totalRequired: allRequiredCheckboxes.length,
        totalCompleted: checkedCount,
        basicCheckboxes: `${basicCheckedCount}/${basicCheckboxes.length}`,
        adminPreChecks: `${adminCheckedCount}/${adminPreChecks.length}`,
        reportReceived: document.querySelector('#reportReceivedCheckbox')?.checked,
        manualSanity: document.querySelector('#manualSanityCheckbox')?.checked,
        manualTestcase: document.querySelector('#manualTestcaseCheckbox')?.checked,
        allValid
    });

    // Enable/disable Update Build in Pre button
    const updatePreButton = document.querySelector('#preBuildUpdateSection button');
    if (updatePreButton) {
        updatePreButton.disabled = !allValid;
        if (allValid) {
            updatePreButton.classList.remove('btn-disabled');
            updatePreButton.classList.add('btn-success');
            updatePreButton.textContent = 'Update Build in Pre (All Requirements Complete)';
        } else {
            updatePreButton.classList.add('btn-disabled');
            updatePreButton.classList.remove('btn-success');
            updatePreButton.textContent = `Update Build in Pre (${checkedCount}/${allRequiredCheckboxes.length} Complete)`;
        }
    }

    console.log(allValid ? '✅ All pre-build requirements met (3 basic + 15 admin pre-checks)' : `⏳ Waiting for all requirements: ${checkedCount}/${allRequiredCheckboxes.length} complete`);
}

// Initialize Pre-Build Verification Validation (for Update to Live)
function initializePreBuildVerificationValidation() {
    const requiredCheckboxes = [
        '#preSanityCheckbox',
        '#preAutomationCheckbox'
    ];

    // Add event listeners
    requiredCheckboxes.forEach(selector => {
        const checkbox = document.querySelector(selector);
        if (checkbox) {
            checkbox.addEventListener('change', validateUpdateToLiveRequirements);
        }
    });

    // Initial validation
    validateUpdateToLiveRequirements();
}

// Validate Update to Live Requirements
function validateUpdateToLiveRequirements() {
    const requiredCheckboxes = [
        '#preSanityCheckbox',
        '#preAutomationCheckbox'
    ];

    const allChecked = requiredCheckboxes.every(selector => {
        const checkbox = document.querySelector(selector);
        return checkbox && checkbox.checked;
    });

    console.log('🔍 Update to Live validation:', {
        preSanity: document.querySelector('#preSanityCheckbox')?.checked,
        preAutomation: document.querySelector('#preAutomationCheckbox')?.checked,
        allChecked
    });

    const liveBuildUpdateSection = document.getElementById('liveBuildUpdateSection');
    if (liveBuildUpdateSection) {
        if (allChecked) {
            liveBuildUpdateSection.style.display = 'block';
            liveBuildUpdateSection.scrollIntoView({ behavior: 'smooth' });

            // Add highlight animation
            liveBuildUpdateSection.classList.add('section-highlight');
            setTimeout(() => {
                liveBuildUpdateSection.classList.remove('section-highlight');
            }, 3000);

            showWorkflowStep(8, 'Ready for Live Update');
            console.log('✅ Step 8: Pre-build verification completed, Update to Live enabled');
        } else {
            liveBuildUpdateSection.style.display = 'none';
            console.log('⏳ Waiting for pre-build verification to complete');
        }
    }
}

// Update to Live
function updateToLive() {
    console.log('🚀 Step 8: Starting Live update process');

    const button = document.getElementById('updateToLiveBtn');
    if (button) {
        button.disabled = true;
        button.textContent = 'Updating to Live...';
    }

    // Simulate live update process
    setTimeout(() => {
        // Show success notification
        showNotification('🎉 Build successfully updated to Live environment!', 'success');

        // Complete this step
        completeWorkflowStep(8, 'Live Update Complete');

        // Show Sanity in Live section
        showSanityInLiveSection();

        if (button) {
            button.disabled = false;
            button.textContent = 'Update to Live';
            button.style.display = 'none'; // Hide the button after completion
        }

        console.log('✅ Step 8 completed: Live update finished, showing sanity in live section');
    }, 3000);
}

// Show Sanity in Live Section
function showSanityInLiveSection() {
    const sanityInLiveSection = document.getElementById('sanityInLiveSection');
    if (sanityInLiveSection) {
        sanityInLiveSection.style.display = 'block';
        sanityInLiveSection.scrollIntoView({ behavior: 'smooth' });

        // Add highlight animation
        sanityInLiveSection.classList.add('section-highlight');
        setTimeout(() => {
            sanityInLiveSection.classList.remove('section-highlight');
        }, 3000);

        showWorkflowStep(9, 'Sanity in Live');

        // Initialize validation for Complete Release
        initializeSanityInLiveValidation();

        console.log('✅ Step 9: Sanity in Live section displayed');
    }
}

// Initialize Sanity in Live Validation
function initializeSanityInLiveValidation() {
    const requiredCheckboxes = [
        '#sanityInLiveCheckbox',
        '#milestoneCheckbox',
        '#reverseMergeCheckbox',
        '#learnDocCheckbox',
        '#connectPostCheckbox'
    ];

    requiredCheckboxes.forEach(selector => {
        const checkbox = document.querySelector(selector);
        if (checkbox) {
            checkbox.addEventListener('change', validateSanityInLiveRequirements);
        }
    });

    // Initial validation
    validateSanityInLiveRequirements();
}

// Validate Sanity in Live Requirements
function validateSanityInLiveRequirements() {
    const requiredCheckboxes = [
        '#sanityInLiveCheckbox',
        '#milestoneCheckbox',
        '#reverseMergeCheckbox',
        '#learnDocCheckbox',
        '#connectPostCheckbox'
    ];

    const allChecked = requiredCheckboxes.every(selector => {
        const checkbox = document.querySelector(selector);
        return checkbox && checkbox.checked;
    });

    const checkedCount = requiredCheckboxes.filter(selector => {
        const checkbox = document.querySelector(selector);
        return checkbox && checkbox.checked;
    }).length;

    console.log(`🔍 Sanity in Live validation: ${checkedCount}/${requiredCheckboxes.length} checkboxes completed`);

    if (allChecked) {
        showCompleteReleaseSection();
    } else {
        hideCompleteReleaseSection();
    }
}

// Show Complete Release Section
function showCompleteReleaseSection() {
    const completeReleaseSection = document.getElementById('completeReleaseSection');
    if (completeReleaseSection) {
        completeReleaseSection.style.display = 'block';
        completeReleaseSection.scrollIntoView({ behavior: 'smooth' });

        // Add highlight animation
        completeReleaseSection.classList.add('section-highlight');
        setTimeout(() => {
            completeReleaseSection.classList.remove('section-highlight');
        }, 3000);

        showWorkflowStep(10, 'Ready to Complete Release');
        console.log('✅ Step 10: Complete Release section displayed');
    }
}

// Hide Complete Release Section
function hideCompleteReleaseSection() {
    const completeReleaseSection = document.getElementById('completeReleaseSection');
    if (completeReleaseSection) {
        completeReleaseSection.style.display = 'none';
        console.log('⏳ Complete Release section hidden');
    }
}

// Complete Release Function
function completeRelease() {
    console.log('🚀 Step 10: Completing release process');

    const button = document.getElementById('completeReleaseBtn');
    if (button) {
        button.disabled = true;
        button.textContent = 'Completing Release...';
    }

    // Simulate completion process
    setTimeout(() => {
        // Show success notification
        showNotification('🎉 Release completed successfully! Ready for new NIC Check.', 'success');

        // Complete final workflow step
        completeWorkflowStep(10, 'Release Completed');

        // Save completed build data with comprehensive details
        saveCompletedBuildDataEnhanced();

        // Clear current workflow state to allow new builds
        clearCurrentWorkflowForNewBuild();

        // Reset the entire workflow to initial state
        resetWorkflowToInitialState();

        // Enable all forms for new workflow
        enableAllForms();

        // Switch to Check Status section
        switchToSection('check-status');

        console.log('✅ Step 10 completed: Release finished, workflow cleared for new build, redirected to Check Status');
    }, 2000);
}

// Clear Current Workflow for New Build
function clearCurrentWorkflowForNewBuild() {
    console.log('🗑️ Clearing current workflow to allow new NIC Check builds');

    // Clear current workflow state
    clearWorkflowState();

    // Clear session storage
    sessionStorage.removeItem('currentBuildId');
    sessionStorage.removeItem('workflowStep');
    sessionStorage.removeItem('automationData');

    // Show notification that system is ready for new build
    setTimeout(() => {
        showNotification('System ready for new NIC Check workflow', 'info');
    }, 3000);

    console.log('✅ Workflow cleared - system ready for new builds');
}

// Reset Workflow to Initial State
function resetWorkflowToInitialState() {
    console.log('🔄 Resetting entire workflow to initial state');

    // Clear all form fields
    clearAllFormFields();

    // Uncheck all checkboxes
    uncheckAllCheckboxes();

    // Hide all conditional sections
    hideAllConditionalSections();

    // Reset release type dropdown
    resetReleaseTypeDropdown();

    // Clear session storage
    clearWorkflowSessionData();

    // Reset workflow step indicator
    resetWorkflowStepIndicator();

    console.log('✅ Workflow completely reset to initial state');
}

// Clear All Form Fields
function clearAllFormFields() {
    const formFields = [
        'input[name="buildsetup"]',
        'input[name="zohoshowinput"]',
        'input[name="shapeframework"]',
        'input[name="graphikosmedia"]',
        'input[name="showrenderingframework"]',
        'input[name="showserver"]',
        'input[name="conversion"]',
        'input[name="pictures"]',
        'input[name="imageconversion"]',
        '#manualTestcaseSheet'
    ];

    formFields.forEach(selector => {
        const field = document.querySelector(selector);
        if (field) {
            field.value = '';
        }
    });

    console.log('✅ All form fields cleared');
}

// Uncheck All Checkboxes
function uncheckAllCheckboxes() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    console.log('✅ All checkboxes unchecked');
}

// Hide All Conditional Sections
function hideAllConditionalSections() {
    const sectionsToHide = [
        '#buildFormContainer',
        '#buildForm',
        '#automationSection',
        '#automationReportSection',
        '#preBuildUpdateSection',
        '#manualTestcaseSection',
        '#preBuildVerificationSection',
        '#liveBuildUpdateSection',
        '#sanityInLiveSection',
        '#completeReleaseSection'
    ];

    sectionsToHide.forEach(selector => {
        const section = document.querySelector(selector);
        if (section) {
            section.style.display = 'none';
        }
    });

    console.log('✅ All conditional sections hidden');
}

// Reset Release Type Dropdown
function resetReleaseTypeDropdown() {
    const releaseTypeSelect = document.getElementById('releaseType');
    if (releaseTypeSelect) {
        releaseTypeSelect.value = '';
    }

    console.log('✅ Release type dropdown reset');
}

// Clear Workflow Session Data
function clearWorkflowSessionData() {
    sessionStorage.removeItem('currentBuildId');
    sessionStorage.removeItem('workflowStep');
    sessionStorage.removeItem('automationData');

    console.log('✅ Workflow session data cleared');
}

// Reset Workflow Step Indicator
function resetWorkflowStepIndicator() {
    const stepIndicator = document.getElementById('workflowStepIndicator');
    if (stepIndicator) {
        stepIndicator.innerHTML = `
            <div class="step-progress">
                <div class="step-number">1</div>
                <div class="step-name">Ready to Start</div>
                <div class="step-status">Select Release Type to Begin</div>
            </div>
        `;
    }

    console.log('✅ Workflow step indicator reset');
}

// Disable Previous Forms When Workflow is in Progress
function disablePreviousForms() {
    console.log('🔒 Disabling previous forms to prevent edits');

    // Disable release type dropdown
    const releaseTypeSelect = document.getElementById('releaseType');
    if (releaseTypeSelect) {
        releaseTypeSelect.disabled = true;
        releaseTypeSelect.classList.add('form-disabled');
    }

    // Disable all build form inputs
    const buildFormInputs = document.querySelectorAll('#buildForm input, #buildForm select, #buildForm textarea');
    buildFormInputs.forEach(input => {
        input.disabled = true;
        input.classList.add('form-disabled');
    });

    // Disable build form submit button
    const buildFormButton = document.querySelector('#buildForm button[type="submit"]');
    if (buildFormButton) {
        buildFormButton.disabled = true;
        buildFormButton.classList.add('btn-disabled');
    }

    console.log('✅ Previous forms disabled');
}

// Enable All Forms (for reset)
function enableAllForms() {
    console.log('🔓 Enabling all forms');

    // Enable release type dropdown
    const releaseTypeSelect = document.getElementById('releaseType');
    if (releaseTypeSelect) {
        releaseTypeSelect.disabled = false;
        releaseTypeSelect.classList.remove('form-disabled');
    }

    // Enable all build form inputs
    const buildFormInputs = document.querySelectorAll('#buildForm input, #buildForm select, #buildForm textarea');
    buildFormInputs.forEach(input => {
        input.disabled = false;
        input.classList.remove('form-disabled');
    });

    // Enable build form submit button
    const buildFormButton = document.querySelector('#buildForm button[type="submit"]');
    if (buildFormButton) {
        buildFormButton.disabled = false;
        buildFormButton.classList.remove('btn-disabled');
    }

    console.log('✅ All forms enabled');
}

// Save Current Workflow State with Enhanced Global Persistence
function saveWorkflowState() {
    const currentUser = sessionStorage.getItem('currentUser') || 'unknown';
    const userRole = sessionStorage.getItem('userRole') || 'unknown';

    const workflowState = {
        releaseType: document.getElementById('releaseType')?.value || '',
        buildForm: {
            buildsetup: document.querySelector('input[name="buildsetup"]')?.value || '',
            zohoshowinput: document.querySelector('input[name="zohoshowinput"]')?.value || '',
            shapeframework: document.querySelector('input[name="shapeframework"]')?.value || '',
            graphikosmedia: document.querySelector('input[name="graphikosmedia"]')?.value || '',
            showrenderingframework: document.querySelector('input[name="showrenderingframework"]')?.value || '',
            showserver: document.querySelector('input[name="showserver"]')?.value || '',
            conversion: document.querySelector('input[name="conversion"]')?.value || '',
            pictures: document.querySelector('input[name="pictures"]')?.value || '',
            imageconversion: document.querySelector('input[name="imageconversion"]')?.value || ''
        },
        autoBuildUpdates: {
            enableAutobuildUpdate: document.querySelector('input[name="enableAutobuildUpdate"]')?.checked || false,
            conversionAutoBuildUpdate: document.querySelector('input[name="conversionAutoBuildUpdate"]')?.checked || false,
            picturesAutoBuildUpdate: document.querySelector('input[name="picturesAutoBuildUpdate"]')?.checked || false,
            imageconversionAutoBuildUpdate: document.querySelector('input[name="imageconversionAutoBuildUpdate"]')?.checked || false
        },
        reports: {
            manualSanityReport: document.getElementById('manualSanityReport')?.value || '',
            automationReportUrl: document.getElementById('automationReportUrl')?.value || '',
            manualSanitySheetUrl: document.getElementById('manualSanitySheetUrl')?.value || '',
            creatorNicFormUrl: document.getElementById('creatorNicFormUrl')?.value || '',
            preSanityDetails: document.getElementById('preSanityDetails')?.value || '',
            preAutomationDetails: document.getElementById('preAutomationDetails')?.value || ''
        },
        checkboxes: {
            reportReceived: document.getElementById('reportReceivedCheckbox')?.checked || false,
            manualSanity: document.getElementById('manualSanityCheckbox')?.checked || false,
            manualTestcase: document.getElementById('manualTestcaseCheckbox')?.checked || false,
            preSanity: document.getElementById('preSanityCheckbox')?.checked || false,
            preAutomation: document.getElementById('preAutomationCheckbox')?.checked || false,
            sanityInLive: document.getElementById('sanityInLiveCheckbox')?.checked || false,
            milestone: document.getElementById('milestoneCheckbox')?.checked || false,
            reverseMerge: document.getElementById('reverseMergeCheckbox')?.checked || false,
            learnDoc: document.getElementById('learnDocCheckbox')?.checked || false,
            connectPost: document.getElementById('connectPostCheckbox')?.checked || false
        },
        precheckContents: {
            sdBuildDiffContent: document.getElementById('sdBuildDiffContent')?.value || '',
            changeFileDiffContent: document.getElementById('changeFileDiffContent')?.value || '',
            zdcmDiffContent: document.getElementById('zdcmDiffContent')?.value || '',
            migrationDiffContent: document.getElementById('migrationDiffContent')?.value || ''
        },
        adminPreChecks: {
            sdBuildDiff_qa: document.getElementById('sdBuildDiff_qa')?.checked || false,
            sdBuildDiff_server: document.getElementById('sdBuildDiff_server')?.checked || false,
            sdBuildDiff_client: document.getElementById('sdBuildDiff_client')?.checked || false,
            changeFileDiff_qa: document.getElementById('changeFileDiff_qa')?.checked || false,
            changeFileDiff_server: document.getElementById('changeFileDiff_server')?.checked || false,
            changeFileDiff_client: document.getElementById('changeFileDiff_client')?.checked || false,
            zdcmDiff_qa: document.getElementById('zdcmDiff_qa')?.checked || false,
            zdcmDiff_server: document.getElementById('zdcmDiff_server')?.checked || false,
            zdcmDiff_client: document.getElementById('zdcmDiff_client')?.checked || false,
            migrationDiff_qa: document.getElementById('migrationDiff_qa')?.checked || false,
            migrationDiff_server: document.getElementById('migrationDiff_server')?.checked || false,
            migrationDiff_client: document.getElementById('migrationDiff_client')?.checked || false
        },
        currentStep: sessionStorage.getItem('workflowStep') || '1',
        buildId: sessionStorage.getItem('currentBuildId') || '',
        lastModifiedBy: currentUser,
        lastModifiedRole: userRole,
        timestamp: new Date().toISOString(),
        inProgress: true
    };

    // Save to localStorage for persistence across sessions
    localStorage.setItem('currentWorkflowState', JSON.stringify(workflowState));

    // Also save to a global state key for cross-user synchronization
    localStorage.setItem('globalWorkflowState', JSON.stringify(workflowState));

    console.log('💾 Enhanced workflow state saved:', workflowState);
}

// Load and Restore Enhanced Global Workflow State
function loadWorkflowState() {
    // Try to load global state first (for cross-user synchronization)
    let savedState = localStorage.getItem('globalWorkflowState');
    if (!savedState) {
        // Fallback to current user state
        savedState = localStorage.getItem('currentWorkflowState');
    }

    if (!savedState) {
        console.log('📋 No saved workflow state found');
        return null;
    }

    try {
        const workflowState = JSON.parse(savedState);
        console.log('📋 Loading enhanced workflow state:', workflowState);

        // Restore form values
        if (workflowState.releaseType) {
            const releaseTypeSelect = document.getElementById('releaseType');
            if (releaseTypeSelect) {
                releaseTypeSelect.value = workflowState.releaseType;
                // Trigger change event to show build form
                releaseTypeSelect.dispatchEvent(new Event('change'));
            }
        }

        // Restore build form values
        if (workflowState.buildForm) {
            Object.keys(workflowState.buildForm).forEach(key => {
                const input = document.querySelector(`input[name="${key}"]`);
                if (input && workflowState.buildForm[key]) {
                    input.value = workflowState.buildForm[key];
                }
            });
        }

        // Restore auto build update checkboxes
        if (workflowState.autoBuildUpdates) {
            Object.keys(workflowState.autoBuildUpdates).forEach(key => {
                const checkbox = document.querySelector(`input[name="${key}"]`);
                if (checkbox) {
                    checkbox.checked = workflowState.autoBuildUpdates[key];
                }
            });
        }

        // Restore reports
        if (workflowState.reports) {
            const reportFields = [
                'manualSanityReport',
                'automationReportUrl',
                'manualSanitySheetUrl',
                'creatorNicFormUrl',
                'preSanityDetails',
                'preAutomationDetails'
            ];

            reportFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field && workflowState.reports[fieldId]) {
                    field.value = workflowState.reports[fieldId];
                }
            });
        }

        // Restore checkboxes
        if (workflowState.checkboxes) {
            Object.keys(workflowState.checkboxes).forEach(key => {
                const checkbox = document.getElementById(key + 'Checkbox');
                if (checkbox) {
                    checkbox.checked = workflowState.checkboxes[key];
                }
            });
        }

        // Restore precheck contents
        if (workflowState.precheckContents) {
            Object.keys(workflowState.precheckContents).forEach(key => {
                const textarea = document.getElementById(key);
                if (textarea && workflowState.precheckContents[key]) {
                    textarea.value = workflowState.precheckContents[key];
                }
            });
        }

        // Restore admin pre-checks
        if (workflowState.adminPreChecks) {
            Object.keys(workflowState.adminPreChecks).forEach(key => {
                const checkbox = document.getElementById(key);
                if (checkbox) {
                    checkbox.checked = workflowState.adminPreChecks[key];
                }
            });
        }

        // Restore session data
        if (workflowState.buildId) {
            sessionStorage.setItem('currentBuildId', workflowState.buildId);
        }
        if (workflowState.currentStep) {
            sessionStorage.setItem('workflowStep', workflowState.currentStep);
        }

        // Show notification about state restoration
        if (workflowState.lastModifiedBy && workflowState.lastModifiedBy !== sessionStorage.getItem('currentUser')) {
            showNotification(`Workflow state restored from ${workflowState.lastModifiedBy} (${workflowState.lastModifiedRole})`, 'info');
        }

        // If workflow is in progress, disable previous forms
        if (workflowState.inProgress) {
            setTimeout(() => {
                disablePreviousForms();
                showNotification('Workflow in progress - previous forms disabled for consistency', 'info');
            }, 1000);
        }

        // Re-initialize enhanced form functionality after state restoration
        setTimeout(() => {
            const zohoshowInput = document.getElementById('zohoshowInput');
            if (zohoshowInput && zohoshowInput.value) {
                // Trigger the enhanced form logic for restored values
                zohoshowInput.dispatchEvent(new Event('input'));
            }
        }, 500);

        return workflowState;
    } catch (error) {
        console.error('❌ Error loading workflow state:', error);
        localStorage.removeItem('currentWorkflowState');
        localStorage.removeItem('globalWorkflowState');
        return null;
    }
}

// Clear Workflow State
function clearWorkflowState() {
    localStorage.removeItem('currentWorkflowState');
    console.log('🗑️ Workflow state cleared');
}

// Save Completed Build Data (Legacy - kept for compatibility)
function saveCompletedBuildData() {
    saveCompletedBuildDataEnhanced();
}

// Save Enhanced Completed Build Data with Full Details
function saveCompletedBuildDataEnhanced() {
    const buildId = getCurrentBuildId();
    const timestamp = new Date().toISOString();

    // Get comprehensive form data
    const buildFormDetails = {
        releaseType: document.getElementById('releaseType')?.value || 'Unknown',
        buildSetup: document.querySelector('input[name="buildsetup"]')?.value || 'Unknown',
        zohoShow: document.querySelector('input[name="zohoshowinput"]')?.value || 'Unknown',
        shapeFramework: document.querySelector('input[name="shapeframework"]')?.value || 'Unknown',
        graphikosMedia: document.querySelector('input[name="graphikosmedia"]')?.value || 'Unknown',
        showRenderingFramework: document.querySelector('input[name="showrenderingframework"]')?.value || 'Unknown',
        showServer: document.querySelector('input[name="showserver"]')?.value || 'Unknown',
        conversion: document.querySelector('input[name="conversion"]')?.value || 'Unknown',
        pictures: document.querySelector('input[name="pictures"]')?.value || 'Unknown',
        imageConversion: document.querySelector('input[name="imageconversion"]')?.value || 'Unknown'
    };

    // Get automation details
    const automationDetails = {
        configCompleted: true,
        startTime: new Date().toISOString(),
        reportReceived: document.querySelector('#reportReceivedCheckbox')?.checked || false,
        completionStatus: 'Completed'
    };

    // Get prechecks details (updated for new text input structure)
    const preChecksDetails = {
        // Basic checks
        manualTestcase: document.querySelector('#manualTestcaseCheckbox')?.checked || false,
        manualTestcaseSheet: document.querySelector('#manualTestcaseSheet')?.value || 'Unknown',

        // Prechecks text inputs
        showBuildDiff: document.getElementById('showBuildDiff')?.value || '',
        showMigrationFiles: document.getElementById('showMigrationFiles')?.value || '',
        databaseChanges: document.getElementById('databaseChanges')?.value || '',
        configurationUpdates: document.getElementById('configurationUpdates')?.value || '',
        dependenciesCheck: document.getElementById('dependenciesCheck')?.value || ''
    };

    // Get pre-build update details
    const preBuildUpdateDetails = {
        updateStatus: 'Completed',
        updateTimestamp: new Date().toISOString()
    };

    // Get pre-build verification details
    const preBuildVerificationDetails = {
        preSanity: document.querySelector('#preSanityCheckbox')?.checked || false,
        preAutomation: document.querySelector('#preAutomationCheckbox')?.checked || false
    };

    // Get live update details
    const liveUpdateDetails = {
        liveUpdateStatus: 'Completed',
        sanityInLive: document.querySelector('#sanityInLiveCheckbox')?.checked || false,
        liveUpdateTimestamp: new Date().toISOString()
    };

    // Create workflow timeline
    const workflowTimeline = [
        { step: 1, name: 'Release Type Selection', timestamp: new Date().toISOString(), status: 'Completed' },
        { step: 2, name: 'Build Form Configuration', timestamp: new Date().toISOString(), status: 'Completed' },
        { step: 3, name: 'Automation Configuration', timestamp: new Date().toISOString(), status: 'Completed' },
        { step: 4, name: 'Start Automation', timestamp: new Date().toISOString(), status: 'Completed' },
        { step: 5, name: 'Report Confirmation', timestamp: new Date().toISOString(), status: 'Completed' },
        { step: 6, name: 'Pre-Build Update', timestamp: new Date().toISOString(), status: 'Completed' },
        { step: 7, name: 'Pre-Build Verification', timestamp: new Date().toISOString(), status: 'Completed' },
        { step: 8, name: 'Live Update', timestamp: new Date().toISOString(), status: 'Completed' },
        { step: 9, name: 'Sanity in Live', timestamp: new Date().toISOString(), status: 'Completed' },
        { step: 10, name: 'Release Completion', timestamp: new Date().toISOString(), status: 'Completed' }
    ];

    // Create comprehensive completed build object
    const completedBuild = {
        id: buildId,
        timestamp: timestamp,
        status: 'Completed',
        buildFormDetails: buildFormDetails,
        automationDetails: automationDetails,
        preChecksDetails: preChecksDetails,
        preBuildUpdateDetails: preBuildUpdateDetails,
        preBuildVerificationDetails: preBuildVerificationDetails,
        liveUpdateDetails: liveUpdateDetails,
        workflowTimeline: workflowTimeline
    };

    // Get existing completed builds from localStorage
    let completedBuilds = JSON.parse(localStorage.getItem('completedBuilds') || '[]');

    // Add new build to the beginning of the array
    completedBuilds.unshift(completedBuild);

    // Save back to localStorage
    localStorage.setItem('completedBuilds', JSON.stringify(completedBuilds));

    console.log('✅ Enhanced completed build data saved:', completedBuild);
    console.log('📊 Total completed builds:', completedBuilds.length);
}

// Generate Unique Build ID for Multiple Submissions
function generateUniqueBuildId() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `BUILD-${timestamp}-${random}`;
}

// Start New Workflow
function startNewWorkflow() {
    if (confirm('Are you sure you want to start a new workflow? This will reset all current progress.')) {
        // Clear session storage
        sessionStorage.removeItem('currentBuildId');

        // Reload the page to start fresh
        window.location.reload();
    }
}

// View Build Details
function viewBuildDetails() {
    const buildId = getCurrentBuildId();
    if (buildId) {
        // Switch to Check Status section to view build details
        switchToSection('check-status');
        showNotification('Switched to Check Status to view build details', 'info');
    }
}

// Search functionality
function searchTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr:not(.empty-row)');
    let visibleCount = 0;

    searchTerm = searchTerm.toLowerCase();

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let rowText = '';

        // Combine all cell text for searching
        cells.forEach(cell => {
            rowText += cell.textContent.toLowerCase() + ' ';
        });

        if (rowText.includes(searchTerm) || searchTerm === '') {
            row.style.display = '';
            visibleCount++;

            // Highlight search terms
            if (searchTerm !== '') {
                highlightSearchTerms(row, searchTerm);
            } else {
                removeHighlights(row);
            }
        } else {
            row.style.display = 'none';
            removeHighlights(row);
        }
    });

    // Update table info
    const tableType = tableId.replace('Table', '');
    updateTableInfo(tableType, visibleCount);
}

// Clear search
function clearSearch(tableType) {
    const searchInput = document.getElementById(tableType + 'Search');
    searchInput.value = '';
    searchTable(tableType + 'Table', '');
}

// Highlight search terms
function highlightSearchTerms(row, searchTerm) {
    const cells = row.querySelectorAll('td');
    cells.forEach(cell => {
        const originalText = cell.textContent;
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
        if (highlightedText !== originalText) {
            cell.innerHTML = highlightedText;
        }
    });
}

// Remove highlights
function removeHighlights(row) {
    const marks = row.querySelectorAll('mark');
    marks.forEach(mark => {
        mark.outerHTML = mark.textContent;
    });
}

// Sort table
let sortStates = {}; // Track sort direction for each column

function sortTable(tableId, columnIndex) {
    const table = document.getElementById(tableId);
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr:not(.empty-row)'));

    if (rows.length === 0) return;

    // Determine sort direction
    const sortKey = `${tableId}_${columnIndex}`;
    const isAscending = !sortStates[sortKey];
    sortStates[sortKey] = isAscending;

    // Update sort arrows
    updateSortArrows(table, columnIndex, isAscending);

    // Sort rows
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();

        // Determine data type and sort accordingly
        let comparison = 0;

        if (isNumeric(aText) && isNumeric(bText)) {
            comparison = parseFloat(aText) - parseFloat(bText);
        } else if (isDate(aText) && isDate(bText)) {
            comparison = new Date(aText) - new Date(bText);
        } else {
            comparison = aText.localeCompare(bText);
        }

        return isAscending ? comparison : -comparison;
    });

    // Reorder rows in DOM
    rows.forEach(row => tbody.appendChild(row));

    // Store sort state in sessionStorage
    sessionStorage.setItem('tableSortState', JSON.stringify(sortStates));
}

// Update sort arrows
function updateSortArrows(table, activeColumn, isAscending) {
    const headers = table.querySelectorAll('th .sort-arrow');
    headers.forEach((arrow, index) => {
        if (index === activeColumn) {
            arrow.textContent = isAscending ? '▲' : '▼';
            arrow.style.color = '#667eea';
        } else {
            arrow.textContent = '⇅';
            arrow.style.color = '#ccc';
        }
    });
}

// Helper functions
function isNumeric(str) {
    return !isNaN(str) && !isNaN(parseFloat(str));
}

function isDate(str) {
    return !isNaN(Date.parse(str));
}

// Update table info
function updateTableInfo(tableType, visibleCount = null) {
    const table = document.getElementById(tableType + 'Table');
    const tbody = table.querySelector('tbody');
    const totalRows = tbody.querySelectorAll('tr:not(.empty-row)').length;
    const visibleRows = visibleCount !== null ? visibleCount : tbody.querySelectorAll('tr:not(.empty-row):not([style*="display: none"])').length;

    const visibleCountSpan = document.getElementById(tableType + 'VisibleCount');
    const totalCountSpan = document.getElementById(tableType + 'TotalCount');

    if (visibleCountSpan) visibleCountSpan.textContent = visibleRows;
    if (totalCountSpan) totalCountSpan.textContent = totalRows;
}
