// Admin Page functionality for GATE

document.addEventListener('DOMContentLoaded', function() {
    initializeAdminPage();
});

function initializeAdminPage() {
    // Initialize add user form
    const addUserForm = document.getElementById('addUserForm');
    if (addUserForm) {
        addUserForm.addEventListener('submit', handleAddUser);
    }

    // Initialize role change tracking
    trackRoleChanges();
}

// Get CSRF token from meta tags
function getCSRFToken() {
    const token = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
    console.log('CSRF Token:', token);
    return token;
}

function getCSRFHeader() {
    const header = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');
    console.log('CSRF Header:', header);
    return header;
}

// Create headers with CSRF token
function createHeaders() {
    const headers = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    };

    const csrfToken = getCSRFToken();
    const csrfHeader = getCSRFHeader();

    if (csrfToken && csrfHeader) {
        headers[csrfHeader] = csrfToken;
    }

    console.log('Request Headers:', headers);
    return headers;
}

// Track role changes for bulk save
let roleChanges = new Map();

function trackRoleChanges() {
    const roleSelects = document.querySelectorAll('.role-select');
    roleSelects.forEach(select => {
        const originalRole = select.dataset.originalRole;
        
        select.addEventListener('change', function() {
            const userId = this.dataset.userId;
            const newRole = this.value;
            
            if (newRole !== originalRole) {
                roleChanges.set(userId, newRole);
                // Visual indicator of change
                this.style.borderColor = '#ffc107';
                this.style.backgroundColor = '#fff3cd';
            } else {
                roleChanges.delete(userId);
                // Reset visual indicator
                this.style.borderColor = '#e9ecef';
                this.style.backgroundColor = 'white';
            }
            
            // Update save button state
            updateSaveButtonState();
        });
    });
}

function updateSaveButtonState() {
    const saveButton = document.querySelector('button[onclick="saveAllRoleChanges()"]');
    if (saveButton) {
        if (roleChanges.size > 0) {
            saveButton.textContent = `Save ${roleChanges.size} Change${roleChanges.size > 1 ? 's' : ''}`;
            saveButton.classList.remove('btn-success');
            saveButton.classList.add('btn-warning');
        } else {
            saveButton.textContent = 'Save All Changes';
            saveButton.classList.remove('btn-warning');
            saveButton.classList.add('btn-success');
        }
    }
}

// Update individual user role
function updateUserRole(button) {
    const userId = button.dataset.userId;
    const select = document.querySelector(`.role-select[data-user-id="${userId}"]`);
    const newRole = select.value;
    
    button.disabled = true;
    button.textContent = 'Updating...';
    
    const updates = [{
        userId: userId,
        role: newRole
    }];
    
    fetch('/gqa/api/admin/users/update-roles', {
        method: 'POST',
        headers: createHeaders(),
        body: JSON.stringify(updates)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('User role updated successfully', 'success');
            
            // Update original role and reset visual indicators
            select.dataset.originalRole = newRole;
            select.style.borderColor = '#e9ecef';
            select.style.backgroundColor = 'white';
            roleChanges.delete(userId);
            updateSaveButtonState();
        } else {
            showNotification('Error updating user role: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error updating role:', error);
        showNotification('Network error occurred', 'error');
    })
    .finally(() => {
        button.disabled = false;
        button.textContent = 'Update';
    });
}

// Save all role changes
function saveAllRoleChanges() {
    if (roleChanges.size === 0) {
        showNotification('No changes to save', 'info');
        return;
    }
    
    const updates = Array.from(roleChanges.entries()).map(([userId, role]) => ({
        userId: userId,
        role: role
    }));
    
    const saveButton = document.querySelector('button[onclick="saveAllRoleChanges()"]');
    saveButton.disabled = true;
    saveButton.textContent = 'Saving...';
    
    fetch('/gqa/api/admin/users/update-roles', {
        method: 'POST',
        headers: createHeaders(),
        body: JSON.stringify(updates)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`${updates.length} user role${updates.length > 1 ? 's' : ''} updated successfully`, 'success');
            
            // Update original roles and reset visual indicators
            roleChanges.forEach((role, userId) => {
                const select = document.querySelector(`.role-select[data-user-id="${userId}"]`);
                if (select) {
                    select.dataset.originalRole = role;
                    select.style.borderColor = '#e9ecef';
                    select.style.backgroundColor = 'white';
                }
            });
            
            roleChanges.clear();
            updateSaveButtonState();
        } else {
            showNotification('Error updating user roles: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error updating roles:', error);
        showNotification('Network error occurred', 'error');
    })
    .finally(() => {
        saveButton.disabled = false;
    });
}

// Handle add user form submission
function handleAddUser(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const userData = {
        fullName: formData.get('fullName'),
        username: formData.get('username'),
        email: formData.get('email'),
        password: formData.get('password'),
        role: formData.get('role')
    };
    
    // Validate form
    if (!userData.fullName || !userData.username || !userData.email || !userData.password || !userData.role) {
        showNotification('Please fill in all fields', 'error');
        return;
    }
    
    const submitButton = event.target.querySelector('button[type="submit"]');
    submitButton.disabled = true;
    submitButton.textContent = 'Creating...';
    
    fetch('/gqa/api/admin/users/create', {
        method: 'POST',
        headers: createHeaders(),
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('User created successfully', 'success');
            event.target.reset();
            
            // Refresh the page to show the new user
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification('Error creating user: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error creating user:', error);
        showNotification('Network error occurred', 'error');
    })
    .finally(() => {
        submitButton.disabled = false;
        submitButton.textContent = 'Create User';
    });
}

// Clear Current Workflow State (Ultimate Role Only)
function clearCurrentWorkflowState() {
    // Verify Ultimate role access
    const userRole = document.body.dataset.userRole;
    if (userRole !== 'ULTIMATE') {
        alert('❌ ACCESS DENIED\n\nOnly users with ULTIMATE role can clear workflow state.');
        console.error('Access denied: User role is', userRole, 'but ULTIMATE required');
        return;
    }

    // Show targeted confirmation dialog
    const confirmed = confirm(
        '🔄 CLEAR CURRENT WORKFLOW STATE\n\n' +
        'This action will:\n' +
        '• Clear current active NIC Check form data\n' +
        '• Remove current workflow progress and states\n' +
        '• Reset prechecks management data for current session\n' +
        '• Clear temporary form validation states\n' +
        '• Affect current workflow only (NOT historical data)\n\n' +
        '✅ WILL NOT AFFECT:\n' +
        '• Historical NIC Check entries in Check Status\n' +
        '• Completed workflow records in database\n' +
        '• Other users\' collaborative state data\n\n' +
        'Continue with clearing current workflow state?'
    );

    if (!confirmed) {
        return;
    }

    // Second confirmation for critical action
    const doubleConfirmed = confirm(
        '🔴 FINAL CONFIRMATION\n\n' +
        'You are about to clear ALL application state for ALL users.\n' +
        'This will reset the entire application to a clean state.\n\n' +
        'Click OK to proceed with clearing all state.'
    );

    if (!doubleConfirmed) {
        return;
    }

    console.log('🔄 Clearing all application state by Ultimate admin');

    try {
        console.log('🔄 Starting targeted workflow state clearing...');

        // Clear only workflow-related localStorage data
        const workflowKeys = ['currentWorkflowState', 'globalWorkflowState', 'nicCheckFormData'];
        workflowKeys.forEach(key => {
            if (localStorage.getItem(key)) {
                localStorage.removeItem(key);
                console.log('📋 Cleared localStorage key:', key);
            }
        });

        // Clear only workflow-related sessionStorage data
        const sessionKeys = ['currentBuildId', 'workflowStep', 'formValidationState'];
        sessionKeys.forEach(key => {
            if (sessionStorage.getItem(key)) {
                sessionStorage.removeItem(key);
                console.log('📋 Cleared sessionStorage key:', key);
            }
        });

        // Clear any cookies related to the application
        document.cookie.split(";").forEach(function(c) {
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
        });

        // Clear any IndexedDB data if present
        if ('indexedDB' in window) {
            indexedDB.databases().then(databases => {
                databases.forEach(db => {
                    indexedDB.deleteDatabase(db.name);
                });
            }).catch(err => console.log('IndexedDB clearing skipped:', err));
        }

        // Delete saved entries and reset form state on server if needed
        try {
            fetch('/api/admin/clear-all-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            }).then(response => {
                if (response.ok) {
                    console.log('✅ Server-side data cleared successfully');
                } else {
                    console.log('⚠️ Server-side clearing may have failed, but local clearing completed');
                }
            }).catch(err => {
                console.log('⚠️ Server-side clearing request failed:', err);
            });
        } catch (error) {
            console.log('⚠️ Could not send server clearing request:', error);
        }

        // Show success notification
        showNotification('🎉 All application state cleared successfully!', 'success');

        // Broadcast to all open tabs/windows
        if (typeof BroadcastChannel !== 'undefined') {
            const channel = new BroadcastChannel('gate-admin');
            channel.postMessage({
                type: 'STATE_CLEARED',
                timestamp: new Date().toISOString(),
                clearedBy: 'ultimate-admin'
            });
        }

        // Force reload all tabs/windows
        if (typeof BroadcastChannel !== 'undefined') {
            const reloadChannel = new BroadcastChannel('gate-reload');
            reloadChannel.postMessage({
                type: 'FORCE_RELOAD',
                timestamp: new Date().toISOString()
            });
        }

        console.log('✅ Comprehensive application state clearing completed');

        // Reload current page after delay
        setTimeout(() => {
            window.location.href = '/gqa/login?cleared=true';
        }, 2000);

    } catch (error) {
        console.error('❌ Error clearing application state:', error);
        showNotification('Error occurred while clearing state. Please try again.', 'error');
    }
}
