// Main JavaScript functionality for Graphikos Automation

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // Initialize page-specific functionality
    initializePageFunctionality();
});

function initializePageFunctionality() {
    const currentPath = window.location.pathname;
    
    if (currentPath.includes('nic-checks')) {
        initializeNicChecks();
    } else if (currentPath.includes('update-build')) {
        initializeUpdateBuild();
    } else if (currentPath.includes('check-status')) {
        initializeCheckStatus();
    }
}

// NIC Checks functionality
function initializeNicChecks() {
    const releaseTypeSelect = document.getElementById('releaseType');
    const buildForm = document.getElementById('buildForm');
    const automationConfigForm = document.getElementById('automationConfigForm');
    
    if (releaseTypeSelect) {
        releaseTypeSelect.addEventListener('change', function() {
            if (this.value) {
                buildForm.style.display = 'block';
                showNotification('Release type selected: ' + this.value, 'success');
            } else {
                buildForm.style.display = 'none';
            }
        });
    }

    // Build form submission
    if (buildForm) {
        buildForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitBuildForm();
        });
    }

    // Automation form submission
    if (automationConfigForm) {
        automationConfigForm.addEventListener('submit', function(e) {
            e.preventDefault();
            startAutomation();
        });
    }

    // Initialize checkbox handlers
    initializeCheckboxHandlers();
    
    // Initialize precheck handlers for admin users
    initializePrecheckHandlers();
}

function submitBuildForm() {
    const formData = new FormData(document.getElementById('buildForm'));
    const buildData = Object.fromEntries(formData.entries());
    
    // Add release type
    buildData.releaseType = document.getElementById('releaseType').value;
    
    // Handle checkboxes
    buildData.enableAutobuildUpdate = document.querySelector('input[name="enableAutobuildUpdate"]').checked;
    buildData.conversionAutoBuildUpdate = document.querySelector('input[name="conversionAutoBuildUpdate"]').checked;
    buildData.picturesAutoBuildUpdate = document.querySelector('input[name="picturesAutoBuildUpdate"]').checked;
    buildData.imageconversionAutoBuildUpdate = document.querySelector('input[name="imageconversionAutoBuildUpdate"]').checked;

    fetch('/gqa/api/builds/submit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(buildData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Build submitted successfully!', 'success');
            updateBuildStatus('Build submitted and processing...');
            
            // Store build ID for later use
            window.currentBuildId = data.buildId;
            
            // Show automation section
            document.getElementById('automationSection').style.display = 'block';
        } else {
            showNotification('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error occurred', 'error');
    });
}

function startAutomation() {
    if (!window.currentBuildId) {
        showNotification('Please submit build first', 'warning');
        return;
    }

    const startButton = document.getElementById('startAutomationBtn');
    startButton.disabled = true;
    startButton.textContent = 'Starting Automation...';

    fetch(`/gqa/api/builds/${window.currentBuildId}/start-automation`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Automation started successfully!', 'success');
            
            // Show automation report URL
            const reportSection = document.getElementById('automationReportSection');
            const reportUrl = document.getElementById('automationReportUrl');
            reportUrl.href = data.reportUrl;
            reportUrl.textContent = data.reportUrl;
            reportSection.style.display = 'block';
            
            // Enable report received checkbox
            document.getElementById('reportReceivedCheckbox').disabled = false;
            
            // Show manual testcase section
            document.getElementById('manualTestcaseSection').style.display = 'block';
        } else {
            showNotification('Error: ' + data.message, 'error');
        }
        
        startButton.disabled = false;
        startButton.textContent = 'Start Automation';
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error occurred', 'error');
        startButton.disabled = false;
        startButton.textContent = 'Start Automation';
    });
}

function initializeCheckboxHandlers() {
    const reportReceivedCheckbox = document.getElementById('reportReceivedCheckbox');
    const manualTestcaseCheckbox = document.getElementById('manualTestcaseCheckbox');
    
    if (reportReceivedCheckbox) {
        reportReceivedCheckbox.addEventListener('change', function() {
            updateCheckboxStatus('reportReceived', this.checked);
            checkPreBuildUpdateConditions();
        });
    }
    
    if (manualTestcaseCheckbox) {
        manualTestcaseCheckbox.addEventListener('change', function() {
            updateCheckboxStatus('manualTestcaseConfirmed', this.checked);
            checkPreBuildUpdateConditions();
        });
    }

    // Pre-build checkboxes
    const preSanityCheckbox = document.getElementById('preSanityCheckbox');
    const preAutomationCheckbox = document.getElementById('preAutomationCheckbox');
    
    if (preSanityCheckbox) {
        preSanityCheckbox.addEventListener('change', function() {
            updateCheckboxStatus('preSanity', this.checked);
        });
    }
    
    if (preAutomationCheckbox) {
        preAutomationCheckbox.addEventListener('change', function() {
            updateCheckboxStatus('preAutomation', this.checked);
        });
    }

    // Final sanity checkbox
    const finalSanityCheckbox = document.getElementById('finalSanityCheckbox');
    if (finalSanityCheckbox) {
        finalSanityCheckbox.addEventListener('change', function() {
            updateCheckboxStatus('finalSanityCompleted', this.checked);
            if (this.checked) {
                document.getElementById('completeButton').style.display = 'block';
            }
        });
    }
}

function checkPreBuildUpdateConditions() {
    const reportReceived = document.getElementById('reportReceivedCheckbox').checked;
    const manualTestcaseConfirmed = document.getElementById('manualTestcaseCheckbox').checked;
    
    if (reportReceived && manualTestcaseConfirmed) {
        document.getElementById('preBuildUpdateSection').style.display = 'block';
        showNotification('Build is started and once completed automation will be triggered', 'info');
    }
}

function updateBuildInPre() {
    if (!window.currentBuildId) {
        showNotification('No active build found', 'error');
        return;
    }

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'PRE_BUILD_UPDATED' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Pre build updated successfully!', 'success');
            document.getElementById('preCheckboxesSection').style.display = 'block';
            
            // Show pre automation checkbox after report is available
            if (document.getElementById('automationReportUrl').href) {
                document.getElementById('preAutomationCheckbox').parentElement.style.display = 'block';
            }
        } else {
            showNotification('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error occurred', 'error');
    });
}

function updateCheckboxStatus(checkboxName, checked) {
    if (!window.currentBuildId) return;

    const data = {};
    data[checkboxName] = checked;

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-checkboxes`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            showNotification('Error updating checkbox: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function initializePrecheckHandlers() {
    const isAdmin = document.body.dataset.userRole === 'ADMIN';
    if (!isAdmin) return;

    const precheckCheckboxes = document.querySelectorAll('.precheck-checkbox input[type="checkbox"]');
    precheckCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updatePrecheckStatus();
            checkLiveBuildUpdateConditions();
        });
    });

    // Precheck textarea handlers
    const precheckTextareas = document.querySelectorAll('.precheck-textarea');
    precheckTextareas.forEach(textarea => {
        textarea.addEventListener('blur', function() {
            savePrecheckContent();
        });
    });
}

function updatePrecheckStatus() {
    if (!window.currentBuildId) return;

    const precheckData = {
        buildDiffChecked: document.getElementById('buildDiffChecked').checked,
        changedFilesChecked: document.getElementById('changedFilesChecked').checked,
        zdcmFilesChecked: document.getElementById('zdcmFilesChecked').checked,
        migrationFilesChecked: document.getElementById('migrationFilesChecked').checked
    };

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-prechecks`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(precheckData)
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            showNotification('Error updating prechecks: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function savePrecheckContent() {
    if (!window.currentBuildId) return;

    const contentData = {
        buildDiffContent: document.getElementById('buildDiffContent').value,
        changedFilesContent: document.getElementById('changedFilesContent').value,
        zdcmFilesContent: document.getElementById('zdcmFilesContent').value,
        migrationFilesContent: document.getElementById('migrationFilesContent').value
    };

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-prechecks`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(contentData)
    })
    .catch(error => {
        console.error('Error saving precheck content:', error);
    });
}

function checkLiveBuildUpdateConditions() {
    const allChecked = document.getElementById('buildDiffChecked').checked &&
                      document.getElementById('changedFilesChecked').checked &&
                      document.getElementById('zdcmFilesChecked').checked &&
                      document.getElementById('migrationFilesChecked').checked;

    if (allChecked) {
        document.getElementById('liveBuildUpdateSection').style.display = 'block';
    }
}

function updateBuildToLive() {
    if (!window.currentBuildId) {
        showNotification('No active build found', 'error');
        return;
    }

    const button = document.getElementById('updateToLiveBtn');
    button.disabled = true;
    button.textContent = 'Updating...';

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'LIVE_BUILD_UPDATED' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Live build updated successfully!', 'success');
            updateBuildStatus('Success - Build updated to live');
            document.getElementById('finalSanitySection').style.display = 'block';
        } else {
            showNotification('Error: ' + data.message, 'error');
            updateBuildStatus('Failed - ' + data.message);
        }
        
        button.disabled = false;
        button.textContent = 'Update Build to Live';
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error occurred', 'error');
        updateBuildStatus('Failed - Network error');
        button.disabled = false;
        button.textContent = 'Update Build to Live';
    });
}

function completeBuild() {
    if (!window.currentBuildId) {
        showNotification('No active build found', 'error');
        return;
    }

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'COMPLETED' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Build completed successfully!', 'success');
            
            // Get final JSON data
            return fetch(`/gqa/api/builds/${window.currentBuildId}/json`);
        } else {
            throw new Error(data.message);
        }
    })
    .then(response => response.json())
    .then(jsonData => {
        console.log('Final build data:', jsonData);
        showNotification('Build data saved in JSON format', 'success');
        
        // Optionally redirect to status page
        setTimeout(() => {
            window.location.href = '/gqa/check-status';
        }, 2000);
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error completing build: ' + error.message, 'error');
    });
}

function updateBuildStatus(message) {
    const statusElement = document.getElementById('buildStatus');
    if (statusElement) {
        statusElement.textContent = message;
        statusElement.style.display = 'block';
    }
}

// Update Build page functionality
function initializeUpdateBuild() {
    // Implementation for update build page
    console.log('Update Build page initialized');
}

// Check Status page functionality
function initializeCheckStatus() {
    loadBuildStatusTable();
}

function loadBuildStatusTable() {
    // This would be implemented to load and display the status table
    console.log('Loading build status table');
}

// Utility functions
function updateBuildStatus(message) {
    const statusContent = document.querySelector('.status-content');
    if (statusContent) {
        statusContent.textContent = message;
        statusContent.parentElement.style.display = 'block';
    }
}
