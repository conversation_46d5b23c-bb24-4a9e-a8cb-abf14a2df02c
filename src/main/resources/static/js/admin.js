// Admin Panel functionality for GATE

function openAdminPanel() {
    const modal = document.getElementById('adminPanelModal');
    modal.style.display = 'flex';
    loadUserManagement();
}

function closeAdminPanel() {
    const modal = document.getElementById('adminPanelModal');
    modal.style.display = 'none';
}

function loadUserManagement() {
    fetch('/gqa/api/admin/users')
        .then(response => response.json())
        .then(users => {
            const container = document.getElementById('userManagementList');
            container.innerHTML = '';
            
            users.forEach(user => {
                const userRow = document.createElement('div');
                userRow.className = 'user-management-row';
                userRow.innerHTML = `
                    <div class="user-info-admin">
                        <span class="user-name-admin">${user.fullName}</span>
                        <span class="user-username-admin">(${user.username})</span>
                    </div>
                    <div class="user-role-selector">
                        <select data-user-id="${user.id}" class="role-select">
                            <option value="EDITOR" ${user.role === 'EDITOR' ? 'selected' : ''}>Editor</option>
                            <option value="ADMIN" ${user.role === 'ADMIN' ? 'selected' : ''}>Admin</option>
                            <option value="QA" ${user.role === 'QA' ? 'selected' : ''}>QA</option>
                            <option value="ULTIMATE" ${user.role === 'ULTIMATE' ? 'selected' : ''}>Ultimate</option>
                        </select>
                    </div>
                `;
                container.appendChild(userRow);
            });
        })
        .catch(error => {
            console.error('Error loading users:', error);
            showNotification('Error loading user list', 'error');
        });
}

function saveUserRoles() {
    const roleSelects = document.querySelectorAll('.role-select');
    const updates = [];
    
    roleSelects.forEach(select => {
        updates.push({
            userId: select.dataset.userId,
            role: select.value
        });
    });
    
    fetch('/gqa/api/admin/users/update-roles', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('User roles updated successfully', 'success');
            closeAdminPanel();
        } else {
            showNotification('Error updating user roles: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error updating roles:', error);
        showNotification('Network error occurred', 'error');
    });
}

function showRegisterForm() {
    const registerSection = document.getElementById('registerFormSection');
    registerSection.style.display = 'block';

    // Initialize form submission
    const form = document.getElementById('adminRegisterForm');
    form.addEventListener('submit', handleRegisterSubmit);
}

function hideRegisterForm() {
    const registerSection = document.getElementById('registerFormSection');
    registerSection.style.display = 'none';

    // Clear form
    const form = document.getElementById('adminRegisterForm');
    form.reset();
}

function handleRegisterSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const userData = {
        fullName: formData.get('fullName'),
        username: formData.get('username'),
        email: formData.get('email'),
        password: formData.get('password'),
        role: formData.get('role')
    };

    fetch('/gqa/api/admin/users/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('User created successfully', 'success');
            hideRegisterForm();
            loadUserManagement(); // Refresh user list
        } else {
            showNotification('Error creating user: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error creating user:', error);
        showNotification('Network error occurred', 'error');
    });
}

// Close modal when clicking outside
window.addEventListener('click', function(event) {
    const modal = document.getElementById('adminPanelModal');
    if (event.target === modal) {
        closeAdminPanel();
    }
});
