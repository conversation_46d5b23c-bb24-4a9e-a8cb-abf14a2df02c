/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
    z-index: 1100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

/* Main page logo - left aligned */
.logo {
    flex-shrink: 0;
}

/* Admin page specific logo centering */
.admin-page .header-content {
    justify-content: center;
    position: relative;
}

.admin-page .logo {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.admin-page .user-menu {
    position: absolute;
    right: 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-img {
    width: 120px;
    height: 50px;
    border-radius: 6px;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

/* Navigation Styles */
.nav-menu {
    flex: 1;
    margin: 0 2rem;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
    justify-content: center;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

/* User Menu */
.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.75rem;
    opacity: 0.8;
    background-color: rgba(255,255,255,0.2);
    padding: 2px 8px;
    border-radius: 12px;
}

.logout-btn {
    background-color: #e74c3c;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Main Content - Expanded Full Width */
.main-content {
    flex: 1;
    padding: 1rem;
    width: 100%;
    max-width: 100%;
}

/* Two-Column Layout */
#mainContent {
    display: flex;
    gap: 2rem;
    width: 100%;
    min-height: calc(100vh - 120px);
}

/* Left Panel - Workflow */
.left-panel {
    flex: 1;
    min-width: 0; /* Allows flex item to shrink below content size */
    background: #ffffff;
    border-radius: 8px;
    padding: 1.5rem;
}

/* Right Panel - Prechecks Management */
.right-panel {
    flex: 0 0 400px; /* Fixed width, no grow, no shrink */
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
}

/* Build Details Panel - Slides in from right */
#buildDetailsPanel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 20px rgba(0,0,0,0.15);
    z-index: 1000;
    transition: right 0.3s ease;
    overflow-y: auto;
    border-left: 1px solid #dee2e6;
}

#buildDetailsPanel.panel-open {
    right: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .right-panel {
        flex: 0 0 350px;
    }
}

@media (max-width: 992px) {
    #mainContent {
        flex-direction: column;
        gap: 1rem;
    }

    .right-panel {
        flex: 1;
        position: static;
        order: -1; /* Show prechecks at top on mobile */
    }

    .left-panel {
        order: 1;
    }

    .right-panel {
        position: static;
    }

    /* Build Details Panel - Full width on mobile */
    #buildDetailsPanel {
        right: -100%;
        width: 100%;
    }

    #buildDetailsPanel.panel-open {
        right: 0;
    }
}

.content-wrapper {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

/* Single Page Application Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* NIC Checks Container */
.nic-checks-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
    align-items: start;
}

.main-panel {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.report-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    cursor: pointer;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.notification-text {
    background-color: #d4edda;
    color: #155724;
    padding: 0.75rem;
    border-radius: 6px;
    margin-top: 1rem;
    font-style: italic;
}

.build-status-label {
    margin-top: 1rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 6px;
}

/* Page Headers */
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
}

/* Form Styles */
.form-container {
    max-width: 800px;
    margin: 0 auto;
}

.form-row {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.form-label {
    min-width: 120px;
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
}

.form-inputs {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-inputs input {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-inputs input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.combo-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.combo-row input {
    flex: 1;
    min-width: 200px;
}

.combo-row label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    white-space: nowrap;
}

.combo-row select {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 150px;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-success {
    background-color: #27ae60;
    color: white;
}

.btn-success:hover {
    background-color: #229954;
    transform: translateY(-2px);
}

.btn-warning {
    background-color: #f39c12;
    color: white;
}

.btn-warning:hover {
    background-color: #e67e22;
    transform: translateY(-2px);
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
}

/* Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.dropdown select {
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    background-color: white;
    cursor: pointer;
    min-width: 200px;
}

.dropdown select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Status Area */
.status-area {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 2rem 0;
}

.status-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.status-content {
    color: #6c757d;
    font-style: italic;
}

/* Automation Config */
.automation-config {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 1.5rem;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    margin: 1.5rem 0;
    overflow-x: auto;
}

/* Prechecks Panel */
.prechecks-panel {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.prechecks-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.precheck-item {
    margin-bottom: 1.5rem;
}

.precheck-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: block;
}

.precheck-textarea {
    width: 100%;
    min-height: 120px;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    resize: vertical;
}

.precheck-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.precheck-checkbox {
    margin-top: 0.5rem;
}

.precheck-checkbox label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #27ae60;
}

/* Footer */
.footer {
    background-color: #2c3e50;
    color: white;
    padding: 1.5rem 0;
    text-align: center;
    margin-top: auto;
}

.footer-content p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

/* Notifications */
.notifications {
    position: fixed;
    top: 80px; /* Position below header */
    right: 20px;
    z-index: 1050; /* Below header but above other content */
    max-width: 400px;
}

.notification {
    background-color: white;
    border-left: 4px solid #667eea;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left-color: #27ae60;
}

.notification.error {
    border-left-color: #e74c3c;
}

.notification.warning {
    border-left-color: #f39c12;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Dashboard Styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 1rem;
    font-weight: bold;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.stat-label {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

.quick-actions {
    margin-bottom: 2rem;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.action-btn.tertiary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.action-icon {
    font-size: 1rem;
    font-weight: bold;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.2);
    border-radius: 12px;
}

.action-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.action-content p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.builds-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.builds-table {
    width: 100%;
    border-collapse: collapse;
}

.builds-table th,
.builds-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.builds-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.release-type {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.release-type.hotfix {
    background-color: #fff3cd;
    color: #856404;
}

.release-type.release {
    background-color: #d4edda;
    color: #155724;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.submitted {
    background-color: #cce5ff;
    color: #0066cc;
}

.status-badge.in_progress {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.completed {
    background-color: #d4edda;
    color: #155724;
}

.table-action {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.table-action:hover {
    background-color: #667eea;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-icon {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #667eea;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.empty-state p {
    margin-bottom: 2rem;
}

/* Automation Configuration Form Styles */
.automation-config-form {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
}

.config-group {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.config-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.config-group-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #667eea;
}

.form-input,
.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background-color: white;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

.checkbox-row {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.checkbox-row .checkbox-label {
    margin: 0;
}

.checkbox-row input[type="checkbox"] {
    margin-right: 0.5rem;
    width: auto;
}

/* Status Table Styles */
.empty-row {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem;
}

/* Update Build Container */
.update-build-container {
    max-width: 800px;
    margin: 0 auto;
}

/* Admin Panel Styles */
.settings-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 2px solid transparent;
    color: white;
    cursor: pointer;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    margin-right: 1rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.settings-btn:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.settings-btn svg {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.admin-panel {
    max-width: 600px;
    width: 90%;
}

.admin-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.admin-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.admin-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.user-management-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.user-info-admin {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-name-admin {
    font-weight: 600;
    color: #2c3e50;
}

.user-username-admin {
    font-size: 0.85rem;
    color: #6c757d;
}

.role-select {
    padding: 0.5rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    background-color: white;
    min-width: 120px;
}

.role-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.report-item {
    margin-bottom: 0.75rem;
}

.report-link {
    display: inline-block;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.report-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    color: white;
}

/* Refresh Icon Button */
.refresh-icon-btn {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    margin-left: 0.5rem;
}

.refresh-icon-btn:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: #764ba2;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Dashboard Styles */
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.activity-icon {
    font-size: 0.8rem;
    font-weight: bold;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    color: white;
}

.activity-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.activity-content p {
    margin: 0 0 0.25rem 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #999;
    font-style: italic;
}

/* Register Form in Admin Panel */
.register-form-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.register-form-section h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.admin-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Automation Status Display */
.automation-status {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border-left: 4px solid #28a745;
}

.automation-status h4 {
    margin: 0 0 1rem 0;
    color: #28a745;
    font-weight: 600;
}

.automation-status p {
    margin: 0.5rem 0;
    color: #2c3e50;
}

.automation-status a {
    color: #667eea;
    text-decoration: none;
    word-break: break-all;
}

.automation-status a:hover {
    text-decoration: underline;
}

/* Improved Form Styling */
.form-label {
    display: block;
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-row {
    margin-bottom: 1.5rem;
}

/* Modern Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
    transform: translateY(-2px);
}

/* Report Received Functionality */
.spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
    color: #667eea;
    font-weight: bold;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.section-highlight {
    background-color: rgba(147, 112, 219, 0.2);
    border-left: 4px solid #9370db;
    animation: highlightFade 3s ease-out;
}

@keyframes highlightFade {
    0% {
        background-color: rgba(255, 235, 59, 0.6);
        transform: scale(1.02);
    }
    100% {
        background-color: rgba(255, 235, 59, 0.1);
        transform: scale(1);
    }
}

/* New Entry Highlight */
.new-entry-highlight {
    background-color: rgba(40, 167, 69, 0.2);
    border-left: 4px solid #28a745;
    animation: newEntryPulse 5s ease-out;
}

@keyframes newEntryPulse {
    0% {
        background-color: rgba(40, 167, 69, 0.4);
        transform: scale(1.02);
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
    }
    50% {
        background-color: rgba(40, 167, 69, 0.3);
    }
    100% {
        background-color: rgba(40, 167, 69, 0.1);
        transform: scale(1);
        box-shadow: none;
    }
}

.status-badge.running {
    background-color: #17a2b8;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Table Search and Controls */
.table-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.search-input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-search-btn {
    padding: 0.75rem 1rem;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.clear-search-btn:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

.table-info {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Sortable Table Headers */
.builds-table th {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: all 0.3s ease;
}

.builds-table th:hover {
    background-color: #e9ecef;
}

.sort-arrow {
    margin-left: 0.5rem;
    color: #ccc;
    font-size: 0.8rem;
    transition: color 0.3s ease;
}

.sort-arrow:hover {
    color: #667eea;
}

/* Search Highlighting */
mark {
    background-color: #fff3cd;
    color: #856404;
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
    font-weight: 600;
}

/* Table Wrapper */
.builds-table-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

/* Responsive Table */
@media (max-width: 768px) {
    .table-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-input {
        margin-bottom: 0.5rem;
    }
}

/* MODERN FORM UI ENHANCEMENTS */

/* Form Container */
.form-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    margin-bottom: 2rem;
}

/* Form Rows and Layout */
.form-row {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .form-row .form-label {
        margin-bottom: 0.25rem;
    }
}

.form-row-inline {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Modern Form Labels */
.form-label {
    display: block;
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    letter-spacing: 0.025em;
    transition: color 0.3s ease;
}

.form-label.required::after {
    content: ' *';
    color: #e74c3c;
}

/* Modern Input Fields */
.form-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95rem;
    font-family: inherit;
    background-color: #fff;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background-color: #fafbff;
}

.form-input:hover:not(:focus) {
    border-color: #d1d9ff;
}

.form-input::placeholder {
    color: #a0a9c0;
    font-style: italic;
}

/* Modern Select Dropdowns */
.form-select {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95rem;
    font-family: inherit;
    background-color: #fff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.25rem;
    padding-right: 2.5rem;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background-color: #fafbff;
}

.form-select:hover:not(:focus) {
    border-color: #d1d9ff;
}

/* Modern Checkboxes */
.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}



.checkbox-label {
    font-size: 0.9rem;
    color: #2c3e50;
    cursor: pointer;
    user-select: none;
    line-height: 1.4;
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
    min-height: 44px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #218838;
    transform: translateY(-2px);
}

.btn-warning {
    background-color: #9370db;
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background-color: #8a2be2;
    transform: translateY(-2px);
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #c82333;
    transform: translateY(-2px);
}

/* Button Sizes */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    min-height: 36px;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    min-height: 52px;
}

/* Form Groups */
.form-group {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid #667eea;
}

.form-group h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Floating Labels Effect */
.floating-label {
    position: relative;
    margin-bottom: 1.5rem;
}

.floating-label input,
.floating-label select {
    padding-top: 1.5rem;
    padding-bottom: 0.5rem;
}

.floating-label label {
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: #a0a9c0;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    pointer-events: none;
    background: white;
    padding: 0 0.25rem;
}

.floating-label input:focus + label,
.floating-label input:not(:placeholder-shown) + label,
.floating-label select:focus + label,
.floating-label select:not([value=""]) + label {
    top: -0.5rem;
    left: 0.75rem;
    font-size: 0.75rem;
    color: #667eea;
    font-weight: 600;
}

/* Form Validation States */
.form-input.is-valid,
.form-select.is-valid {
    border-color: #28a745;
}

.form-input.is-invalid,
.form-select.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
}

/* Responsive Form Design */
@media (max-width: 768px) {
    .form-row-inline {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .form-container {
        padding: 1.5rem;
        margin: 1rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Automation Details Modal */
.automation-details-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.automation-details-modal .modal-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.automation-details-modal h3 {
    margin: 0 0 1.5rem 0;
    color: #2c3e50;
    font-size: 1.3rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.details-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.detail-item {
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

.detail-item strong {
    color: #2c3e50;
    display: inline-block;
    min-width: 120px;
}

.detail-item a {
    color: #667eea;
    text-decoration: none;
    word-break: break-all;
}

.detail-item a:hover {
    text-decoration: underline;
}

/* Final Step Styling */
.final-step-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    margin: 1rem 0;
    border: 1px solid #dee2e6;
}

.completion-message {
    font-size: 1.1rem;
    color: #28a745;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    min-height: 52px;
}

/* Workflow Completion Banner */
.workflow-completion {
    margin-top: 2rem;
}

.completion-banner {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.completion-banner h2 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.completion-banner p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.completion-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Checkbox Group Styling */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 1rem 0;
}

.checkbox-group.horizontal {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.checkbox-label:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

/* Custom Checkbox Styling */
.checkbox-label input[type="checkbox"] {
    width: 20px;
    height: 20px;
    border: 2px solid #667eea;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    position: relative;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.checkbox-label input[type="checkbox"]:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"]:focus {
    outline: 2px solid rgba(102, 126, 234, 0.3);
    outline-offset: 2px;
}

/* Auto Build Update Checkboxes - Smaller Rectangular */
.checkbox-row input[type="checkbox"] {
    width: 14px;
    height: 10px;
    border: 1px solid #667eea;
    border-radius: 2px;
    background-color: white;
    cursor: pointer;
    position: relative;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    margin-right: 0.5rem;
    vertical-align: middle;
}

.checkbox-row input[type="checkbox"]:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.checkbox-row input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 8px;
}

/* Ensure Auto Build Update text is inline */
.combo-row label {
    display: inline-flex;
    align-items: center;
    font-size: 0.9rem;
    color: #495057;
    margin-left: 0.5rem;
}

/* Form Disabled State */
.form-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f8f9fa !important;
}

/* Section Disabled State */
.section-disabled {
    opacity: 0.7;
    pointer-events: none;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    position: relative;
}

.section-disabled::before {
    content: '🔒 Section Locked';
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(108, 117, 125, 0.9);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
}

/* Download Button Styling */
.download-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.download-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.download-btn:active {
    transform: translateY(0);
}

/* Status Badge Styling */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.hf {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
}

.status-badge.release {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
}

.status-badge.completed {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.status-badge.in-progress {
    background: linear-gradient(135deg, #9370db 0%, #8a2be2 100%);
    color: white;
}

.status-badge.pending {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

/* Button Disabled State */
.btn-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #6c757d !important;
}

.btn-disabled:hover {
    background: #6c757d !important;
    transform: none !important;
    box-shadow: none !important;
}



/* Right Panel Styling - Removed duplicate definition to avoid conflicts */

.panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
    display: none;
}

.panel-overlay.overlay-visible {
    opacity: 1;
}

.panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.25rem;
}

.close-panel-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.8rem;
    cursor: pointer;
    padding: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
    z-index: 10;
}

.close-panel-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.close-panel-btn:active {
    transform: scale(0.95);
}

.panel-content {
    padding: 1rem;
}

.panel-section {
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
}

.panel-section:last-child {
    border-bottom: none;
}

.panel-section-title {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    gap: 0.5rem;
}

.detail-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f3f4;
    font-size: 0.9rem;
}

.detail-item:last-child {
    border-bottom: none;
}

/* Timeline Styling */
.timeline {
    position: relative;
}

.timeline-item {
    display: flex;
    margin-bottom: 1rem;
    position: relative;
}

.timeline-marker {
    width: 30px;
    height: 30px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.timeline-content {
    flex: 1;
}

.timeline-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.timeline-time {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.timeline-status {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    display: inline-block;
}

.timeline-status.status-completed {
    background: #d4edda;
    color: #155724;
}

.panel-actions {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Clickable Row Styling */
.clickable-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.clickable-row:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

/* Admin Pre-Checks Panel Styling */
.prechecks-admin-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.precheck-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
}

.precheck-item h4 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.5rem;
}

.precheck-checkboxes {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.precheck-admin-checkbox {
    width: 16px;
    height: 16px;
    border: 2px solid #667eea;
    border-radius: 3px;
    background-color: white;
    cursor: pointer;
    position: relative;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.precheck-admin-checkbox:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.precheck-admin-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

/* Button Loading State */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Sample Report Styling */
.sample-report-container, .manual-sanity-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.sample-report-container h4, .manual-sanity-container h4 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.sample-report {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

.sample-report p {
    margin: 0.5rem 0;
    font-weight: 600;
}

.sample-report ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.sample-report li {
    margin: 0.25rem 0;
}

#manualSanityReport {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: vertical;
    margin-bottom: 1rem;
}

#manualSanityReport:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* New Prechecks Management Styling */
.prechecks-title {
    margin: 0 0 1.5rem 0;
    color: #495057;
    font-size: 1.25rem;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.precheck-container {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.precheck-label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    text-align: left;
}

.precheck-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    resize: vertical;
    margin-bottom: 0.75rem;
    min-height: 80px;
}

.precheck-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.precheck-checkboxes {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.precheck-checkboxes .checkbox-label {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
}

/* Reset Section Styling */
.reset-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid #dc3545;
    text-align: center;
}

.btn-reset {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.btn-reset:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.reset-warning {
    font-size: 0.8rem;
    color: #dc3545;
    margin: 0;
    font-style: italic;
}

/* Workflow Step Indicator */
.workflow-step-indicator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.step-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.step-progress.completed {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    padding: 0.5rem;
    border-radius: 6px;
}

.step-number {
    background: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

.step-progress.completed .step-number {
    background: rgba(255, 255, 255, 0.3);
}

.step-name {
    font-weight: 600;
    font-size: 1.1rem;
}

.step-status {
    margin-left: auto;
    font-size: 0.9rem;
    opacity: 0.9;
}
