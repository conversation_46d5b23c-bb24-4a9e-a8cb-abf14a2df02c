/* Responsive Design */

/* Tablet Styles */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }
    
    .nav-list {
        gap: 1rem;
    }
    
    .form-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .form-label {
        min-width: auto;
        text-align: left;
        font-size: 0.9rem;
    }
    
    .combo-row {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .combo-row input {
        min-width: auto;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .header-content {
        position: relative;
    }
    
    .logo h1 {
        font-size: 1.2rem;
    }
    
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        padding: 1rem;
        border-radius: 0 0 12px 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        z-index: 1000;
    }
    
    .nav-menu.active {
        display: block;
    }
    
    .nav-list {
        flex-direction: column;
        gap: 0.5rem;
        justify-content: flex-start;
    }
    
    .nav-link {
        display: block;
        padding: 0.75rem 1rem;
        border-radius: 6px;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .user-menu {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-end;
    }
    
    .user-info {
        align-items: center;
    }
    
    .main-content {
        padding: 1rem 0;
    }
    
    .content-wrapper {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .form-container {
        max-width: none;
    }
    
    .form-inputs {
        gap: 0.75rem;
    }
    
    .form-inputs input {
        padding: 0.875rem;
        font-size: 1rem;
    }
    
    .btn {
        padding: 0.875rem 1.25rem;
        font-size: 1rem;
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .dropdown select {
        width: 100%;
        padding: 0.875rem;
        font-size: 1rem;
    }
    
    .prechecks-panel {
        padding: 1rem;
    }
    
    .precheck-textarea {
        min-height: 100px;
        padding: 0.875rem;
        font-size: 0.9rem;
    }
    
    .notifications {
        left: 10px;
        right: 10px;
        max-width: none;
    }
    
    .notification {
        padding: 0.875rem;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .header {
        padding: 0.75rem 0;
    }
    
    .logo {
        gap: 10px;
    }
    
    .logo-img {
        width: 32px;
        height: 32px;
    }
    
    .logo h1 {
        font-size: 1rem;
    }
    
    .user-name {
        font-size: 0.8rem;
    }
    
    .user-role {
        font-size: 0.7rem;
    }
    
    .logout-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
    
    .content-wrapper {
        padding: 0.75rem;
        border-radius: 8px;
    }
    
    .page-title {
        font-size: 1.25rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .form-inputs input {
        padding: 1rem;
    }
    
    .btn {
        padding: 1rem;
        font-size: 1rem;
    }
    
    .precheck-textarea {
        min-height: 80px;
        padding: 1rem;
    }
    
    .automation-config {
        padding: 1rem;
        font-size: 0.8rem;
    }
    
    .footer {
        padding: 1rem 0;
    }
    
    .footer-content p {
        font-size: 0.8rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .header {
        padding: 0.5rem 0;
    }
    
    .main-content {
        padding: 0.5rem 0;
    }
    
    .content-wrapper {
        padding: 1rem;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo-img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .mobile-menu-toggle,
    .user-menu,
    .notifications {
        display: none;
    }
    
    .main-content {
        padding: 0;
    }
    
    .content-wrapper {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .btn {
        display: none;
    }
    
    .form-inputs input,
    .precheck-textarea {
        border: 1px solid #ddd;
        background: white;
    }
}

/* Focus and Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .content-wrapper {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }
    
    .form-inputs input,
    .precheck-textarea,
    .dropdown select {
        background-color: #3d3d3d;
        border-color: #555;
        color: #e0e0e0;
    }
    
    .status-area,
    .prechecks-panel {
        background-color: #3d3d3d;
        border-color: #555;
    }
    
    .notification {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }
}
