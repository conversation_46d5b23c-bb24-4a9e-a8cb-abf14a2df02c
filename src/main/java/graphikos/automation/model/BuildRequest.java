package graphikos.automation.model;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;

@Entity
@Table(name = "build_requests")
public class BuildRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Release type is required")
    private String releaseType; // HF, Release

    // GC Configuration
    private String buildsetup;

    // Zohoshow Configuration
    private String zohoshowinput;

    // Client Configuration
    private String shapeframework;
    private String graphikosmedia;
    private String showrenderingframework;
    private String graphikosi18n;
    private String showlistingdialog;
    private String showrightpanel;
    private String showslideshowviews;
    private String showui;
    private String showoffline;

    // Server Configuration
    private String showserver;
    private boolean enableAutobuildUpdate = false;
    private String zohoshow;

    // Conversion Configuration
    private String conversion;
    private boolean conversionAutoBuildUpdate = false;

    // Pictures Configuration
    private String pictures;
    private boolean picturesAutoBuildUpdate = false;

    // Image Conversion Configuration
    private String imageconversion;
    private boolean imageconversionAutoBuildUpdate = false;

    // Status and Metadata
    @Enumerated(EnumType.STRING)
    private BuildStatus status = BuildStatus.SUBMITTED;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by")
    private User createdBy;

    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Automation Configuration
    @Column(columnDefinition = "TEXT")
    private String automationConfig;

    // Report URLs
    private String automationReportUrl;
    private String manualTestcaseSheet;

    // Checkboxes status
    private boolean reportReceived = false;
    private boolean manualTestcaseConfirmed = false;
    private boolean preSanity = false;
    private boolean preAutomation = false;

    // Admin Prechecks
    private boolean buildDiffChecked = false;
    private boolean changedFilesChecked = false;
    private boolean zdcmFilesChecked = false;
    private boolean migrationFilesChecked = false;

    // Precheck content
    @Column(columnDefinition = "TEXT")
    private String buildDiffContent;
    @Column(columnDefinition = "TEXT")
    private String changedFilesContent;
    @Column(columnDefinition = "TEXT")
    private String zdcmFilesContent;
    @Column(columnDefinition = "TEXT")
    private String migrationFilesContent;

    // Final status
    private boolean buildUpdatedToLive = false;
    private boolean finalSanityCompleted = false;
    private String sanityUrl;

    public enum BuildStatus {
        SUBMITTED, IN_PROGRESS, AUTOMATION_RUNNING, PRE_BUILD_UPDATED, 
        LIVE_BUILD_UPDATED, COMPLETED, FAILED
    }

    // Constructors
    public BuildRequest() {}

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getReleaseType() { return releaseType; }
    public void setReleaseType(String releaseType) { this.releaseType = releaseType; }

    public String getBuildsetup() { return buildsetup; }
    public void setBuildsetup(String buildsetup) { this.buildsetup = buildsetup; }

    public String getZohoshowinput() { return zohoshowinput; }
    public void setZohoshowinput(String zohoshowinput) { this.zohoshowinput = zohoshowinput; }

    public String getShapeframework() { return shapeframework; }
    public void setShapeframework(String shapeframework) { this.shapeframework = shapeframework; }

    public String getGraphikosmedia() { return graphikosmedia; }
    public void setGraphikosmedia(String graphikosmedia) { this.graphikosmedia = graphikosmedia; }

    public String getShowrenderingframework() { return showrenderingframework; }
    public void setShowrenderingframework(String showrenderingframework) { this.showrenderingframework = showrenderingframework; }

    public String getGraphikosi18n() { return graphikosi18n; }
    public void setGraphikosi18n(String graphikosi18n) { this.graphikosi18n = graphikosi18n; }

    public String getShowlistingdialog() { return showlistingdialog; }
    public void setShowlistingdialog(String showlistingdialog) { this.showlistingdialog = showlistingdialog; }

    public String getShowrightpanel() { return showrightpanel; }
    public void setShowrightpanel(String showrightpanel) { this.showrightpanel = showrightpanel; }

    public String getShowslideshowviews() { return showslideshowviews; }
    public void setShowslideshowviews(String showslideshowviews) { this.showslideshowviews = showslideshowviews; }

    public String getShowui() { return showui; }
    public void setShowui(String showui) { this.showui = showui; }

    public String getShowoffline() { return showoffline; }
    public void setShowoffline(String showoffline) { this.showoffline = showoffline; }

    public String getShowserver() { return showserver; }
    public void setShowserver(String showserver) { this.showserver = showserver; }

    public boolean isEnableAutobuildUpdate() { return enableAutobuildUpdate; }
    public void setEnableAutobuildUpdate(boolean enableAutobuildUpdate) { this.enableAutobuildUpdate = enableAutobuildUpdate; }

    public String getZohoshow() { return zohoshow; }
    public void setZohoshow(String zohoshow) { this.zohoshow = zohoshow; }

    public String getConversion() { return conversion; }
    public void setConversion(String conversion) { this.conversion = conversion; }

    public boolean isConversionAutoBuildUpdate() { return conversionAutoBuildUpdate; }
    public void setConversionAutoBuildUpdate(boolean conversionAutoBuildUpdate) { this.conversionAutoBuildUpdate = conversionAutoBuildUpdate; }

    public String getPictures() { return pictures; }
    public void setPictures(String pictures) { this.pictures = pictures; }

    public boolean isPicturesAutoBuildUpdate() { return picturesAutoBuildUpdate; }
    public void setPicturesAutoBuildUpdate(boolean picturesAutoBuildUpdate) { this.picturesAutoBuildUpdate = picturesAutoBuildUpdate; }

    public String getImageconversion() { return imageconversion; }
    public void setImageconversion(String imageconversion) { this.imageconversion = imageconversion; }

    public boolean isImageconversionAutoBuildUpdate() { return imageconversionAutoBuildUpdate; }
    public void setImageconversionAutoBuildUpdate(boolean imageconversionAutoBuildUpdate) { this.imageconversionAutoBuildUpdate = imageconversionAutoBuildUpdate; }

    public BuildStatus getStatus() { return status; }
    public void setStatus(BuildStatus status) { this.status = status; }

    public User getCreatedBy() { return createdBy; }
    public void setCreatedBy(User createdBy) { this.createdBy = createdBy; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public String getAutomationConfig() { return automationConfig; }
    public void setAutomationConfig(String automationConfig) { this.automationConfig = automationConfig; }

    public String getAutomationReportUrl() { return automationReportUrl; }
    public void setAutomationReportUrl(String automationReportUrl) { this.automationReportUrl = automationReportUrl; }

    public String getManualTestcaseSheet() { return manualTestcaseSheet; }
    public void setManualTestcaseSheet(String manualTestcaseSheet) { this.manualTestcaseSheet = manualTestcaseSheet; }

    public boolean isReportReceived() { return reportReceived; }
    public void setReportReceived(boolean reportReceived) { this.reportReceived = reportReceived; }

    public boolean isManualTestcaseConfirmed() { return manualTestcaseConfirmed; }
    public void setManualTestcaseConfirmed(boolean manualTestcaseConfirmed) { this.manualTestcaseConfirmed = manualTestcaseConfirmed; }

    public boolean isPreSanity() { return preSanity; }
    public void setPreSanity(boolean preSanity) { this.preSanity = preSanity; }

    public boolean isPreAutomation() { return preAutomation; }
    public void setPreAutomation(boolean preAutomation) { this.preAutomation = preAutomation; }

    public boolean isBuildDiffChecked() { return buildDiffChecked; }
    public void setBuildDiffChecked(boolean buildDiffChecked) { this.buildDiffChecked = buildDiffChecked; }

    public boolean isChangedFilesChecked() { return changedFilesChecked; }
    public void setChangedFilesChecked(boolean changedFilesChecked) { this.changedFilesChecked = changedFilesChecked; }

    public boolean isZdcmFilesChecked() { return zdcmFilesChecked; }
    public void setZdcmFilesChecked(boolean zdcmFilesChecked) { this.zdcmFilesChecked = zdcmFilesChecked; }

    public boolean isMigrationFilesChecked() { return migrationFilesChecked; }
    public void setMigrationFilesChecked(boolean migrationFilesChecked) { this.migrationFilesChecked = migrationFilesChecked; }

    public String getBuildDiffContent() { return buildDiffContent; }
    public void setBuildDiffContent(String buildDiffContent) { this.buildDiffContent = buildDiffContent; }

    public String getChangedFilesContent() { return changedFilesContent; }
    public void setChangedFilesContent(String changedFilesContent) { this.changedFilesContent = changedFilesContent; }

    public String getZdcmFilesContent() { return zdcmFilesContent; }
    public void setZdcmFilesContent(String zdcmFilesContent) { this.zdcmFilesContent = zdcmFilesContent; }

    public String getMigrationFilesContent() { return migrationFilesContent; }
    public void setMigrationFilesContent(String migrationFilesContent) { this.migrationFilesContent = migrationFilesContent; }

    public boolean isBuildUpdatedToLive() { return buildUpdatedToLive; }
    public void setBuildUpdatedToLive(boolean buildUpdatedToLive) { this.buildUpdatedToLive = buildUpdatedToLive; }

    public boolean isFinalSanityCompleted() { return finalSanityCompleted; }
    public void setFinalSanityCompleted(boolean finalSanityCompleted) { this.finalSanityCompleted = finalSanityCompleted; }

    public String getSanityUrl() { return sanityUrl; }
    public void setSanityUrl(String sanityUrl) { this.sanityUrl = sanityUrl; }
}
