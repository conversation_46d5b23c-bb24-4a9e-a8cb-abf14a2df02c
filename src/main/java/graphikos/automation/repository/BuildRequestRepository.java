package graphikos.automation.repository;

import graphikos.automation.model.BuildRequest;
import graphikos.automation.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface BuildRequestRepository extends JpaRepository<BuildRequest, Long> {
    List<BuildRequest> findByCreatedByOrderByCreatedAtDesc(User user);
    List<BuildRequest> findAllByOrderByCreatedAtDesc();
    List<BuildRequest> findByStatusOrderByCreatedAtDesc(BuildRequest.BuildStatus status);
    
    @Query("SELECT br FROM BuildRequest br WHERE br.createdAt >= :startDate ORDER BY br.createdAt DESC")
    List<BuildRequest> findRecentBuilds(LocalDateTime startDate);
    
    long countByStatus(BuildRequest.BuildStatus status);
    long countByCreatedBy(User user);
}
