package graphikos.automation.controller;

import graphikos.automation.model.User;
import graphikos.automation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Autowired
    private UserService userService;

    @GetMapping("/users")
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = userService.findAllUsers();
        return ResponseEntity.ok(users);
    }

    @PostMapping("/users/update-roles")
    public ResponseEntity<Map<String, Object>> updateUserRoles(@RequestBody List<UserRoleUpdate> updates) {
        Map<String, Object> response = new HashMap<>();

        try {
            for (UserRoleUpdate update : updates) {
                User user = userService.findById(Long.parseLong(update.getUserId()))
                        .orElseThrow(() -> new RuntimeException("User not found"));

                User.Role newRole = User.Role.valueOf(update.getRole());
                user.setRole(newRole);
                userService.updateUser(user);
            }

            response.put("success", true);
            response.put("message", "User roles updated successfully");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    @PostMapping("/users/create")
    public ResponseEntity<Map<String, Object>> createUser(@RequestBody UserCreateRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            // Check if username already exists
            if (userService.existsByUsername(request.getUsername())) {
                response.put("success", false);
                response.put("message", "Username already exists");
                return ResponseEntity.ok(response);
            }

            // Check if email already exists
            if (userService.existsByEmail(request.getEmail())) {
                response.put("success", false);
                response.put("message", "Email already exists");
                return ResponseEntity.ok(response);
            }

            User newUser = new User();
            newUser.setUsername(request.getUsername());
            newUser.setPassword(request.getPassword());
            newUser.setEmail(request.getEmail());
            newUser.setFullName(request.getFullName());
            newUser.setRole(User.Role.valueOf(request.getRole()));
            newUser.setEnabled(true);

            userService.createUser(newUser);

            response.put("success", true);
            response.put("message", "User created successfully");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    public static class UserRoleUpdate {
        private String userId;
        private String role;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }
    }

    public static class UserCreateRequest {
        private String username;
        private String password;
        private String email;
        private String fullName;
        private String role;

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getFullName() {
            return fullName;
        }

        public void setFullName(String fullName) {
            this.fullName = fullName;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }
    }
}
