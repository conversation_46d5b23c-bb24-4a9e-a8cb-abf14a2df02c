package graphikos.automation.controller;

import graphikos.automation.model.User;
import graphikos.automation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Controller
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN') or hasRole('ULTIMATE')")
public class AdminPageController {

    @Autowired
    private UserService userService;

    @GetMapping
    public String adminPage(Model model) {
        // Get current user
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String username = auth.getName();
        
        User currentUser = userService.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Load all users for management
        List<User> users = userService.findAllUsers();
        
        model.addAttribute("currentUser", currentUser);
        model.addAttribute("users", users);

        return "admin/gate_admin";
    }
}
