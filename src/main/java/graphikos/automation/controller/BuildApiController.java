package graphikos.automation.controller;

import graphikos.automation.model.BuildRequest;
import graphikos.automation.model.User;
import graphikos.automation.service.BuildService;
import graphikos.automation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/builds")
public class BuildApiController {

    @Autowired
    private BuildService buildService;

    @Autowired
    private UserService userService;

    @PostMapping("/submit")
    public ResponseEntity<Map<String, Object>> submitBuild(@RequestBody BuildRequest buildRequest) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String username = auth.getName();
            
            User currentUser = userService.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("User not found"));

            BuildRequest savedBuild = buildService.createBuildRequest(buildRequest, currentUser);
            
            response.put("success", true);
            response.put("message", "Build request submitted successfully");
            response.put("buildId", savedBuild.getId());
            response.put("status", savedBuild.getStatus().name());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error submitting build: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/{id}/start-automation")
    public ResponseEntity<Map<String, Object>> startAutomation(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Simulate automation start - in real implementation, this would trigger actual automation
            String reportUrl = "http://localhost:8080/gqa/reports/automation/" + id;
            BuildRequest updatedBuild = buildService.startAutomation(id, reportUrl);
            
            response.put("success", true);
            response.put("message", "Automation started successfully");
            response.put("reportUrl", reportUrl);
            response.put("status", updatedBuild.getStatus().name());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error starting automation: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/{id}/update-status")
    public ResponseEntity<Map<String, Object>> updateBuildStatus(@PathVariable Long id, 
                                                                @RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String statusStr = request.get("status");
            BuildRequest.BuildStatus status = BuildRequest.BuildStatus.valueOf(statusStr);
            
            BuildRequest updatedBuild = buildService.updateBuildStatus(id, status);
            
            response.put("success", true);
            response.put("message", "Build status updated successfully");
            response.put("status", updatedBuild.getStatus().name());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error updating build status: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/{id}/update-checkboxes")
    public ResponseEntity<Map<String, Object>> updateCheckboxes(@PathVariable Long id, 
                                                               @RequestBody Map<String, Boolean> checkboxes) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Optional<BuildRequest> buildOpt = buildService.findById(id);
            if (buildOpt.isPresent()) {
                BuildRequest build = buildOpt.get();
                
                // Update checkbox states
                if (checkboxes.containsKey("reportReceived")) {
                    build.setReportReceived(checkboxes.get("reportReceived"));
                }
                if (checkboxes.containsKey("manualTestcaseConfirmed")) {
                    build.setManualTestcaseConfirmed(checkboxes.get("manualTestcaseConfirmed"));
                }
                if (checkboxes.containsKey("preSanity")) {
                    build.setPreSanity(checkboxes.get("preSanity"));
                }
                if (checkboxes.containsKey("preAutomation")) {
                    build.setPreAutomation(checkboxes.get("preAutomation"));
                }
                if (checkboxes.containsKey("finalSanityCompleted")) {
                    build.setFinalSanityCompleted(checkboxes.get("finalSanityCompleted"));
                }
                
                BuildRequest updatedBuild = buildService.updateBuildRequest(build);
                
                response.put("success", true);
                response.put("message", "Checkboxes updated successfully");
                response.put("status", updatedBuild.getStatus().name());
                
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "Build not found");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error updating checkboxes: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/{id}/update-prechecks")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> updatePrechecks(@PathVariable Long id, 
                                                              @RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Optional<BuildRequest> buildOpt = buildService.findById(id);
            if (buildOpt.isPresent()) {
                BuildRequest build = buildOpt.get();
                
                // Update precheck content
                if (request.containsKey("buildDiffContent")) {
                    build.setBuildDiffContent((String) request.get("buildDiffContent"));
                }
                if (request.containsKey("changedFilesContent")) {
                    build.setChangedFilesContent((String) request.get("changedFilesContent"));
                }
                if (request.containsKey("zdcmFilesContent")) {
                    build.setZdcmFilesContent((String) request.get("zdcmFilesContent"));
                }
                if (request.containsKey("migrationFilesContent")) {
                    build.setMigrationFilesContent((String) request.get("migrationFilesContent"));
                }
                
                // Update precheck checkboxes (admin only)
                if (request.containsKey("buildDiffChecked")) {
                    build.setBuildDiffChecked((Boolean) request.get("buildDiffChecked"));
                }
                if (request.containsKey("changedFilesChecked")) {
                    build.setChangedFilesChecked((Boolean) request.get("changedFilesChecked"));
                }
                if (request.containsKey("zdcmFilesChecked")) {
                    build.setZdcmFilesChecked((Boolean) request.get("zdcmFilesChecked"));
                }
                if (request.containsKey("migrationFilesChecked")) {
                    build.setMigrationFilesChecked((Boolean) request.get("migrationFilesChecked"));
                }
                
                BuildRequest updatedBuild = buildService.updateBuildRequest(build);
                
                response.put("success", true);
                response.put("message", "Prechecks updated successfully");
                
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "Build not found");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error updating prechecks: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<BuildRequest> getBuild(@PathVariable Long id) {
        Optional<BuildRequest> build = buildService.findById(id);
        return build.map(ResponseEntity::ok)
                   .orElse(ResponseEntity.notFound().build());
    }



    @PostMapping("/{buildId}/confirm-report")
    public ResponseEntity<Map<String, Object>> confirmReport(@PathVariable Long buildId) {
        Map<String, Object> response = new HashMap<>();

        try {
            Optional<BuildRequest> buildOpt = buildService.findById(buildId);
            if (buildOpt.isPresent()) {
                BuildRequest build = buildOpt.get();

                // Update report received status
                build.setReportReceived(true);
                buildService.updateBuildRequest(build);

                response.put("success", true);
                response.put("message", "Report confirmed successfully");
                response.put("nextStep", "manual-testcase");

                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "Build not found");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error confirming report: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{id}/json")
    public ResponseEntity<Map<String, Object>> getBuildJson(@PathVariable Long id) {
        Optional<BuildRequest> buildOpt = buildService.findById(id);
        if (buildOpt.isPresent()) {
            BuildRequest build = buildOpt.get();
            Map<String, Object> buildData = new HashMap<>();

            // Convert build to JSON-friendly format
            buildData.put("id", build.getId());
            buildData.put("releaseType", build.getReleaseType());
            buildData.put("status", build.getStatus().name());
            buildData.put("createdAt", build.getCreatedAt().toString());
            buildData.put("createdBy", build.getCreatedBy().getUsername());

            // Add all form fields
            buildData.put("buildsetup", build.getBuildsetup());
            buildData.put("zohoshowinput", build.getZohoshowinput());
            buildData.put("shapeframework", build.getShapeframework());
            buildData.put("graphikosmedia", build.getGraphikosmedia());
            buildData.put("showrenderingframework", build.getShowrenderingframework());
            buildData.put("graphikosi18n", build.getGraphikosi18n());
            buildData.put("showlistingdialog", build.getShowlistingdialog());
            buildData.put("showrightpanel", build.getShowrightpanel());
            buildData.put("showslideshowviews", build.getShowslideshowviews());
            buildData.put("showui", build.getShowui());
            buildData.put("showoffline", build.getShowoffline());
            buildData.put("showserver", build.getShowserver());
            buildData.put("conversion", build.getConversion());
            buildData.put("pictures", build.getPictures());
            buildData.put("imageconversion", build.getImageconversion());

            // Add status flags
            buildData.put("reportReceived", build.isReportReceived());
            buildData.put("manualTestcaseConfirmed", build.isManualTestcaseConfirmed());
            buildData.put("preSanity", build.isPreSanity());
            buildData.put("preAutomation", build.isPreAutomation());
            buildData.put("buildUpdatedToLive", build.isBuildUpdatedToLive());
            buildData.put("finalSanityCompleted", build.isFinalSanityCompleted());

            return ResponseEntity.ok(buildData);
        }
        return ResponseEntity.notFound().build();
    }
}
