package graphikos.automation.controller;

import graphikos.automation.model.BuildRequest;
import graphikos.automation.model.User;
import graphikos.automation.service.BuildService;
import graphikos.automation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

@Controller
public class MainController {

    @Autowired
    private BuildService buildService;

    @Autowired
    private UserService userService;

    @GetMapping("/")
    public String home() {
        return "redirect:/main";
    }

    @GetMapping("/main")
    public String main(Model model) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String username = auth.getName();

        User currentUser = userService.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));

        model.addAttribute("currentUser", currentUser);

        return "index";
    }

    @GetMapping("/dashboard")
    public String dashboard() {
        return "redirect:/main";
    }

    @GetMapping("/nic-checks")
    public String nicChecks() {
        return "redirect:/main";
    }

    @GetMapping("/update-build")
    public String updateBuild() {
        return "redirect:/main";
    }

    @GetMapping("/check-status")
    public String checkStatus() {
        return "redirect:/main";
    }

    @GetMapping("/logout")
    public String logout() {
        return "redirect:/login?logout=true";
    }
}
