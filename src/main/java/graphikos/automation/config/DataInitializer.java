package graphikos.automation.config;

import graphikos.automation.model.User;
import graphikos.automation.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserService userService;

    @Override
    public void run(String... args) throws Exception {
        // Create default admin user if not exists
        if (!userService.existsByUsername("admin")) {
            User admin = new User();
            admin.setUsername("admin");
            admin.setPassword("admin123");
            admin.setEmail("<EMAIL>");
            admin.setFullName("System Administrator");
            admin.setRole(User.Role.ADMIN);
            admin.setEnabled(true);
            
            userService.createUser(admin);
            System.out.println("Created default admin user: admin/admin123");
        }

        // Create default editor user if not exists
        if (!userService.existsByUsername("editor")) {
            User editor = new User();
            editor.setUsername("editor");
            editor.setPassword("editor123");
            editor.setEmail("<EMAIL>");
            editor.setFullName("QA Editor");
            editor.setRole(User.Role.EDITOR);
            editor.setEnabled(true);
            
            userService.createUser(editor);
            System.out.println("Created default editor user: editor/editor123");
        }



        // Create QA user
        if (!userService.existsByUsername("qauser")) {
            User qa = new User();
            qa.setUsername("qauser");
            qa.setPassword("qa123");
            qa.setEmail("<EMAIL>");
            qa.setFullName("QA User");
            qa.setRole(User.Role.QA);
            qa.setEnabled(true);

            userService.createUser(qa);
            System.out.println("Created QA user: qauser/qa123");
        }

        // Create Ultimate user
        if (!userService.existsByUsername("ultimate")) {
            User ultimate = new User();
            ultimate.setUsername("ultimate");
            ultimate.setPassword("ultimate123");
            ultimate.setEmail("<EMAIL>");
            ultimate.setFullName("Ultimate User");
            ultimate.setRole(User.Role.ULTIMATE);
            ultimate.setEnabled(true);

            userService.createUser(ultimate);
            System.out.println("Created Ultimate user: ultimate/ultimate123");
        }

        System.out.println("Data initialization completed!");
        System.out.println("Available users:");
        System.out.println("- admin/admin123 (ADMIN)");
        System.out.println("- editor/editor123 (EDITOR)");
        System.out.println("- qauser/qa123 (QA)");
        System.out.println("- ultimate/ultimate123 (ULTIMATE)");
    }
}
