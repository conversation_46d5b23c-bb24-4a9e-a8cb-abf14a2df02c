{"name": "graphikos-automation-tests", "version": "1.0.0", "description": "Playwright tests for Graphikos Automation application", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:nic-check": "playwright test test-nic-check.spec.ts", "test:update-build": "playwright test test-update-build.spec.ts", "report": "playwright show-report", "allure:generate": "allure generate allure-results --clean -o allure-report", "allure:serve": "allure serve allure-results", "report:standalone": "node scripts/generate-standalone-report.js", "test:report": "npm run test && npm run report:standalone", "install:browsers": "playwright install"}, "keywords": ["playwright", "testing", "automation", "qa", "graphikos"], "author": "Graphikos QA Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0", "allure-commandline": "^2.34.1", "allure-playwright": "^2.15.1", "typescript": "^5.2.2"}}