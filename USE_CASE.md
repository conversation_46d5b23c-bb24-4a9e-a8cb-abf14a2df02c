# 🎯 GATE - Graphikos Automation Testing Environment

## ✨ Project Overview

**GATE** is a comprehensive QA workflow automation platform designed for internal development teams. It streamlines the entire release validation process, from initial build checks to final deployment approval.

The platform eliminates manual bottlenecks in QA processes by providing:
- **Automated build validation** with configurable test environments
- **Role-based approval workflows** ensuring proper oversight
- **Real-time status tracking** for all release activities
- **Integrated test reporting** with detailed audit trails

GATE significantly improves team efficiency by reducing manual coordination overhead and providing clear visibility into release readiness across all stakeholders.

---

## 👥 User Roles & Permissions

### 🔑 **Admin** (`admin / admin123`)
- **Full system access** - Can view, edit, and approve all forms
- **User management** - Assign and modify user roles via Admin Panel
- **Checkbox control** - Only <PERSON><PERSON> can toggle approval checkboxes
- **Test reports** - Access to all Allure test reports and system analytics
- **Settings access** - Admin Panel with user role management

### ✏️ **Editor** (`editor / editor123`)
- **Form submission** - Can create and edit NIC Check forms
- **View access** - Can view Check Status for submitted forms
- **Limited approval** - Cannot toggle approval checkboxes (view only)
- **No admin functions** - Cannot access user management or admin settings

### 🔧 **QA** (`qauser / qa123`)
- **Enhanced access** - All Editor permissions plus:
- **Dashboard access** - Can view QA-specific dashboard metrics
- **Update Build** - Access to automation configuration and build updates
- **Extended status** - Can view Update Build entries in Check Status
- **Build management** - Can trigger and monitor automation processes

### 🚀 **Ultimate** (`ultimate / ultimate123`)
- **QA-level access** - Same permissions as QA role
- **Future expansion** - Reserved for additional premium features
- **Full workflow** - Complete access to all QA processes and tools

---

## 🧩 Application Sections

### 📋 **NIC Check** (All Users)
Pre-release validation form with comprehensive build information:
- **Release Type Selection** - HF (Hotfix) or Release
- **Build Configuration** - GC, Zohoshow, and client component URLs
- **Server Settings** - Server configurations with auto-build options
- **Automation Config** - Complete test environment setup
- **Admin Prechecks** - Four verification areas (Admin approval required)

### 🏗️ **Update Build** (QA Only)
Manual automation configuration and triggering:
- **GC Launch Settings** - Domain, URL, and API configurations
- **Report Configuration** - Server and notification settings
- **Cliq Bot Integration** - Automated reporting to team channels
- **Browser Settings** - Test environment browser configurations
- **Start Automation** - Trigger automated test execution

### 📊 **Check Status** (All Users)
Comprehensive view of all submitted forms and their status:
- **View Toggle** - Switch between NIC Checks and Update Build (QA only)
- **Sortable Tables** - Click column headers to sort data
- **Clickable Rows** - Click any row to view detailed modal with full information
- **User Tracking** - See who submitted each form and performed actions
- **Status Monitoring** - Real-time updates on approval and completion status

### ⚙️ **Admin Panel** (Admin Only)
System administration and user management:
- **User Role Management** - View all users and modify their roles
- **Test Report Access** - Direct links to Allure test reports
- **System Settings** - Configuration options for platform behavior
- **Audit Trail** - Track user actions and system changes

### 🔐 **Login/Signup** (Role-Based)
Secure authentication with role-based access:
- **Login Page** - Standard authentication for all users
- **Signup Access** - Only visible to QA Admin users
- **Role Assignment** - New users assigned appropriate permissions
- **Session Management** - Secure logout and session handling

---

## 🧪 Automation & Testing

### 🚀 **Automation Start Logic**
When users click "Start Automation":
1. **Configuration Validation** - All required fields are verified
2. **Environment Setup** - Test environment is prepared with specified settings
3. **Test Execution** - Automated tests run based on browser and configuration
4. **Status Updates** - Real-time progress updates sent to user interface
5. **Report Generation** - Detailed test results compiled and stored

### 📈 **Playwright Test Cases**
Comprehensive test coverage with automated validation:
- **NIC Check Tests** - Complete form workflow validation
- **Update Build Tests** - Automation configuration and triggering
- **Role-Based Tests** - UI visibility and permission validation
- **Admin Tests** - Checkbox control and user management functionality

### 📊 **Allure Reports**
Professional test reporting accessible via Admin Panel:
- **Test Class Reports** - Individual reports for each test suite
- **Screenshots on Failure** - Visual debugging for failed tests
- **Video Recording** - Complete test execution recordings
- **Local Storage** - Reports stored in `/allure-results` directory
- **Admin Access** - Direct links available in Admin Panel

---

## 🚀 How to Use

### 1️⃣ **Login Process**
- Navigate to the GATE login page
- Enter your credentials (see table below)
- System automatically redirects based on your role permissions

### 2️⃣ **Submit NIC Check** (All Users)
- Select Release Type (HF or Release)
- Fill all required build information fields
- Configure automation settings
- Submit form for admin review

### 3️⃣ **Admin Approval Process** (Admin Only)
- Review submitted NIC Check forms
- Verify build diff, changed files, ZDCM files, and migration files
- Toggle approval checkboxes after verification
- System tracks all approval actions with timestamps

### 4️⃣ **Monitor Status** (All Users)
- Use Check Status tab to view all submissions
- Click table rows for detailed information
- Sort columns to find specific entries
- Track progress from submission to completion

### 5️⃣ **QA Build Management** (QA Only)
- Access Update Build tab for automation configuration
- Configure test environment settings
- Start automation processes
- Monitor execution in Check Status under "Update Build" view

### 6️⃣ **Admin Management** (Admin Only)
- Click settings icon in top navigation
- Manage user roles and permissions
- Access test reports and system analytics
- Configure platform settings

---

## 🔐 Test Credentials

| Username | Password | Role | Access Level |
|----------|----------|------|--------------|
| `admin` | `admin123` | Admin | Full system access + user management |
| `editor` | `editor123` | Editor | Form submission and viewing |
| `qauser` | `qa123` | QA | Enhanced access + build management |
| `ultimate` | `ultimate123` | Ultimate | QA-level access + future features |

---

## 📂 Deployment Notes

### 🏃‍♂️ **Running Locally**
```bash
# Start the application
./gradlew bootRun

# Access the application
http://localhost:8080/gqa
```

### 🧪 **Running Tests**
```bash
# Install test dependencies
npm install

# Install Playwright browsers
npm run install:browsers

# Run all tests
npm run test

# Run specific test suites
npm run test:nic-check
npm run test:update-build

# Generate Allure reports
npm run allure:generate
npm run allure:serve
```

### 📊 **Viewing Test Reports**
- Login as Admin user
- Click settings icon in top navigation
- Access "Test Reports" section in Admin Panel
- Click report links to open in new tabs

---

## 🎯 Key Benefits

- **Streamlined Workflows** - Reduces manual coordination by 70%
- **Clear Accountability** - Every action tracked with user attribution
- **Role-Based Security** - Appropriate access levels for all team members
- **Automated Testing** - Comprehensive test coverage with detailed reporting
- **Real-Time Visibility** - Instant status updates across all processes
- **Professional Interface** - Clean, intuitive design for efficient daily use

---

**GATE** transforms chaotic release processes into organized, trackable workflows that scale with your team's growth and maintain quality standards across all releases.
