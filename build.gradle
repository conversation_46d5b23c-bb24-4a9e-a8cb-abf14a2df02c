plugins {
    id 'java'
    id 'org.springframework.boot' version '2.7.18'
    id 'io.spring.dependency-management' version '1.0.15.RELEASE'
    id 'io.qameta.allure' version '2.11.2'
}

group = 'graphikos.automation'
version = '1.0-SNAPSHOT'
java.sourceCompatibility = JavaVersion.VERSION_11

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    // Database
    runtimeOnly 'com.h2database:h2'

    // JSON processing
    implementation 'com.fasterxml.jackson.core:jackson-databind'

    // Development tools
    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'

    // JUnit 5
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'

    // Playwright
    testImplementation 'com.microsoft.playwright:playwright:1.40.0'

    // Allure
    testImplementation 'io.qameta.allure:allure-junit5:2.24.0'
    testImplementation 'io.qameta.allure:allure-java-commons:2.24.0'

    // Logging
    testImplementation 'org.slf4j:slf4j-simple:2.0.9'
}

allure {
    adapter {
        aspectjWeaver.set(true)
        frameworks {
            junit5 {
                adapterVersion.set('2.24.0')
            }
        }
    }
}

allure {
    version = '2.24.0'
    aspectjweaver = true
    autoconfigure = true
}

test {
    useJUnitPlatform()
    systemProperty 'junit.jupiter.extensions.autodetection.enabled', 'true'

    // Allure results directory
    systemProperty 'allure.results.directory', "${buildDir}/allure-results"

    // Test execution settings
    maxParallelForks = 1

    testLogging {
        events "passed", "skipped", "failed"
        exceptionFormat "full"
    }
}

// Task to install Playwright browsers
task installPlaywrightBrowsers(type: Exec) {
    commandLine 'npx', 'playwright', 'install'
    doFirst {
        println "Installing Playwright browsers..."
    }
}