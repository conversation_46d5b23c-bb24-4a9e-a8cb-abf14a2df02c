{"uid": "98d3104e051c652961429bf95fa0b5d6", "name": "suites", "children": [{"name": "chromium", "children": [{"name": "NIC Check Complete Workflow", "children": [{"name": "Complete NIC Check form workflow", "uid": "1e6b822c23320caa", "parentUid": "f2b000a8efecdc805532d22741c83b6f", "status": "failed", "time": {"start": 1753351178951, "stop": 1753351193578, "duration": 14627}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}, {"name": "Verify automation configuration form fields", "uid": "538bbacabb46d97d", "parentUid": "f2b000a8efecdc805532d22741c83b6f", "status": "passed", "time": {"start": 1753351178959, "stop": 1753351184543, "duration": 5584}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["chromium"], "tags": []}], "uid": "f2b000a8efecdc805532d22741c83b6f"}, {"name": "Update Build Workflow", "children": [{"name": "Verify Update Build automation form fields and defaults", "uid": "62bebbae51e9870", "parentUid": "c6ba8a5db548bf8962a1c1519fbe165a", "status": "failed", "time": {"start": 1753351178955, "stop": 1753351192294, "duration": 13339}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}, {"name": "Complete Update Build automation workflow", "uid": "4e475d67d407790", "parentUid": "c6ba8a5db548bf8962a1c1519fbe165a", "status": "failed", "time": {"start": 1753351178957, "stop": 1753351192324, "duration": 13367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}, {"name": "Test browser dropdown functionality", "uid": "3cd5ff883aa4f632", "parentUid": "c6ba8a5db548bf8962a1c1519fbe165a", "status": "failed", "time": {"start": 1753351184550, "stop": 1753351195917, "duration": 11367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}], "uid": "c6ba8a5db548bf8962a1c1519fbe165a"}], "uid": "36c900977e1d8b81a2c6893cc82bfc01"}]}