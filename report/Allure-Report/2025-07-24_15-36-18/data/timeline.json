{"uid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "name": "timeline", "children": [{"name": "prakash-3480", "children": [{"name": "prakash-3480-14327-playwright-worker-2", "children": [{"name": "Complete Update Build automation workflow", "uid": "681b50d4051a63fb", "parentUid": "2bdc94a66b045be7aeb2a6de8574ad97", "status": "failed", "time": {"start": 1753340430046, "stop": 1753340447195, "duration": 17149}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}, {"name": "Test browser dropdown functionality", "uid": "2c5e1105b09ad6dc", "parentUid": "2bdc94a66b045be7aeb2a6de8574ad97", "status": "failed", "time": {"start": 1753340447523, "stop": 1753340459230, "duration": 11707}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}], "uid": "2bdc94a66b045be7aeb2a6de8574ad97"}, {"name": "prakash-3480-17059-playwright-worker-2", "children": [{"name": "Complete Update Build automation workflow", "uid": "4e475d67d407790", "parentUid": "e3bf84ea2818cf8d77bd1450b4e4343f", "status": "failed", "time": {"start": 1753351178957, "stop": 1753351192324, "duration": 13367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}], "uid": "e3bf84ea2818cf8d77bd1450b4e4343f"}, {"name": "prakash-3480-14327-playwright-worker-1", "children": [{"name": "Verify automation configuration form fields", "uid": "7b3dfe282e6de06c", "parentUid": "6927e5c7422f21f61328f596598fa3b9", "status": "failed", "time": {"start": 1753340430051, "stop": 1753340449348, "duration": 19297}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}], "uid": "6927e5c7422f21f61328f596598fa3b9"}, {"name": "prakash-3480-17059-playwright-worker-3", "children": [{"name": "Verify Update Build automation form fields and defaults", "uid": "62bebbae51e9870", "parentUid": "be457063642a13ea20712d76751da249", "status": "failed", "time": {"start": 1753351178955, "stop": 1753351192294, "duration": 13339}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}], "uid": "be457063642a13ea20712d76751da249"}, {"name": "prakash-3480-14327-playwright-worker-3", "children": [{"name": "Verify Update Build automation form fields and defaults", "uid": "9ec5b83e9cfff87c", "parentUid": "cdd70abfa9cccfbb90cb153449b63860", "status": "failed", "time": {"start": 1753340430056, "stop": 1753340447305, "duration": 17249}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}], "uid": "cdd70abfa9cccfbb90cb153449b63860"}, {"name": "prakash-3480-17059-playwright-worker-1", "children": [{"name": "Verify automation configuration form fields", "uid": "538bbacabb46d97d", "parentUid": "6897fb4aaddff5de606bcc5098f2aed2", "status": "passed", "time": {"start": 1753351178959, "stop": 1753351184543, "duration": 5584}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["chromium"], "tags": []}, {"name": "Test browser dropdown functionality", "uid": "3cd5ff883aa4f632", "parentUid": "6897fb4aaddff5de606bcc5098f2aed2", "status": "failed", "time": {"start": 1753351184550, "stop": 1753351195917, "duration": 11367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}], "uid": "6897fb4aaddff5de606bcc5098f2aed2"}, {"name": "prakash-3480-14327-playwright-worker-0", "children": [{"name": "Complete NIC Check form workflow", "uid": "5e5023a1ca0d4da7", "parentUid": "78e3b3ff21dc355530b13f495957d95a", "status": "failed", "time": {"start": 1753340430052, "stop": 1753340449344, "duration": 19292}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}], "uid": "78e3b3ff21dc355530b13f495957d95a"}, {"name": "prakash-3480-17059-playwright-worker-0", "children": [{"name": "Complete NIC Check form workflow", "uid": "1e6b822c23320caa", "parentUid": "f2650183d3f172248596b9930dd3b46d", "status": "failed", "time": {"start": 1753351178951, "stop": 1753351193578, "duration": 14627}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}], "uid": "f2650183d3f172248596b9930dd3b46d"}], "uid": "18cda84efc7cff7885bb56505ce3ab8c"}]}