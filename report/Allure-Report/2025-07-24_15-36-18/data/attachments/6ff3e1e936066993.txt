# Page snapshot

```yaml
- banner:
  - img "Graphikos Automation"
  - navigation:
    - list:
      - listitem:
        - link "NIC Checks":
          - /url: "#"
      - listitem:
        - link "Check Status":
          - /url: "#"
  - text: System Administrator ADMIN
  - button "Admin Settings":
    - img
  - link "Logout":
    - /url: /gqa/logout
- main:
  - text: 2 Build Form Configuration In Progress...
  - heading "NIC Checks" [level=1]
  - paragraph: Build Process Management
  - text: Release Type
  - combobox "Release Type":
    - option "Select Release Type"
    - option "HF" [selected]
    - option "Release"
  - text: GC
  - textbox "Branch or Build url": https://github.com/test/gc-build-hf-123
  - text: Zohoshow
  - textbox "Branch or Build url": https://github.com/test/zohoshow-build-hf-123
  - text: Client
  - textbox "shapeframework": shape-framework-v2.1.0
  - textbox "graphikosmedia": graphikos-media-v3.2.1
  - textbox "showrenderingframework": show-rendering-v4.0.0
  - textbox "graphikosi18n": graphikos-i18n-v2.5.0
  - textbox "showlistingdialog": show-listing-dialog-v3.1.0
  - textbox "showrightpanel": show-right-panel-v2.8.0
  - textbox "showslideshowviews": show-slideshow-views-v3.5.0
  - textbox "showui": show-ui-v5.0.0
  - textbox "showoffline": show-offline-v2.2.0
  - text: Server
  - textbox: show-server-v4.1.0
  - checkbox "Auto Build Update" [checked]: ✓
  - text: Auto Build Update Conversion
  - textbox: show-conversion-v3.0.0
  - checkbox "Auto Build Update"
  - text: Auto Build Update Pictures
  - textbox: show-pictures-v2.5.0
  - checkbox "Auto Build Update"
  - text: Auto Build Update Image Conversion
  - textbox: image-conversion-v2.0.0
  - checkbox "Auto Build Update"
  - text: Auto Build Update
  - button "Submit Build"
  - heading "Prechecks" [level=3]
  - text: Show Build Diff
  - textbox "Enter build diff information..."
  - checkbox "Verified"
  - text: Verified Show Changed Files
  - textbox "Enter changed files information..."
  - checkbox "Verified"
  - text: Verified Show ZDCM Files
  - textbox "Enter ZDCM files information..."
  - checkbox "Verified"
  - text: Verified Show Migration Files
  - textbox "Enter migration files information..."
  - checkbox "Verified"
  - text: Verified
- contentinfo:
  - paragraph: © 2024 Graphikos Automation - Internal QA Team Tool
  - paragraph: Version 1.0.0
```