# Page snapshot

```yaml
- banner:
  - img "Graphikos Automation"
  - navigation:
    - list:
      - listitem:
        - link "NIC Checks":
          - /url: "#"
      - listitem:
        - link "Check Status":
          - /url: "#"
  - text: QA Editor EDITOR
  - link "Logout":
    - /url: /gqa/logout
- main:
  - text: 1 Release Type Selection In Progress...
  - heading "NIC Checks" [level=1]
  - paragraph: Build Process Management
  - text: Release Type
  - combobox "Release Type":
    - option "Select Release Type" [selected]
    - option "HF"
    - option "Release"
  - heading "Prechecks" [level=3]
  - text: Show Build Diff
  - textbox "Enter build diff information..."
  - checkbox "Verified" [disabled]
  - text: Verified Show Changed Files
  - textbox "Enter changed files information..."
  - checkbox "Verified" [disabled]
  - text: Verified Show ZDCM Files
  - textbox "Enter ZDCM files information..."
  - checkbox "Verified" [disabled]
  - text: Verified Show Migration Files
  - textbox "Enter migration files information..."
  - checkbox "Verified" [disabled]
  - text: Verified
- contentinfo:
  - paragraph: © 2024 Graphikos Automation - Internal QA Team Tool
  - paragraph: Version 1.0.0
```