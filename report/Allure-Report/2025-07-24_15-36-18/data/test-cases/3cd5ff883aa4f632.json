{"uid": "3cd5ff883aa4f632", "name": "Test browser dropdown functionality", "fullName": "test-update-build.spec.ts#Update Build Workflow Test browser dropdown functionality", "historyId": "380cf6ce8daf7988b24027aeb8b3224d:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1753351184550, "stop": 1753351195917, "duration": 11367}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "Before Hooks", "time": {"start": 1753351184550, "stop": 1753351195458, "duration": 10908}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "beforeEach hook", "time": {"start": 1753351184550, "stop": 1753351195457, "duration": 10907}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "context", "time": {"start": 1753351184551, "stop": 1753351184556, "duration": 5}, "status": "passed", "steps": [{"name": "Create context", "time": {"start": 1753351184552, "stop": 1753351184556, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753351184556, "stop": 1753351184999, "duration": 443}, "status": "passed", "steps": [{"name": "Create page", "time": {"start": 1753351184556, "stop": 1753351184999, "duration": 443}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Navigate to \"/gqa/login\"", "time": {"start": 1753351185000, "stop": 1753351185072, "duration": 72}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"editor\" locator('#username')", "time": {"start": 1753351185073, "stop": 1753351185089, "duration": 16}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"editor123\" locator('#password')", "time": {"start": 1753351185090, "stop": 1753351185124, "duration": 34}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('button[type=\"submit\"]')", "time": {"start": 1753351185124, "stop": 1753351185440, "duration": 316}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for load state \"load\"", "time": {"start": 1753351185442, "stop": 1753351185450, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('a[data-section=\"update-build\"]')", "time": {"start": 1753351185451, "stop": 1753351195456, "duration": 10005}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": true, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 10, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 11, "attachmentsCount": 0, "attachmentStep": false}, {"name": "After Hooks", "time": {"start": 1753351195459, "stop": 1753351195793, "duration": 334}, "status": "passed", "steps": [{"name": "Screenshot", "time": {"start": 1753351195459, "stop": 1753351195568, "duration": 109}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753351195569, "stop": 1753351195569, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753351195569, "stop": 1753351195569, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 3, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Worker Cleanup", "time": {"start": 1753351195793, "stop": 1753351195913, "duration": 120}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753351195793, "stop": 1753351195913, "duration": 120}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "20f644f9eee31662", "name": "screenshot", "source": "20f644f9eee31662.png", "type": "image/png", "size": 111855}, {"uid": "c5f1a6568b4abfef", "name": "video", "source": "c5f1a6568b4abfef.webm", "type": "video/webm", "size": 231168}, {"uid": "7ebdfa681bb4cd43", "name": "error-context", "source": "7ebdfa681bb4cd43.txt", "type": "text/markdown", "size": 1215}], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 18, "attachmentsCount": 3, "attachmentStep": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test-update-build.spec.ts > Update Build Workflow"}, {"name": "host", "value": "prakash-3480"}, {"name": "thread", "value": "prakash-3480-17059-playwright-worker-1"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "Update Build Workflow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "2c5e1105b09ad6dc", "status": "failed", "statusDetails": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "time": {"start": 1753340447523, "stop": 1753340459230, "duration": 11707}}], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": []}, "source": "3cd5ff883aa4f632.json", "parameterValues": ["chromium"]}