{"uid": "2c5e1105b09ad6dc", "name": "Test browser dropdown functionality", "fullName": "test-update-build.spec.ts#Update Build Workflow Test browser dropdown functionality", "historyId": "380cf6ce8daf7988b24027aeb8b3224d:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1753340447523, "stop": 1753340459230, "duration": 11707}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "Before Hooks", "time": {"start": 1753340447524, "stop": 1753340458631, "duration": 11107}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "beforeEach hook", "time": {"start": 1753340447525, "stop": 1753340458630, "duration": 11105}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "browser", "time": {"start": 1753340447526, "stop": 1753340447830, "duration": 304}, "status": "passed", "steps": [{"name": "Launch browser", "time": {"start": 1753340447528, "stop": 1753340447830, "duration": 302}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753340447831, "stop": 1753340447837, "duration": 6}, "status": "passed", "steps": [{"name": "Create context", "time": {"start": 1753340447832, "stop": 1753340447837, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753340447838, "stop": 1753340448165, "duration": 327}, "status": "passed", "steps": [{"name": "Create page", "time": {"start": 1753340447841, "stop": 1753340448165, "duration": 324}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Navigate to \"/gqa/login\"", "time": {"start": 1753340448169, "stop": 1753340448293, "duration": 124}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"editor\" locator('#username')", "time": {"start": 1753340448294, "stop": 1753340448357, "duration": 63}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"editor123\" locator('#password')", "time": {"start": 1753340448365, "stop": 1753340448377, "duration": 12}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('button[type=\"submit\"]')", "time": {"start": 1753340448379, "stop": 1753340448619, "duration": 240}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for load state \"load\"", "time": {"start": 1753340448619, "stop": 1753340448624, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('a[data-section=\"update-build\"]')", "time": {"start": 1753340448624, "stop": 1753340458628, "duration": 10004}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": true, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 12, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 13, "attachmentsCount": 0, "attachmentStep": false}, {"name": "After Hooks", "time": {"start": 1753340458631, "stop": 1753340459105, "duration": 474}, "status": "passed", "steps": [{"name": "Screenshot", "time": {"start": 1753340458631, "stop": 1753340458685, "duration": 54}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753340458687, "stop": 1753340458687, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753340458687, "stop": 1753340458687, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 3, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Worker Cleanup", "time": {"start": 1753340459105, "stop": 1753340459226, "duration": 121}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753340459105, "stop": 1753340459226, "duration": 121}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "3cbad6d651acfc81", "name": "screenshot", "source": "3cbad6d651acfc81.png", "type": "image/png", "size": 61128}, {"uid": "97b995f9214b0ca0", "name": "video", "source": "97b995f9214b0ca0.webm", "type": "video/webm", "size": 446241}, {"uid": "939b1f0e0db21df1", "name": "error-context", "source": "939b1f0e0db21df1.txt", "type": "text/markdown", "size": 502}], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 20, "attachmentsCount": 3, "attachmentStep": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test-update-build.spec.ts > Update Build Workflow"}, {"name": "host", "value": "prakash-3480"}, {"name": "thread", "value": "prakash-3480-14327-playwright-worker-2"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "Update Build Workflow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "2c5e1105b09ad6dc.json", "parameterValues": ["chromium"]}