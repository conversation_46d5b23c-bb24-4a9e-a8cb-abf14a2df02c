{"uid": "7b3dfe282e6de06c", "name": "Verify automation configuration form fields", "fullName": "test-nic-check.spec.ts#NIC Check Complete Workflow Verify automation configuration form fields", "historyId": "bff9135c84878baf8b46e8d12b1795f2:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1753340430051, "stop": 1753340449348, "duration": 19297}, "status": "failed", "statusMessage": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n", "statusTrace": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-nic-check.spec.ts:132:16", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n", "statusTrace": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-nic-check.spec.ts:132:16", "steps": [{"name": "Before Hooks", "time": {"start": 1753340430051, "stop": 1753340437838, "duration": 7787}, "status": "passed", "steps": [{"name": "beforeEach hook", "time": {"start": 1753340430051, "stop": 1753340437838, "duration": 7787}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753340430051, "stop": 1753340432580, "duration": 2529}, "status": "passed", "steps": [{"name": "Launch browser", "time": {"start": 1753340430051, "stop": 1753340432580, "duration": 2529}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753340432580, "stop": 1753340432615, "duration": 35}, "status": "passed", "steps": [{"name": "Create context", "time": {"start": 1753340432580, "stop": 1753340432615, "duration": 35}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753340432615, "stop": 1753340433860, "duration": 1245}, "status": "passed", "steps": [{"name": "Create page", "time": {"start": 1753340432665, "stop": 1753340433860, "duration": 1195}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Navigate to \"/gqa/login\"", "time": {"start": 1753340433868, "stop": 1753340434130, "duration": 262}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"admin\" locator('#username')", "time": {"start": 1753340434302, "stop": 1753340434501, "duration": 199}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"admin123\" locator('#password')", "time": {"start": 1753340434501, "stop": 1753340434613, "duration": 112}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('button[type=\"submit\"]')", "time": {"start": 1753340434614, "stop": 1753340436501, "duration": 1887}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for load state \"load\"", "time": {"start": 1753340436511, "stop": 1753340436584, "duration": 73}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('a[data-section=\"nic-checks\"]')", "time": {"start": 1753340436584, "stop": 1753340436835, "duration": 251}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for timeout", "time": {"start": 1753340436836, "stop": 1753340437838, "duration": 1002}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 13, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 14, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Select option locator('#releaseType')", "time": {"start": 1753340437839, "stop": 1753340447845, "duration": 10006}, "status": "failed", "statusMessage": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n", "statusTrace": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-nic-check.spec.ts:132:16", "steps": [], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": true, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "After Hooks", "time": {"start": 1753340447845, "stop": 1753340448716, "duration": 871}, "status": "passed", "steps": [{"name": "Screenshot", "time": {"start": 1753340447847, "stop": 1753340448176, "duration": 329}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753340448188, "stop": 1753340448188, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753340448188, "stop": 1753340448188, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 3, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Worker Cleanup", "time": {"start": 1753340448716, "stop": 1753340449341, "duration": 625}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753340448716, "stop": 1753340449341, "duration": 625}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "5a2d441e7048f279", "name": "screenshot", "source": "5a2d441e7048f279.png", "type": "image/png", "size": 65153}, {"uid": "17334b88567a08f", "name": "video", "source": "17334b88567a08f.webm", "type": "video/webm", "size": 220723}, {"uid": "97de8ec140f07c74", "name": "error-context", "source": "97de8ec140f07c74.txt", "type": "text/markdown", "size": 551}], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 22, "attachmentsCount": 3, "attachmentStep": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test-nic-check.spec.ts > NIC Check Complete Workflow"}, {"name": "host", "value": "prakash-3480"}, {"name": "thread", "value": "prakash-3480-14327-playwright-worker-1"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "NIC Check Complete Workflow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "7b3dfe282e6de06c.json", "parameterValues": ["chromium"]}