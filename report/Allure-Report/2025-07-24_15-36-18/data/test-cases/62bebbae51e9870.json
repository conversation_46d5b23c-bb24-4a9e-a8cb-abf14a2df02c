{"uid": "62bebbae51e9870", "name": "Verify Update Build automation form fields and defaults", "fullName": "test-update-build.spec.ts#Update Build Workflow Verify Update Build automation form fields and defaults", "historyId": "4a206ed6fffdf2f719976ad112183305:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1753351178955, "stop": 1753351192294, "duration": 13339}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "Before Hooks", "time": {"start": 1753351178955, "stop": 1753351191753, "duration": 12798}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "beforeEach hook", "time": {"start": 1753351178955, "stop": 1753351191752, "duration": 12797}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "browser", "time": {"start": 1753351178955, "stop": 1753351179991, "duration": 1036}, "status": "passed", "steps": [{"name": "Launch browser", "time": {"start": 1753351178955, "stop": 1753351179991, "duration": 1036}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753351179991, "stop": 1753351180006, "duration": 15}, "status": "passed", "steps": [{"name": "Create context", "time": {"start": 1753351179993, "stop": 1753351180005, "duration": 12}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753351180006, "stop": 1753351180805, "duration": 799}, "status": "passed", "steps": [{"name": "Create page", "time": {"start": 1753351180007, "stop": 1753351180804, "duration": 797}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Navigate to \"/gqa/login\"", "time": {"start": 1753351180808, "stop": 1753351181022, "duration": 214}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"editor\" locator('#username')", "time": {"start": 1753351181026, "stop": 1753351181088, "duration": 62}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"editor123\" locator('#password')", "time": {"start": 1753351181090, "stop": 1753351181176, "duration": 86}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('button[type=\"submit\"]')", "time": {"start": 1753351181176, "stop": 1753351181682, "duration": 506}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for load state \"load\"", "time": {"start": 1753351181682, "stop": 1753351181750, "duration": 68}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('a[data-section=\"update-build\"]')", "time": {"start": 1753351181750, "stop": 1753351191750, "duration": 10000}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": true, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 12, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 13, "attachmentsCount": 0, "attachmentStep": false}, {"name": "After Hooks", "time": {"start": 1753351191753, "stop": 1753351192161, "duration": 408}, "status": "passed", "steps": [{"name": "Screenshot", "time": {"start": 1753351191753, "stop": 1753351191920, "duration": 167}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753351191923, "stop": 1753351191923, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753351191924, "stop": 1753351191924, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 3, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Worker Cleanup", "time": {"start": 1753351192161, "stop": 1753351192287, "duration": 126}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753351192161, "stop": 1753351192287, "duration": 126}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "204e904d8c316ab1", "name": "screenshot", "source": "204e904d8c316ab1.png", "type": "image/png", "size": 111849}, {"uid": "e10d754187f71513", "name": "video", "source": "e10d754187f71513.webm", "type": "video/webm", "size": 257061}, {"uid": "f220e66607c50787", "name": "error-context", "source": "f220e66607c50787.txt", "type": "text/markdown", "size": 1215}], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 20, "attachmentsCount": 3, "attachmentStep": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test-update-build.spec.ts > Update Build Workflow"}, {"name": "host", "value": "prakash-3480"}, {"name": "thread", "value": "prakash-3480-17059-playwright-worker-3"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "Update Build Workflow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "9ec5b83e9cfff87c", "status": "failed", "statusDetails": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "time": {"start": 1753340430056, "stop": 1753340447305, "duration": 17249}}], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": []}, "source": "62bebbae51e9870.json", "parameterValues": ["chromium"]}