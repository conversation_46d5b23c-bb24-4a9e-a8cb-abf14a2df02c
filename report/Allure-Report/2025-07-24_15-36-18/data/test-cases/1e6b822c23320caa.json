{"uid": "1e6b822c23320caa", "name": "Complete NIC Check form workflow", "fullName": "test-nic-check.spec.ts#NIC Check Complete Workflow Complete NIC Check form workflow", "historyId": "4ae4a402cf0935ad338415c135be6811:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1753351178951, "stop": 1753351193578, "duration": 14627}, "status": "failed", "statusMessage": "Error: Timed out 10000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('#automationSection')\nExpected: visible\nReceived: hidden\nCall log:\n  - Expect \"toBeVisible\" with timeout 10000ms\n  - waiting for locator('#automationSection')\n    14 × locator resolved to <div class=\"form-section\" id=\"automationSection\">…</div>\n       - unexpected value \"hidden\"\n", "statusTrace": "Error: Timed out 10000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('#automationSection')\nExpected: visible\nReceived: hidden\nCall log:\n  - Expect \"toBeVisible\" with timeout 10000ms\n  - waiting for locator('#automationSection')\n    14 × locator resolved to <div class=\"form-section\" id=\"automationSection\">…</div>\n       - unexpected value \"hidden\"\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-nic-check.spec.ts:54:54", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "Error: Timed out 10000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('#automationSection')\nExpected: visible\nReceived: hidden\nCall log:\n  - Expect \"toBeVisible\" with timeout 10000ms\n  - waiting for locator('#automationSection')\n    14 × locator resolved to <div class=\"form-section\" id=\"automationSection\">…</div>\n       - unexpected value \"hidden\"\n", "statusTrace": "Error: Timed out 10000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('#automationSection')\nExpected: visible\nReceived: hidden\nCall log:\n  - Expect \"toBeVisible\" with timeout 10000ms\n  - waiting for locator('#automationSection')\n    14 × locator resolved to <div class=\"form-section\" id=\"automationSection\">…</div>\n       - unexpected value \"hidden\"\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-nic-check.spec.ts:54:54", "steps": [{"name": "Before Hooks", "time": {"start": 1753351178952, "stop": 1753351182935, "duration": 3983}, "status": "passed", "steps": [{"name": "beforeEach hook", "time": {"start": 1753351178952, "stop": 1753351182935, "duration": 3983}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753351178952, "stop": 1753351179991, "duration": 1039}, "status": "passed", "steps": [{"name": "Launch browser", "time": {"start": 1753351178952, "stop": 1753351179991, "duration": 1039}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753351179991, "stop": 1753351180005, "duration": 14}, "status": "passed", "steps": [{"name": "Create context", "time": {"start": 1753351179991, "stop": 1753351180005, "duration": 14}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753351180005, "stop": 1753351180806, "duration": 801}, "status": "passed", "steps": [{"name": "Create page", "time": {"start": 1753351180006, "stop": 1753351180806, "duration": 800}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Navigate to \"/gqa/login\"", "time": {"start": 1753351180809, "stop": 1753351181009, "duration": 200}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"admin\" locator('#username')", "time": {"start": 1753351181024, "stop": 1753351181088, "duration": 64}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"admin123\" locator('#password')", "time": {"start": 1753351181090, "stop": 1753351181185, "duration": 95}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('button[type=\"submit\"]')", "time": {"start": 1753351181207, "stop": 1753351181682, "duration": 475}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for load state \"load\"", "time": {"start": 1753351181682, "stop": 1753351181760, "duration": 78}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('a[data-section=\"nic-checks\"]')", "time": {"start": 1753351181763, "stop": 1753351181933, "duration": 170}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for timeout", "time": {"start": 1753351181934, "stop": 1753351182935, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 13, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 14, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Select option locator('#releaseType')", "time": {"start": 1753351182936, "stop": 1753351182944, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toBeVisible", "time": {"start": 1753351182946, "stop": 1753351182954, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"https://github.com/test/gc-build-hf-123\" locator('input[name=\"buildsetup\"]')", "time": {"start": 1753351182954, "stop": 1753351182963, "duration": 9}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"https://github.com/test/zohoshow-build-hf-123\" locator('input[name=\"zohoshowinput\"]')", "time": {"start": 1753351182963, "stop": 1753351182972, "duration": 9}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"shape-framework-v2.1.0\" locator('input[name=\"shapeframework\"]')", "time": {"start": 1753351182972, "stop": 1753351182984, "duration": 12}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"graphikos-media-v3.2.1\" locator('input[name=\"graphikosmedia\"]')", "time": {"start": 1753351182985, "stop": 1753351182989, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"show-rendering-v4.0.0\" locator('input[name=\"showrenderingframework\"]')", "time": {"start": 1753351182990, "stop": 1753351182995, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"graphikos-i18n-v2.5.0\" locator('input[name=\"graphikosi18n\"]')", "time": {"start": 1753351182996, "stop": 1753351183001, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"show-listing-dialog-v3.1.0\" locator('input[name=\"showlistingdialog\"]')", "time": {"start": 1753351183001, "stop": 1753351183005, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"show-right-panel-v2.8.0\" locator('input[name=\"showrightpanel\"]')", "time": {"start": 1753351183005, "stop": 1753351183010, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"show-slideshow-views-v3.5.0\" locator('input[name=\"showslideshowviews\"]')", "time": {"start": 1753351183011, "stop": 1753351183015, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"show-ui-v5.0.0\" locator('input[name=\"showui\"]')", "time": {"start": 1753351183016, "stop": 1753351183020, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"show-offline-v2.2.0\" locator('input[name=\"showoffline\"]')", "time": {"start": 1753351183021, "stop": 1753351183025, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"show-server-v4.1.0\" locator('input[name=\"showserver\"]')", "time": {"start": 1753351183025, "stop": 1753351183031, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Check locator('input[name=\"enableAutobuildUpdate\"]')", "time": {"start": 1753351183031, "stop": 1753351183165, "duration": 134}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"show-conversion-v3.0.0\" locator('input[name=\"conversion\"]')", "time": {"start": 1753351183165, "stop": 1753351183169, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"show-pictures-v2.5.0\" locator('input[name=\"pictures\"]')", "time": {"start": 1753351183170, "stop": 1753351183174, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"image-conversion-v2.0.0\" locator('input[name=\"imageconversion\"]')", "time": {"start": 1753351183175, "stop": 1753351183179, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('#buildForm button[type=\"submit\"]')", "time": {"start": 1753351183180, "stop": 1753351183245, "duration": 65}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toBeVisible", "time": {"start": 1753351183246, "stop": 1753351193250, "duration": 10004}, "status": "failed", "statusMessage": "Error: Timed out 10000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('#automationSection')\nExpected: visible\nReceived: hidden\nCall log:\n  - Expect \"toBeVisible\" with timeout 10000ms\n  - waiting for locator('#automationSection')\n    14 × locator resolved to <div class=\"form-section\" id=\"automationSection\">…</div>\n       - unexpected value \"hidden\"\n", "statusTrace": "Error: Timed out 10000ms waiting for expect(locator).toBeVisible()\n\nLocator: locator('#automationSection')\nExpected: visible\nReceived: hidden\nCall log:\n  - Expect \"toBeVisible\" with timeout 10000ms\n  - waiting for locator('#automationSection')\n    14 × locator resolved to <div class=\"form-section\" id=\"automationSection\">…</div>\n       - unexpected value \"hidden\"\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-nic-check.spec.ts:54:54", "steps": [], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": true, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "After Hooks", "time": {"start": 1753351193250, "stop": 1753351193466, "duration": 216}, "status": "passed", "steps": [{"name": "Screenshot", "time": {"start": 1753351193250, "stop": 1753351193403, "duration": 153}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753351193404, "stop": 1753351193404, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753351193404, "stop": 1753351193404, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 3, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Worker Cleanup", "time": {"start": 1753351193466, "stop": 1753351193573, "duration": 107}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753351193466, "stop": 1753351193573, "duration": 107}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "110545faa8de088d", "name": "screenshot", "source": "110545faa8de088d.png", "type": "image/png", "size": 71153}, {"uid": "c11fa362d12e0cd0", "name": "video", "source": "c11fa362d12e0cd0.webm", "type": "video/webm", "size": 495232}, {"uid": "6ff3e1e936066993", "name": "error-context", "source": "6ff3e1e936066993.txt", "type": "text/markdown", "size": 2382}], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 41, "attachmentsCount": 3, "attachmentStep": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test-nic-check.spec.ts > NIC Check Complete Workflow"}, {"name": "host", "value": "prakash-3480"}, {"name": "thread", "value": "prakash-3480-17059-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "NIC Check Complete Workflow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "5e5023a1ca0d4da7", "status": "failed", "statusDetails": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n", "time": {"start": 1753340430052, "stop": 1753340449344, "duration": 19292}}], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": []}, "source": "1e6b822c23320caa.json", "parameterValues": ["chromium"]}