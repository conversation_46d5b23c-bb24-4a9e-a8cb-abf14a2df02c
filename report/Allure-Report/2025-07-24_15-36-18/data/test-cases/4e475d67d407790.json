{"uid": "4e475d67d407790", "name": "Complete Update Build automation workflow", "fullName": "test-update-build.spec.ts#Update Build Workflow Complete Update Build automation workflow", "historyId": "4cf5dcdcf169d186b3b6c88ac6e3c7e5:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1753351178957, "stop": 1753351192324, "duration": 13367}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "Before Hooks", "time": {"start": 1753351178957, "stop": 1753351191786, "duration": 12829}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "beforeEach hook", "time": {"start": 1753351178957, "stop": 1753351191785, "duration": 12828}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "browser", "time": {"start": 1753351178957, "stop": 1753351179991, "duration": 1034}, "status": "passed", "steps": [{"name": "Launch browser", "time": {"start": 1753351178957, "stop": 1753351179991, "duration": 1034}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753351179991, "stop": 1753351180006, "duration": 15}, "status": "passed", "steps": [{"name": "Create context", "time": {"start": 1753351179992, "stop": 1753351180005, "duration": 13}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753351180006, "stop": 1753351180803, "duration": 797}, "status": "passed", "steps": [{"name": "Create page", "time": {"start": 1753351180007, "stop": 1753351180803, "duration": 796}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Navigate to \"/gqa/login\"", "time": {"start": 1753351180808, "stop": 1753351181020, "duration": 212}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"editor\" locator('#username')", "time": {"start": 1753351181024, "stop": 1753351181088, "duration": 64}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"editor123\" locator('#password')", "time": {"start": 1753351181091, "stop": 1753351181176, "duration": 85}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('button[type=\"submit\"]')", "time": {"start": 1753351181186, "stop": 1753351181691, "duration": 505}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for load state \"load\"", "time": {"start": 1753351181691, "stop": 1753351181777, "duration": 86}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('a[data-section=\"update-build\"]')", "time": {"start": 1753351181777, "stop": 1753351191784, "duration": 10007}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": true, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 12, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 13, "attachmentsCount": 0, "attachmentStep": false}, {"name": "After Hooks", "time": {"start": 1753351191786, "stop": 1753351192198, "duration": 412}, "status": "passed", "steps": [{"name": "Screenshot", "time": {"start": 1753351191786, "stop": 1753351191961, "duration": 175}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753351191964, "stop": 1753351191964, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753351191964, "stop": 1753351191964, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 3, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Worker Cleanup", "time": {"start": 1753351192199, "stop": 1753351192320, "duration": 121}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753351192199, "stop": 1753351192320, "duration": 121}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "7405acb5ee102fbf", "name": "screenshot", "source": "7405acb5ee102fbf.png", "type": "image/png", "size": 111849}, {"uid": "c5267afb7c01bc71", "name": "video", "source": "c5267afb7c01bc71.webm", "type": "video/webm", "size": 259830}, {"uid": "538d6a27cc44beb7", "name": "error-context", "source": "538d6a27cc44beb7.txt", "type": "text/markdown", "size": 1215}], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 20, "attachmentsCount": 3, "attachmentStep": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test-update-build.spec.ts > Update Build Workflow"}, {"name": "host", "value": "prakash-3480"}, {"name": "thread", "value": "prakash-3480-17059-playwright-worker-2"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "Update Build Workflow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "681b50d4051a63fb", "status": "failed", "statusDetails": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "time": {"start": 1753340430046, "stop": 1753340447195, "duration": 17149}}], "categories": [{"name": "Product defects", "matchedStatuses": []}], "tags": []}, "source": "4e475d67d407790.json", "parameterValues": ["chromium"]}