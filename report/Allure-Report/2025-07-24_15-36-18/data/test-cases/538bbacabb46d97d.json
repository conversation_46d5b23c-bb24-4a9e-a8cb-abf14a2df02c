{"uid": "538bbacabb46d97d", "name": "Verify automation configuration form fields", "fullName": "test-nic-check.spec.ts#NIC Check Complete Workflow Verify automation configuration form fields", "historyId": "bff9135c84878baf8b46e8d12b1795f2:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1753351178959, "stop": 1753351184543, "duration": 5584}, "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 1, "retriesStatusChange": true, "beforeStages": [], "testStage": {"status": "passed", "steps": [{"name": "Before Hooks", "time": {"start": 1753351178959, "stop": 1753351183045, "duration": 4086}, "status": "passed", "steps": [{"name": "beforeEach hook", "time": {"start": 1753351178959, "stop": 1753351183044, "duration": 4085}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753351178959, "stop": 1753351179991, "duration": 1032}, "status": "passed", "steps": [{"name": "Launch browser", "time": {"start": 1753351178962, "stop": 1753351179991, "duration": 1029}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753351179992, "stop": 1753351180006, "duration": 14}, "status": "passed", "steps": [{"name": "Create context", "time": {"start": 1753351179994, "stop": 1753351180006, "duration": 12}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753351180006, "stop": 1753351180807, "duration": 801}, "status": "passed", "steps": [{"name": "Create page", "time": {"start": 1753351180007, "stop": 1753351180807, "duration": 800}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Navigate to \"/gqa/login\"", "time": {"start": 1753351180810, "stop": 1753351181016, "duration": 206}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"admin\" locator('#username')", "time": {"start": 1753351181024, "stop": 1753351181087, "duration": 63}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"admin123\" locator('#password')", "time": {"start": 1753351181089, "stop": 1753351181175, "duration": 86}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('button[type=\"submit\"]')", "time": {"start": 1753351181187, "stop": 1753351181691, "duration": 504}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for load state \"load\"", "time": {"start": 1753351181691, "stop": 1753351181750, "duration": 59}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('a[data-section=\"nic-checks\"]')", "time": {"start": 1753351181776, "stop": 1753351182040, "duration": 264}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for timeout", "time": {"start": 1753351182041, "stop": 1753351183044, "duration": 1003}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 13, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 14, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Select option locator('#releaseType')", "time": {"start": 1753351183047, "stop": 1753351183062, "duration": 15}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"test-build\" locator('input[name=\"buildsetup\"]')", "time": {"start": 1753351183062, "stop": 1753351183071, "duration": 9}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('#buildForm button[type=\"submit\"]')", "time": {"start": 1753351183072, "stop": 1753351183718, "duration": 646}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toBeVisible", "time": {"start": 1753351183719, "stop": 1753351183825, "duration": 106}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toHaveValue", "time": {"start": 1753351183826, "stop": 1753351183828, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toHaveValue", "time": {"start": 1753351183828, "stop": 1753351183830, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toHaveValue", "time": {"start": 1753351183830, "stop": 1753351183832, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toHaveValue", "time": {"start": 1753351183833, "stop": 1753351183834, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toHaveValue", "time": {"start": 1753351183834, "stop": 1753351183836, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toHaveValue", "time": {"start": 1753351183837, "stop": 1753351183839, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Evaluate locator('select[name=\"browser\"] option')", "time": {"start": 1753351183840, "stop": 1753351183848, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toContain", "time": {"start": 1753351183848, "stop": 1753351183849, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toContain", "time": {"start": 1753351183849, "stop": 1753351183850, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toContain", "time": {"start": 1753351183850, "stop": 1753351183850, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toBeChecked", "time": {"start": 1753351183851, "stop": 1753351183855, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toBeChecked", "time": {"start": 1753351183856, "stop": 1753351183857, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toBeChecked", "time": {"start": 1753351183858, "stop": 1753351183859, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "toBeChecked", "time": {"start": 1753351183859, "stop": 1753351183862, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Screenshot", "time": {"start": 1753351183862, "stop": 1753351184412, "duration": 550}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "After Hooks", "time": {"start": 1753351184415, "stop": 1753351184541, "duration": 126}, "status": "passed", "steps": [{"name": "page", "time": {"start": 1753351184415, "stop": 1753351184415, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753351184415, "stop": 1753351184415, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 2, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 37, "attachmentsCount": 0, "attachmentStep": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test-nic-check.spec.ts > NIC Check Complete Workflow"}, {"name": "host", "value": "prakash-3480"}, {"name": "thread", "value": "prakash-3480-17059-playwright-worker-1"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "NIC Check Complete Workflow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [{"uid": "7b3dfe282e6de06c", "status": "failed", "statusDetails": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n", "time": {"start": 1753340430051, "stop": 1753340449348, "duration": 19297}}], "categories": [], "tags": []}, "source": "538bbacabb46d97d.json", "parameterValues": ["chromium"]}