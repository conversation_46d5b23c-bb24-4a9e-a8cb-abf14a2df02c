{"uid": "5e5023a1ca0d4da7", "name": "Complete NIC Check form workflow", "fullName": "test-nic-check.spec.ts#NIC Check Complete Workflow Complete NIC Check form workflow", "historyId": "4ae4a402cf0935ad338415c135be6811:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1753340430052, "stop": 1753340449344, "duration": 19292}, "status": "failed", "statusMessage": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n", "statusTrace": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-nic-check.spec.ts:23:16", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n", "statusTrace": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-nic-check.spec.ts:23:16", "steps": [{"name": "Before Hooks", "time": {"start": 1753340430052, "stop": 1753340437837, "duration": 7785}, "status": "passed", "steps": [{"name": "beforeEach hook", "time": {"start": 1753340430052, "stop": 1753340437837, "duration": 7785}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753340430052, "stop": 1753340432581, "duration": 2529}, "status": "passed", "steps": [{"name": "Launch browser", "time": {"start": 1753340430052, "stop": 1753340432581, "duration": 2529}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753340432581, "stop": 1753340432626, "duration": 45}, "status": "passed", "steps": [{"name": "Create context", "time": {"start": 1753340432586, "stop": 1753340432626, "duration": 40}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753340432666, "stop": 1753340433962, "duration": 1296}, "status": "passed", "steps": [{"name": "Create page", "time": {"start": 1753340432666, "stop": 1753340433962, "duration": 1296}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Navigate to \"/gqa/login\"", "time": {"start": 1753340433979, "stop": 1753340434146, "duration": 167}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"admin\" locator('#username')", "time": {"start": 1753340434148, "stop": 1753340434399, "duration": 251}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"admin123\" locator('#password')", "time": {"start": 1753340434399, "stop": 1753340434501, "duration": 102}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('button[type=\"submit\"]')", "time": {"start": 1753340434568, "stop": 1753340436500, "duration": 1932}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for load state \"load\"", "time": {"start": 1753340436500, "stop": 1753340436531, "duration": 31}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('a[data-section=\"nic-checks\"]')", "time": {"start": 1753340436532, "stop": 1753340436835, "duration": 303}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for timeout", "time": {"start": 1753340436836, "stop": 1753340437837, "duration": 1001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 13, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 14, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Select option locator('#releaseType')", "time": {"start": 1753340437838, "stop": 1753340447839, "duration": 10001}, "status": "failed", "statusMessage": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n", "statusTrace": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('#releaseType')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-nic-check.spec.ts:23:16", "steps": [], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": true, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "After Hooks", "time": {"start": 1753340447840, "stop": 1753340448735, "duration": 895}, "status": "passed", "steps": [{"name": "Screenshot", "time": {"start": 1753340447843, "stop": 1753340448171, "duration": 328}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753340448181, "stop": 1753340448181, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753340448181, "stop": 1753340448181, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 3, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Worker Cleanup", "time": {"start": 1753340448735, "stop": 1753340449341, "duration": 606}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753340448735, "stop": 1753340449341, "duration": 606}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "19bbd5b5f91401da", "name": "screenshot", "source": "19bbd5b5f91401da.png", "type": "image/png", "size": 65153}, {"uid": "d5c116bc8be948c0", "name": "video", "source": "d5c116bc8be948c0.webm", "type": "video/webm", "size": 236169}, {"uid": "9675ff3f14265f83", "name": "error-context", "source": "9675ff3f14265f83.txt", "type": "text/markdown", "size": 551}], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 22, "attachmentsCount": 3, "attachmentStep": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test-nic-check.spec.ts > NIC Check Complete Workflow"}, {"name": "host", "value": "prakash-3480"}, {"name": "thread", "value": "prakash-3480-14327-playwright-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "NIC Check Complete Workflow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "5e5023a1ca0d4da7.json", "parameterValues": ["chromium"]}