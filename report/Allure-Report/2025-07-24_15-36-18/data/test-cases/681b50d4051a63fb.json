{"uid": "681b50d4051a63fb", "name": "Complete Update Build automation workflow", "fullName": "test-update-build.spec.ts#Update Build Workflow Complete Update Build automation workflow", "historyId": "4cf5dcdcf169d186b3b6c88ac6e3c7e5:5bd835b0d6b1d4ada3b9f0db936e82c8", "time": {"start": 1753340430046, "stop": 1753340447195, "duration": 17149}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [], "testStage": {"status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "Before Hooks", "time": {"start": 1753340430048, "stop": 1753340446674, "duration": 16626}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "beforeEach hook", "time": {"start": 1753340430048, "stop": 1753340446673, "duration": 16625}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [{"name": "browser", "time": {"start": 1753340430048, "stop": 1753340432580, "duration": 2532}, "status": "passed", "steps": [{"name": "Launch browser", "time": {"start": 1753340430048, "stop": 1753340432580, "duration": 2532}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753340432580, "stop": 1753340432615, "duration": 35}, "status": "passed", "steps": [{"name": "Create context", "time": {"start": 1753340432581, "stop": 1753340432615, "duration": 34}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753340432620, "stop": 1753340433868, "duration": 1248}, "status": "passed", "steps": [{"name": "Create page", "time": {"start": 1753340432762, "stop": 1753340433868, "duration": 1106}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Navigate to \"/gqa/login\"", "time": {"start": 1753340433870, "stop": 1753340434158, "duration": 288}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"editor\" locator('#username')", "time": {"start": 1753340434302, "stop": 1753340434388, "duration": 86}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Fill \"editor123\" locator('#password')", "time": {"start": 1753340434399, "stop": 1753340434560, "duration": 161}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('button[type=\"submit\"]')", "time": {"start": 1753340434560, "stop": 1753340436511, "duration": 1951}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Wait for load state \"load\"", "time": {"start": 1753340436511, "stop": 1753340436666, "duration": 155}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Click locator('a[data-section=\"update-build\"]')", "time": {"start": 1753340436666, "stop": 1753340446671, "duration": 10005}, "status": "failed", "statusMessage": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n", "statusTrace": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n  - waiting for locator('a[data-section=\"update-build\"]')\n\n    at /Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/test-update-build.spec.ts:17:16", "steps": [], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": true, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 12, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 13, "attachmentsCount": 0, "attachmentStep": false}, {"name": "After Hooks", "time": {"start": 1753340446674, "stop": 1753340447059, "duration": 385}, "status": "passed", "steps": [{"name": "Screenshot", "time": {"start": 1753340446674, "stop": 1753340446753, "duration": 79}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "page", "time": {"start": 1753340446755, "stop": 1753340446755, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}, {"name": "context", "time": {"start": 1753340446755, "stop": 1753340446755, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 3, "attachmentsCount": 0, "attachmentStep": false}, {"name": "Worker Cleanup", "time": {"start": 1753340447059, "stop": 1753340447187, "duration": 128}, "status": "passed", "steps": [{"name": "browser", "time": {"start": 1753340447060, "stop": 1753340447187, "duration": 127}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "hasContent": false, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 1, "attachmentsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "91f0eaf3d3ac1a61", "name": "screenshot", "source": "91f0eaf3d3ac1a61.png", "type": "image/png", "size": 61128}, {"uid": "e024f8fa4f7d70d1", "name": "video", "source": "e024f8fa4f7d70d1.webm", "type": "video/webm", "size": 287570}, {"uid": "644459f00687eb4c", "name": "error-context", "source": "644459f00687eb4c.txt", "type": "text/markdown", "size": 502}], "parameters": [], "hasContent": true, "shouldDisplayMessage": false, "stepsCount": 20, "attachmentsCount": 3, "attachmentStep": false}, "afterStages": [], "labels": [{"name": "language", "value": "JavaScript"}, {"name": "framework", "value": "Playwright"}, {"name": "titlePath", "value": " > chromium > test-update-build.spec.ts > Update Build Workflow"}, {"name": "host", "value": "prakash-3480"}, {"name": "thread", "value": "prakash-3480-14327-playwright-worker-2"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "Update Build Workflow"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [{"name": "Project", "value": "chromium"}], "links": [], "hidden": true, "retry": true, "extra": {"categories": [], "tags": []}, "source": "681b50d4051a63fb.json", "parameterValues": ["chromium"]}