{"uid": "b1a8273437954620fa374b796ffaacdd", "name": "behaviors", "children": [{"name": "Complete NIC Check form workflow", "uid": "1e6b822c23320caa", "parentUid": "b1a8273437954620fa374b796ffaacdd", "status": "failed", "time": {"start": 1753351178951, "stop": 1753351193578, "duration": 14627}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}, {"name": "Verify Update Build automation form fields and defaults", "uid": "62bebbae51e9870", "parentUid": "b1a8273437954620fa374b796ffaacdd", "status": "failed", "time": {"start": 1753351178955, "stop": 1753351192294, "duration": 13339}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}, {"name": "Complete Update Build automation workflow", "uid": "4e475d67d407790", "parentUid": "b1a8273437954620fa374b796ffaacdd", "status": "failed", "time": {"start": 1753351178957, "stop": 1753351192324, "duration": 13367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}, {"name": "Verify automation configuration form fields", "uid": "538bbacabb46d97d", "parentUid": "b1a8273437954620fa374b796ffaacdd", "status": "passed", "time": {"start": 1753351178959, "stop": 1753351184543, "duration": 5584}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": true, "parameters": ["chromium"], "tags": []}, {"name": "Test browser dropdown functionality", "uid": "3cd5ff883aa4f632", "parentUid": "b1a8273437954620fa374b796ffaacdd", "status": "failed", "time": {"start": 1753351184550, "stop": 1753351195917, "duration": 11367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 1, "retriesStatusChange": false, "parameters": ["chromium"], "tags": []}]}