[{"uid": "4e475d67d407790", "name": "Complete Update Build automation workflow", "time": {"start": 1753351178957, "stop": 1753351192324, "duration": 13367}, "status": "failed", "severity": "normal"}, {"uid": "538bbacabb46d97d", "name": "Verify automation configuration form fields", "time": {"start": 1753351178959, "stop": 1753351184543, "duration": 5584}, "status": "passed", "severity": "normal"}, {"uid": "62bebbae51e9870", "name": "Verify Update Build automation form fields and defaults", "time": {"start": 1753351178955, "stop": 1753351192294, "duration": 13339}, "status": "failed", "severity": "normal"}, {"uid": "1e6b822c23320caa", "name": "Complete NIC Check form workflow", "time": {"start": 1753351178951, "stop": 1753351193578, "duration": 14627}, "status": "failed", "severity": "normal"}, {"uid": "3cd5ff883aa4f632", "name": "Test browser dropdown functionality", "time": {"start": 1753351184550, "stop": 1753351195917, "duration": 11367}, "status": "failed", "severity": "normal"}]