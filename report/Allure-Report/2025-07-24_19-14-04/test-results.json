{"config": {"configFile": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/playwright.config.js", "rootDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/playwright", "forbidOnly": false, "fullyParallel": false, "globalSetup": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/global-setup.js", "globalTeardown": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "report/Allure-Report/2025-07-24_19-14-04/html-report"}], ["/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/node_modules/allure-playwright/dist/index.js", {"outputFolder": "report/Allure-Report/2025-07-24_19-14-04/allure-results"}], ["json", {"outputFile": "report/Allure-Report/2025-07-24_19-14-04/test-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/playwright", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/playwright", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/playwright", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/playwright", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/playwright", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 4, "webServer": {"command": "./gradlew bootRun", "url": "http://localhost:7777/gqa", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: Application startup failed", "stack": "Error: Application startup failed\n    at globalSetup (/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/global-setup.js:50:15)", "location": {"file": "/Users/<USER>/Desktop/tomcatProjects/tomcat9/webapps/PrakashProjects/GQA/tests/global-setup.js", "column": 15, "line": 50}, "snippet": "\u001b[90m   at \u001b[39m../global-setup.js:50\n\n\u001b[0m \u001b[90m 48 |\u001b[39m     } \u001b[36mcatch\u001b[39m (error) {\n \u001b[90m 49 |\u001b[39m         console\u001b[33m.\u001b[39merror(\u001b[32m'❌ GATE application is not ready:'\u001b[39m\u001b[33m,\u001b[39m error\u001b[33m.\u001b[39mmessage)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m         \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'Application startup failed'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     } \u001b[36mfinally\u001b[39m {\n \u001b[90m 52 |\u001b[39m         \u001b[36mawait\u001b[39m browser\u001b[33m.\u001b[39mclose()\u001b[33m;\u001b[39m\n \u001b[90m 53 |\u001b[39m     }\u001b[0m"}], "stats": {"startTime": "2025-07-24T13:44:04.270Z", "duration": 1888.2320000000002, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}