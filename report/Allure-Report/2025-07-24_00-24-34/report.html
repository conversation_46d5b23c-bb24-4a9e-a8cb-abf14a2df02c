<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GATE Application - Standalone Test Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        .content {
            padding: 2rem;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .summary-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .summary-card h3 {
            margin: 0 0 0.5rem 0;
            color: #495057;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .summary-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin: 0;
        }
        .test-results {
            margin-top: 2rem;
        }
        .test-item {
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .test-name {
            font-weight: 600;
            color: #495057;
        }
        .test-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-passed {
            background: #d4edda;
            color: #155724;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        .status-skipped {
            background: #fff3cd;
            color: #856404;
        }
        .test-details {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        .test-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 1rem;
            margin-top: 0.5rem;
            font-family: monospace;
            font-size: 0.8rem;
            color: #721c24;
        }
        .test-media {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .media-section {
            margin-bottom: 1rem;
        }
        .media-section h5 {
            margin: 0 0 0.5rem 0;
            color: #495057;
            font-size: 0.9rem;
        }
        .screenshot-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        .screenshot-item {
            max-width: 200px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }
        .screenshot-item img {
            width: 100%;
            height: auto;
            cursor: pointer;
        }
        .video-player {
            max-width: 100%;
            border-radius: 4px;
        }
        .expandable-media {
            cursor: pointer;
            background: #e9ecef;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.25rem 0;
            font-size: 0.9rem;
        }
        .expandable-media:hover {
            background: #dee2e6;
        }
        .footer {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GATE Test Report</h1>
            <p>Standalone Test Execution Results</p>
            <p>Generated: 7/25/2025, 12:24:34 AM</p>
            <p>Report ID: 2025-07-24_00-24-34</p>
        </div>
        
        <div class="content">
            <div class="summary">
                <div class="summary-card">
                    <h3>Total Tests</h3>
                    <div class="number">0</div>
                </div>
                <div class="summary-card">
                    <h3>Passed</h3>
                    <div class="number">0</div>
                </div>
                <div class="summary-card">
                    <h3>Failed</h3>
                    <div class="number">0</div>
                </div>
                <div class="summary-card">
                    <h3>Success Rate</h3>
                    <div class="number">0%</div>
                </div>
                <div class="summary-card">
                    <h3>Duration</h3>
                    <div class="number">0s</div>
                </div>
            </div>

            <div class="test-results">
                <h2>Test Execution Details</h2>
                
            </div>
        </div>

        <div class="footer">
            <p><strong>GATE Application - Comprehensive Testing Framework</strong></p>
            <p>This is a standalone report that can be viewed without a server.</p>
            <p>Generated by Playwright with custom standalone reporter.</p>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.9);">
        <span onclick="closeImageModal()" style="position: absolute; top: 15px; right: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; cursor: pointer;">&times;</span>
        <img id="modalImage" style="margin: auto; display: block; width: 80%; max-width: 700px; margin-top: 50px;">
    </div>

    <script>
        function toggleMediaSection(sectionId) {
            const section = document.getElementById(sectionId);
            const arrow = event.target.querySelector('.arrow');

            if (section.style.display === 'none') {
                section.style.display = 'block';
                arrow.textContent = '▼';
            } else {
                section.style.display = 'none';
                arrow.textContent = '▶';
            }
        }

        function openImageModal(imageSrc) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.style.display = 'block';
            modalImg.src = imageSrc;
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>