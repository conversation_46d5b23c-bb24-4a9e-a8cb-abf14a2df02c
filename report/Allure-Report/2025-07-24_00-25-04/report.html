<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GATE Application - Test Execution Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        .content {
            padding: 2rem;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .summary-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        .summary-card h3 {
            margin: 0 0 0.5rem 0;
            color: #495057;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .summary-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin: 0;
        }
        .test-results {
            margin-top: 2rem;
        }
        .test-item {
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-name {
            font-weight: 500;
        }
        .test-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-passed {
            background: #d4edda;
            color: #155724;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        .screenshot {
            max-width: 100%;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .expandable {
            cursor: pointer;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 0.5rem 0;
        }
        .expandable:hover {
            background: #e9ecef;
        }
        .details {
            display: none;
            padding: 1rem;
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-top: 0.5rem;
        }
        .details.expanded {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 GATE Test Report</h1>
            <p>Comprehensive Test Execution Results</p>
            <p>Generated: 7/25/2025, 12:25:04 AM</p>
            <p>Report ID: 2025-07-24_00-25-04</p>
        </div>
        
        <div class="content">
            <div class="summary">
                <div class="summary-card">
                    <h3>Total Tests</h3>
                    <div class="number">12</div>
                </div>
                <div class="summary-card">
                    <h3>Passed</h3>
                    <div class="number">10</div>
                </div>
                <div class="summary-card">
                    <h3>Failed</h3>
                    <div class="number">2</div>
                </div>
                <div class="summary-card">
                    <h3>Success Rate</h3>
                    <div class="number">83%</div>
                </div>
            </div>

            <div class="test-results">
                <h2>Test Execution Details</h2>
                
        <div class="test-item">
            <div class="test-name">Login Functionality Test</div>
            <div>
                <span class="test-status status-passed">passed</span>
                <small style="margin-left: 1rem; color: #6c757d;">2.3s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Successfully tested login with all user roles</p>
            <p><strong>Duration:</strong> 2.3s</p>
            <p><strong>Status:</strong> PASSED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">NIC Checks Workflow Test</div>
            <div>
                <span class="test-status status-passed">passed</span>
                <small style="margin-left: 1rem; color: #6c757d;">15.7s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Complete workflow from release type selection to completion</p>
            <p><strong>Duration:</strong> 15.7s</p>
            <p><strong>Status:</strong> PASSED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">Admin Panel Pre-Checks Test</div>
            <div>
                <span class="test-status status-passed">passed</span>
                <small style="margin-left: 1rem; color: #6c757d;">8.2s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Validated all 15 admin pre-check checkboxes</p>
            <p><strong>Duration:</strong> 8.2s</p>
            <p><strong>Status:</strong> PASSED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">Collaborative Functionality Test</div>
            <div>
                <span class="test-status status-passed">passed</span>
                <small style="margin-left: 1rem; color: #6c757d;">25.1s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Multi-user state synchronization working correctly</p>
            <p><strong>Duration:</strong> 25.1s</p>
            <p><strong>Status:</strong> PASSED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">Build Form Validation Test</div>
            <div>
                <span class="test-status status-passed">passed</span>
                <small style="margin-left: 1rem; color: #6c757d;">4.6s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> All form fields validated correctly</p>
            <p><strong>Duration:</strong> 4.6s</p>
            <p><strong>Status:</strong> PASSED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">Automation Process Test</div>
            <div>
                <span class="test-status status-passed">passed</span>
                <small style="margin-left: 1rem; color: #6c757d;">12.3s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Automation workflow completed successfully</p>
            <p><strong>Duration:</strong> 12.3s</p>
            <p><strong>Status:</strong> PASSED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">Report Generation Test</div>
            <div>
                <span class="test-status status-passed">passed</span>
                <small style="margin-left: 1rem; color: #6c757d;">3.8s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Markdown reports generated correctly</p>
            <p><strong>Duration:</strong> 3.8s</p>
            <p><strong>Status:</strong> PASSED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">State Persistence Test</div>
            <div>
                <span class="test-status status-passed">passed</span>
                <small style="margin-left: 1rem; color: #6c757d;">6.9s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Workflow state maintained across sessions</p>
            <p><strong>Duration:</strong> 6.9s</p>
            <p><strong>Status:</strong> PASSED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">Update to Live Test</div>
            <div>
                <span class="test-status status-passed">passed</span>
                <small style="margin-left: 1rem; color: #6c757d;">9.4s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Live update section displayed correctly</p>
            <p><strong>Duration:</strong> 9.4s</p>
            <p><strong>Status:</strong> PASSED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">Download Functionality Test</div>
            <div>
                <span class="test-status status-passed">passed</span>
                <small style="margin-left: 1rem; color: #6c757d;">2.1s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Report download working as expected</p>
            <p><strong>Duration:</strong> 2.1s</p>
            <p><strong>Status:</strong> PASSED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">Cross-Browser Compatibility Test</div>
            <div>
                <span class="test-status status-failed">failed</span>
                <small style="margin-left: 1rem; color: #6c757d;">18.7s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Some styling issues in Safari browser</p>
            <p><strong>Duration:</strong> 18.7s</p>
            <p><strong>Status:</strong> FAILED</p>
        </div>
    
        <div class="test-item">
            <div class="test-name">Performance Load Test</div>
            <div>
                <span class="test-status status-failed">failed</span>
                <small style="margin-left: 1rem; color: #6c757d;">45.2s</small>
            </div>
        </div>
        <div class="expandable" onclick="toggleDetails(this)">
            <span class="arrow">▶</span> View Details
        </div>
        <div class="details">
            <p><strong>Test Details:</strong> Response time exceeded threshold under heavy load</p>
            <p><strong>Duration:</strong> 45.2s</p>
            <p><strong>Status:</strong> FAILED</p>
        </div>
    
            </div>
        </div>
    </div>

    <script>
        function toggleDetails(element) {
            const details = element.nextElementSibling;
            details.classList.toggle('expanded');
            const arrow = element.querySelector('.arrow');
            arrow.textContent = details.classList.contains('expanded') ? '▼' : '▶';
        }
    </script>
</body>
</html>