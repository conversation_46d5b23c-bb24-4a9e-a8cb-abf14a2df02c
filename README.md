# GATE Application - Graphikos Automation Testing Environment

## Overview

GATE (Graphikos Automation Testing Environment) is a comprehensive web application designed for managing NIC (Network Infrastructure Checks) workflows, build automation, and collaborative testing processes. The application provides role-based access control, real-time collaboration, and comprehensive reporting capabilities.

## Features

### Core Functionality
- **NIC Checks Workflow**: Complete end-to-end workflow management
- **Build Automation**: Automated build processes with real-time monitoring
- **Role-Based Access Control**: Admin, Editor, QA, and Ultimate user roles
- **Real-Time Collaboration**: Multi-user workflow state synchronization
- **Comprehensive Reporting**: Markdown reports with detailed activity logs
- **Admin Panel**: User management and pre-checks administration

### Workflow Features
- Release type selection and build form configuration
- Automation process management with report confirmation
- Manual testcase integration
- Pre-build update and verification processes
- Live environment deployment with sanity testing
- Complete release management with state cleanup
- **Build Tool**: Gradle

## 📋 Prerequisites

### System Requirements
- **Java**: JDK 11 or higher (JDK 17 recommended)
- **Node.js**: Version 14 or higher (for testing and reporting)
- **Gradle**: Version 7.0 or higher (included in wrapper)
- **Browser**: Chrome, Firefox, or Safari (latest versions)
- **Memory**: Minimum 4GB RAM (8GB recommended for testing)
- **Storage**: At least 2GB free space

### Development Tools
- **IDE**: IntelliJ IDEA, Eclipse, or VS Code
- **Git**: For version control and repository management
- **Playwright**: For automated testing (installed via npm)
- **Allure**: For advanced test reporting (optional)

## 🛠️ Installation & Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd GQA
```

### 2. Build the Application

```bash
./gradlew build
```

### 3. Install Playwright Browsers

```bash
./gradlew installPlaywrightBrowsers
```

### 4. Run the Application

```bash
./gradlew bootRun
```

The application will be available at: `http://localhost:7777/gqa`

## 👥 Default Users

The application comes with pre-configured users:

| Username | Password | Role | Description |
|----------|----------|------|-------------|
| admin | admin123 | ADMIN | Full NIC Checks workflow |
| editor | editor123 | EDITOR | Build configuration and updates |
| qauser | qa123 | QA | Testing and verification |
| ultimate | ultimate123 | ULTIMATE | Complete system access + Admin panel |

## 🧪 Testing Instructions

### Playwright Tests
```bash
# Run all Playwright tests
npx playwright test

# Run tests with UI mode
npx playwright test --ui

# Run tests in headed mode (visible browser)
npx playwright test --headed

# Run specific test file
npx playwright test tests/multi-user-collaborative-test.js

# Generate standalone HTML report
npm run report:standalone

# Run tests and generate report
npm run test:report
```

### Unit Tests
```bash
# Run Java unit tests
./gradlew test

# Run tests with coverage
./gradlew test jacocoTestReport
```

### Allure Reports
```bash
# Generate Allure report
./gradlew allureReport

# Serve Allure report
./gradlew allureServe

# Generate standalone report
npm run report:standalone
```

### View Test Reports
- **Playwright HTML Report**: `test-results/html-report/index.html`
- **Standalone Report**: `report/Allure-Report/[timestamp]/report.html`
- **Allure Report**: `allure-report/index.html`

## 📱 Application Usage

### Complete NIC Checks Workflow

#### Step 1: Login
1. Navigate to http://localhost:7777/gqa/login
2. Use credentials from the table above
3. Click "Login" to access the main application

#### Step 2: Release Type Selection
1. Go to the NIC Checks section
2. Select release type (HF or Release) from dropdown
3. Build form will appear automatically

#### Step 3: Build Configuration
1. Fill in all required fields:
   - Build Setup URL
   - Zoho Show URL
   - Framework versions (Shape, Graphikos Media, etc.)
2. Check Auto Build Update checkboxes as needed
3. Submit the build form

#### Step 4: Automation Process
1. Click "Start Automation" when section appears
2. Wait for automation to complete
3. Check "Report Received" when automation report is ready

#### Step 5: Manual Testing
1. Fill in Manual Testcase Sheet URL
2. Check "Manual Testcase" checkbox
3. Complete all required validations

#### Step 6: Admin Pre-Checks (Admin Role Required)
1. Navigate to Admin Panel (http://localhost:7777/gqa/admin)
2. Complete all 15 pre-check verifications:
   - Show Build Diff (QA, Server, Client)
   - Show Migration Files (QA, Server, Client)
   - Database Changes (QA, Server, Client)
   - Configuration Updates (QA, Server, Client)
   - Dependencies Check (QA, Server, Client)
3. All checkboxes must be completed before proceeding

#### Step 7: Pre-Build Update
1. Click "Update Build in Pre" (enabled only when all requirements met)
2. Wait for update to complete
3. Pre-Build Verification section will appear

#### Step 8: Pre-Build Verification
1. Check "Pre Sanity" checkbox
2. Check "Pre Automation" checkbox
3. Update to Live section will appear automatically

#### Step 9: Live Deployment
1. Click "Update to Live" button
2. Complete "Sanity in Live" checkbox
3. Complete Release button will appear

#### Step 10: Release Completion
1. Click "Complete Release" button
2. System will redirect to Check Status page
3. Workflow will reset for new builds

### Admin Panel Features

#### User Management
- View all registered users
- Change user roles (Admin, Editor, QA, Ultimate)
- Add new users to the system

#### Pre-Checks Management
- Monitor all pre-check categories
- Verify QA, Server, and Client approvals
- Track completion status across all categories

#### System Reports
- View latest Playwright test results
- Access Allure reports
- Download comprehensive build reports

### Collaborative Features

#### Real-Time State Synchronization
- Multiple users can work on the same workflow
- Changes are automatically saved every 30 seconds
- Form modifications trigger immediate state updates
- Cross-browser state sharing

#### Multi-User Workflow
- Editor: Can configure builds and start automation
- Admin: Can complete pre-checks and manage users
- QA: Can verify testing and complete validations
- Ultimate: Has complete system access

#### State Persistence
- Workflow state maintained across browser sessions
- Page refreshes preserve current progress
- Automatic recovery from interruptions

## 🔌 API Documentation

### Authentication Endpoints
- `POST /login` - User authentication
- `POST /logout` - User logout
- `GET /main` - Main application page (requires authentication)

### Build Management Endpoints
- `POST /build-update-process` - Submit build configuration
- `GET /api/builds` - Get list of builds
- `POST /api/builds/{id}/confirm-report` - Confirm automation report
- `PUT /api/builds/{id}/status` - Update build status

### Admin Endpoints
- `GET /admin` - Admin panel access (Ultimate role required)
- `POST /admin/users` - Create new user
- `PUT /admin/users/{id}/role` - Update user role
- `GET /admin/users` - Get all users
- `DELETE /admin/users/{id}` - Delete user

### Report Endpoints
- `GET /api/reports/{buildId}` - Get build report
- `POST /api/reports/{buildId}/download` - Download report as Markdown
- `GET /api/reports/status` - Get all completed builds

### WebSocket Endpoints (Collaborative Features)
- `/ws/workflow` - Real-time workflow state updates
- `/ws/notifications` - System notifications
- `/ws/collaboration` - Multi-user collaboration events

## 🔧 Troubleshooting

### Common Issues

#### Application Won't Start
**Problem**: Application fails to start or shows port conflicts
**Solutions**:
- Check if port 7777 is available: `lsof -i :7777`
- Kill existing processes: `kill -9 $(lsof -t -i:7777)`
- Verify Java version: `java -version` (should be 11+)
- Clean and rebuild: `./gradlew clean build`

#### Login Issues
**Problem**: Cannot login with provided credentials
**Solutions**:
- Clear browser cache and cookies
- Verify credentials from the user table above
- Check browser console for JavaScript errors
- Restart application to reset H2 database

#### Workflow State Issues
**Problem**: Workflow state not saving or synchronizing
**Solutions**:
- Check browser localStorage: Open DevTools → Application → Local Storage
- Verify network connectivity for auto-save
- Clear workflow state: `localStorage.removeItem('currentWorkflowState')`
- Refresh page to reload state

#### Test Execution Issues
**Problem**: Playwright tests failing or not running
**Solutions**:
- Install browsers: `npx playwright install`
- Check application is running on port 7777
- Verify test configuration in `playwright.config.js`
- Run tests in headed mode for debugging: `npx playwright test --headed`

#### Admin Panel Access Issues
**Problem**: Cannot access admin panel or pre-checks
**Solutions**:
- Verify user has Ultimate role
- Check URL: http://localhost:7777/gqa/admin
- Clear session and re-login
- Verify admin panel is properly loaded

#### Report Generation Issues
**Problem**: Reports not generating or displaying incorrectly
**Solutions**:
- Check report directory permissions
- Verify Node.js version (14+ required)
- Run standalone report generation: `npm run report:standalone`
- Check browser console for errors

### Performance Optimization

#### Database Performance
- H2 database runs in-memory for development
- For production, configure PostgreSQL or MySQL
- Monitor memory usage during long test runs

#### Browser Performance
- Close unnecessary browser tabs during testing
- Use headless mode for CI/CD: `npx playwright test --headed=false`
- Increase timeout values for slow networks

#### Memory Management
- Restart application periodically during development
- Monitor Java heap usage: `jps -v`
- Increase JVM memory if needed: `export JAVA_OPTS="-Xmx2g"`

### Development Setup

#### IDE Configuration
**IntelliJ IDEA**:
1. Import as Gradle project
2. Set Project SDK to Java 11+
3. Enable annotation processing
4. Install Thymeleaf plugin for template support

**VS Code**:
1. Install Java Extension Pack
2. Install Gradle for Java extension
3. Configure Java path in settings
4. Install Playwright Test for VS Code

#### Database Access
- **H2 Console**: http://localhost:7777/gqa/h2-console
- **JDBC URL**: `jdbc:h2:mem:testdb`
- **Username**: `sa`
- **Password**: `password`

#### Hot Reload
- Use Spring Boot DevTools for automatic restart
- Enable LiveReload in browser
- Modify static resources without restart

### Getting Help

#### Documentation
- Check this README for comprehensive information
- Review code comments for implementation details
- Examine test files for usage examples

#### Debugging
- Enable debug logging: `logging.level.com.gate=DEBUG`
- Use browser DevTools for frontend issues
- Check application logs for backend errors
- Use Playwright trace viewer for test debugging

#### Community Support
- Create GitHub issues for bugs
- Submit pull requests for improvements
- Follow coding standards and conventions

### 1. Login
- Navigate to `/login`
- Use any of the default credentials above
- Access role-based features

### 2. NIC Checks Workflow

1. **Select Release Type**: Choose HF or Release
2. **Fill Build Form**: Enter all required build information
3. **Submit Build**: Submit the build request
4. **Start Automation**: Configure and trigger automation
5. **Confirm Reports**: Mark automation and manual test reports as received
6. **Pre-Build Update**: Update build to pre-environment
7. **Admin Prechecks**: (Admin only) Verify build diff, changed files, etc.
8. **Live Update**: Update build to live environment
9. **Final Sanity**: Complete final verification
10. **Complete**: Finalize and store build data as JSON

### 3. Navigation Tabs

- **Dashboard**: Overview and statistics
- **NIC Checks**: Main build workflow
- **Update Build**: Manage existing builds
- **Check Status**: View all build summaries and JSON data

## 🔧 Configuration

### Application Properties

Key configuration options in `src/main/resources/application.properties`:

```properties
# Server Configuration
server.port=8080
server.servlet.context-path=/gqa

# Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.username=sa
spring.datasource.password=password

# Automation Configuration
automation.gc.domain-setup=gcautomation
automation.gc.local-url=gcautomation.localzoho.com
automation.cliq.bot.url=https://cliq.zoho.com/company/*********/api/v2/bots/showreport/incoming
```

## 🎨 UI/UX Features

- **Responsive Design**: Works on desktop, tablet, and mobile
- **Professional Theme**: Flat colors and modern design
- **Web-Safe Fonts**: Segoe UI, Tahoma, Geneva, Verdana
- **Interactive Elements**: Smooth animations and transitions
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Dark Mode Support**: Automatic dark mode detection

## 🔐 Security Features

- **Spring Security**: Authentication and authorization
- **Role-Based Access**: Editor and Admin roles
- **CSRF Protection**: Cross-site request forgery protection
- **Session Management**: Secure session handling
- **Password Encryption**: BCrypt password hashing

## 📊 Testing Strategy

### Test Coverage

- **Unit Tests**: Service layer and business logic
- **Integration Tests**: API endpoints and database operations
- **E2E Tests**: Complete user workflows with Playwright
- **Visual Testing**: Screenshot comparisons
- **Performance Testing**: Load and stress testing capabilities

### Test Reports

- **Allure Reports**: Comprehensive test reporting with screenshots
- **JUnit Reports**: Standard test execution reports
- **Coverage Reports**: Code coverage analysis

## 🚀 Deployment

### Development
```bash
./gradlew bootRun
```

### Production Build
```bash
./gradlew bootJar
java -jar build/libs/GQA-1.0-SNAPSHOT.jar
```

### Docker Deployment
```bash
# Build Docker image
docker build -t graphikos-automation .

# Run container
docker run -p 8080:8080 graphikos-automation
```

## 📁 Project Structure

```
src/
├── main/
│   ├── java/graphikos/automation/
│   │   ├── config/          # Configuration classes
│   │   ├── controller/      # Web and API controllers
│   │   ├── model/          # Entity classes
│   │   ├── repository/     # Data access layer
│   │   └── service/        # Business logic
│   ├── resources/
│   │   ├── static/         # CSS, JS, images
│   │   └── templates/      # Thymeleaf templates
└── test/
    └── java/               # Test classes
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📝 API Documentation

### Build Management APIs

- `POST /api/builds/submit` - Submit new build request
- `POST /api/builds/{id}/start-automation` - Start automation
- `POST /api/builds/{id}/update-status` - Update build status
- `POST /api/builds/{id}/update-checkboxes` - Update checkbox states
- `POST /api/builds/{id}/update-prechecks` - Update precheck content (Admin only)
- `GET /api/builds/{id}` - Get build details
- `GET /api/builds/{id}/json` - Get build data as JSON

## 🐛 Troubleshooting

### Common Issues

1. **Port Already in Use**: Change port in `application.properties`
2. **Database Connection**: Check H2 console at `/h2-console`
3. **Playwright Issues**: Run `./gradlew installPlaywrightBrowsers`
4. **Permission Denied**: Ensure proper file permissions on gradlew

### Logs

Application logs are available in the console output. For production, configure logging in `application.properties`:

```properties
logging.level.graphikos.automation=DEBUG
logging.file.name=logs/application.log
```

## 📞 Support

For issues and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the test cases for usage examples

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Graphikos Automation** - Streamlining QA processes with automation and efficiency.
