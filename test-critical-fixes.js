const puppeteer = require('puppeteer');

async function testCriticalFixes() {
    console.log('🚀 Starting comprehensive critical fixes testing...');
    
    const browser = await puppeteer.launch({ 
        headless: false, 
        defaultViewport: null,
        args: ['--start-maximized']
    });
    
    try {
        // Test 1: Role-Based Access Control for Clear State Button
        console.log('\n📋 Test 1: Role-Based Access Control for Clear State Button');
        
        // Test with ADMIN role (should NOT see clear state button)
        const adminPage = await browser.newPage();
        await adminPage.goto('http://localhost:7777/gqa/login');
        await adminPage.type('input[name="username"]', 'admin');
        await adminPage.type('input[name="password"]', 'admin123');
        await adminPage.click('button[type="submit"]');
        await adminPage.waitForNavigation();
        
        await adminPage.goto('http://localhost:7777/gqa/admin');
        await adminPage.waitForSelector('.admin-card', { timeout: 5000 });
        
        const clearStateVisible = await adminPage.$('#clearStateSection');
        if (clearStateVisible) {
            console.log('❌ FAIL: Clear state button visible to ADMIN role (should be ULTIMATE only)');
        } else {
            console.log('✅ PASS: Clear state button correctly hidden from ADMIN role');
        }
        
        // Test with ULTIMATE role (should see clear state button)
        const ultimatePage = await browser.newPage();
        await ultimatePage.goto('http://localhost:7777/gqa/login');
        await ultimatePage.type('input[name="username"]', 'ultimate');
        await ultimatePage.type('input[name="password"]', 'ultimate123');
        await ultimatePage.click('button[type="submit"]');
        await ultimatePage.waitForNavigation();
        
        await ultimatePage.goto('http://localhost:7777/gqa/admin');
        await ultimatePage.waitForSelector('.admin-card', { timeout: 5000 });
        
        const clearStateVisibleUltimate = await ultimatePage.$('#clearStateSection');
        if (clearStateVisibleUltimate) {
            console.log('✅ PASS: Clear state button correctly visible to ULTIMATE role');
        } else {
            console.log('❌ FAIL: Clear state button not visible to ULTIMATE role');
        }
        
        // Test 2: Right Panel Visibility
        console.log('\n📋 Test 2: Right Panel Visibility and Content');
        
        const testPage = await browser.newPage();
        await testPage.goto('http://localhost:7777/gqa/login');
        await testPage.type('input[name="username"]', 'ultimate');
        await testPage.type('input[name="password"]', 'ultimate123');
        await testPage.click('button[type="submit"]');
        await testPage.waitForNavigation();
        
        // Check if right panel is visible
        const rightPanel = await testPage.$('#prechecksPanelAdmin');
        if (rightPanel) {
            const isVisible = await testPage.evaluate(el => {
                const style = window.getComputedStyle(el);
                return style.display !== 'none' && style.visibility !== 'hidden';
            }, rightPanel);
            
            if (isVisible) {
                console.log('✅ PASS: Right panel (#prechecksPanelAdmin) is visible');
                
                // Check for all 4 precheck containers
                const containers = [
                    'sdBuildDiffContent',
                    'changeFileDiffContent',
                    'zdcmDiffContent',
                    'migrationDiffContent'
                ];
                
                let allContainersFound = true;
                for (const containerId of containers) {
                    const container = await testPage.$(`#${containerId}`);
                    if (container) {
                        console.log(`✅ PASS: Found precheck container: ${containerId}`);
                    } else {
                        console.log(`❌ FAIL: Missing precheck container: ${containerId}`);
                        allContainersFound = false;
                    }
                }
                
                if (allContainersFound) {
                    console.log('✅ PASS: All 4 precheck containers are present');
                } else {
                    console.log('❌ FAIL: Some precheck containers are missing');
                }
                
                // Check for checkboxes in each container
                const checkboxes = await testPage.$$('.precheck-admin-checkbox');
                console.log(`📊 Found ${checkboxes.length} precheck checkboxes (expected: 12)`);
                
                if (checkboxes.length >= 12) {
                    console.log('✅ PASS: Sufficient precheck checkboxes found');
                } else {
                    console.log('❌ FAIL: Insufficient precheck checkboxes');
                }
                
            } else {
                console.log('❌ FAIL: Right panel is not visible');
            }
        } else {
            console.log('❌ FAIL: Right panel element not found');
        }
        
        // Test 3: Fresh Form State (No Persistent Default Data)
        console.log('\n📋 Test 3: Fresh Form State Testing');
        
        const freshPage = await browser.newPage();
        await freshPage.goto('http://localhost:7777/gqa/login');
        await freshPage.type('input[name="username"]', 'editor');
        await freshPage.type('input[name="password"]', 'editor123');
        await freshPage.click('button[type="submit"]');
        await freshPage.waitForNavigation();
        
        // Check if release type dropdown is empty/selectable
        const releaseTypeValue = await freshPage.$eval('#releaseType', el => el.value);
        if (releaseTypeValue === '' || releaseTypeValue === null) {
            console.log('✅ PASS: Release type dropdown is fresh (no default selection)');
            
            // Test if we can select a release type
            await freshPage.select('#releaseType', 'HF');
            await freshPage.waitForTimeout(1000);
            
            const buildFormVisible = await freshPage.$('#buildFormContainer');
            if (buildFormVisible) {
                const isFormVisible = await freshPage.evaluate(el => {
                    const style = window.getComputedStyle(el);
                    return style.display !== 'none';
                }, buildFormVisible);
                
                if (isFormVisible) {
                    console.log('✅ PASS: Build form appears after release type selection');
                } else {
                    console.log('❌ FAIL: Build form does not appear after release type selection');
                }
            } else {
                console.log('❌ FAIL: Build form container not found');
            }
        } else {
            console.log(`❌ FAIL: Release type has default value: ${releaseTypeValue}`);
        }
        
        // Test 4: Clear State Functionality (Ultimate Role)
        console.log('\n📋 Test 4: Clear State Functionality Testing');
        
        // First, create some state to clear
        await testPage.select('#releaseType', 'Release');
        await testPage.waitForTimeout(1000);
        await testPage.type('input[name="buildsetup"]', 'https://github.com/test/clear-state-test');
        await testPage.type('#sdBuildDiffContent', 'Test content to be cleared');
        
        console.log('📝 Created test data in forms');
        
        // Go to admin panel and test clear state
        await testPage.goto('http://localhost:7777/gqa/admin');
        await testPage.waitForSelector('#clearStateSection', { timeout: 5000 });
        
        // Test the clear state function (but don't actually execute it to avoid disrupting other tests)
        const clearStateButton = await testPage.$('#clearStateSection button');
        if (clearStateButton) {
            console.log('✅ PASS: Clear state button found and accessible to Ultimate role');
            
            // Test the JavaScript function exists
            const functionExists = await testPage.evaluate(() => {
                return typeof clearApplicationState === 'function';
            });
            
            if (functionExists) {
                console.log('✅ PASS: clearApplicationState function is defined');
            } else {
                console.log('❌ FAIL: clearApplicationState function not found');
            }
        } else {
            console.log('❌ FAIL: Clear state button not found');
        }
        
        // Test 5: Responsive Layout
        console.log('\n📋 Test 5: Responsive Layout Testing');
        
        await testPage.goto('http://localhost:7777/gqa');
        
        // Test desktop layout
        await testPage.setViewport({ width: 1400, height: 800 });
        await testPage.waitForTimeout(500);
        
        const mainContentFlex = await testPage.$eval('#mainContent', el => 
            window.getComputedStyle(el).display
        );
        
        if (mainContentFlex === 'flex') {
            console.log('✅ PASS: Desktop layout uses flex display');
        } else {
            console.log('❌ FAIL: Desktop layout not using flex display');
        }
        
        // Test mobile layout
        await testPage.setViewport({ width: 800, height: 600 });
        await testPage.waitForTimeout(500);
        
        const mobileFlexDirection = await testPage.$eval('#mainContent', el => 
            window.getComputedStyle(el).flexDirection
        );
        
        if (mobileFlexDirection === 'column') {
            console.log('✅ PASS: Mobile layout uses column flex direction');
        } else {
            console.log('❌ FAIL: Mobile layout not using column flex direction');
        }
        
        console.log('\n🎉 Critical fixes testing completed!');
        
        // Close pages
        await adminPage.close();
        await ultimatePage.close();
        await testPage.close();
        await freshPage.close();
        
    } catch (error) {
        console.error('❌ Error during testing:', error);
    } finally {
        await browser.close();
    }
}

// Run the test
testCriticalFixes().catch(console.error);
