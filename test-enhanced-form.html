<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Form Test</title>
    <link rel="stylesheet" href="src/main/resources/static/css/main.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
        }
        .test-title {
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }

        /* Additional styles for new functionality */
        .client-sub-products-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .client-products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .readonly-input {
            background-color: #e9ecef !important;
            cursor: not-allowed;
            color: #6c757d;
        }

        .multi-select-container {
            margin-top: 0.5rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.75rem;
            background: white;
        }

        .selected-items {
            min-height: 40px;
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 6px;
            background: #fff;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            align-items: center;
        }

        .selected-pill {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 16px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .remove-pill {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            padding: 0;
            margin-left: 0.25rem;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .remove-pill:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Enhanced Build Form Test</h1>
        
        <div class="test-section">
            <h2 class="test-title">Test the Enhanced Form Functionality</h2>
            
            <!-- Release Type Selection -->
            <div class="form-container">
                <div class="form-row">
                    <label for="releaseType" class="form-label">Release Type</label>
                    <select id="releaseType" name="releaseType" class="form-select" required>
                        <option value="">Select Release Type</option>
                        <option value="HF">HF</option>
                        <option value="Release">Release</option>
                    </select>
                </div>
            </div>

            <!-- Build Form -->
            <div class="form-container" id="buildFormContainer" style="display: none;">
                <form id="buildForm" method="POST">
                    <div class="form-row">
                        <label class="form-label">GC <span class="required">*</span></label>
                        <input type="text" name="buildsetup" id="gcInput" class="form-input" placeholder="Branch or Build url" required>
                    </div>

                    <div class="form-row">
                        <label class="form-label">Zoho Show <span class="required">*</span></label>
                        <div class="input-with-checkbox">
                            <input type="text" name="zohoshowinput" id="zohoshowInput" class="form-input" placeholder="Build URL or Branch Name" required>
                            <label class="checkbox-label">
                                <input type="checkbox" id="applyBranchToAllCheckbox" class="checkbox-input">
                                Apply same branch name to all sub-products
                            </label>
                        </div>
                    </div>
                
                    <div class="client-sub-products-section">
                        <h3 class="section-title">Client Sub-Products</h3>
                        <div class="client-products-grid">
                            <div class="form-row">
                                <label class="form-label">shapeframework:</label>
                                <input type="text" name="shapeframework" class="form-input client-input">
                            </div>
                            <div class="form-row">
                                <label class="form-label">graphikosmedia:</label>
                                <input type="text" name="graphikosmedia" class="form-input client-input">
                            </div>
                            <div class="form-row">
                                <label class="form-label">showrenderingframework:</label>
                                <input type="text" name="showrenderingframework" class="form-input client-input">
                            </div>
                            <div class="form-row">
                                <label class="form-label">graphikosi18n:</label>
                                <input type="text" name="graphikosi18n" class="form-input client-input">
                            </div>
                            <div class="form-row">
                                <label class="form-label">showlistingdialog:</label>
                                <input type="text" name="showlistingdialog" class="form-input client-input">
                            </div>
                            <div class="form-row">
                                <label class="form-label">showrightpanel:</label>
                                <input type="text" name="showrightpanel" class="form-input client-input">
                            </div>
                            <div class="form-row">
                                <label class="form-label">showslideshowviews:</label>
                                <input type="text" name="showslideshowviews" class="form-input client-input">
                            </div>
                            <div class="form-row">
                                <label class="form-label">showui:</label>
                                <input type="text" name="showui" class="form-input client-input">
                            </div>
                            <div class="form-row">
                                <label class="form-label">showoffline:</label>
                                <input type="text" name="showoffline" class="form-input client-input">
                            </div>
                        </div>
                    </div>
                
                    <div class="form-row">
                        <label class="form-label">showserver:</label>
                        <div class="combo-row">
                            <input type="text" name="showserver" class="form-input" placeholder="">
                            <label>Auto Build Update <input type="checkbox" name="enableAutobuildUpdate" id="serverAutoBuildUpdate" class="checkbox-row"></label>
                            <div class="multi-select-container" id="serverMultiSelectContainer" style="display: none;">
                                <div class="selected-items" id="serverSelectedItems"></div>
                                <select id="serverMultiSelect" class="form-select multi-select-dropdown">
                                    <option value="">Select servers...</option>
                                    <option value="show3">show3</option>
                                    <option value="nic_show3">nic_show3</option>
                                    <option value="show_cdn">show_cdn</option>
                                    <option value="showpublic">showpublic</option>
                                    <option value="showapi">showapi</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <label class="form-label">Conversion</label>
                        <div class="combo-row">
                            <input type="text" name="conversion" class="form-input" placeholder="">
                            <label>Auto Build Update <input type="checkbox" name="conversionAutoBuildUpdate" id="conversionAutoBuildUpdate" class="checkbox-row"></label>
                            <select name="conversionMultiSelect" id="conversionMultiSelect" class="form-select multi-select" multiple style="display: none;">
                                <option value="showconversion">showconversion</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <label class="form-label">Pictures</label>
                        <div class="combo-row">
                            <input type="text" name="pictures" class="form-input" placeholder="">
                            <label>Auto Build Update <input type="checkbox" name="picturesAutoBuildUpdate" id="picturesAutoBuildUpdate" class="checkbox-row"></label>
                            <select name="picturesMultiSelect" id="picturesMultiSelect" class="form-select multi-select" multiple style="display: none;">
                                <option value="showpictures">showpictures</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <label class="form-label">Image Conversion</label>
                        <div class="combo-row">
                            <input type="text" name="imageconversion" class="form-input" placeholder="">
                            <label>Auto Build Update <input type="checkbox" name="imageconversionAutoBuildUpdate" id="imageconversionAutoBuildUpdate" class="checkbox-row"></label>
                            <select name="imageconversionMultiSelect" id="imageconversionMultiSelect" class="form-select multi-select" multiple style="display: none;">
                                <option value="showimageconversion">showimageconversion</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <label class="form-label">Release Notes <span class="required">*</span></label>
                        <textarea name="releaseNotes" id="releaseNotesInput" class="form-control" rows="3" placeholder="Enter release notes..." required></textarea>
                    </div>

                    <div class="form-row">
                        <label class="form-label"></label>
                        <button type="submit" id="submitBuildBtn" class="btn btn-primary" disabled>Submit Build</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="test-section">
            <h3 class="test-title">Test Instructions</h3>
            <ol>
                <li>Select a Release Type to show the build form</li>
                <li>Notice that the Submit button is disabled initially</li>
                <li>Fill in the GC field - button should still be disabled</li>
                <li>Fill in the Zoho Show field - button should still be disabled</li>
                <li><strong>New:</strong> Fill in the Release Notes field - now all mandatory fields are filled and button should be enabled</li>
                <li><strong>Enhanced:</strong> Check the "Apply same branch name to all sub-products" checkbox</li>
                <li>Change the Zoho Show field to a branch name (e.g., "feature-branch") - branch name should auto-copy to all Client Sub-Product fields and make them non-editable</li>
                <li><strong>New:</strong> Uncheck the checkbox - Client Sub-Product fields should become editable again</li>
                <li><strong>Updated:</strong> Enter a build URL ending with .zip (e.g., "build.zip") - Zoho Show field should remain enabled but Client Sub-Product fields should be disabled</li>
                <li><strong>New:</strong> Change from .zip back to a branch name - Client Sub-Product fields should be re-enabled</li>
                <li><strong>Enhanced:</strong> Test Auto Build Update checkboxes - when checked, pill-based multi-select containers should appear</li>
                <li><strong>New:</strong> Select items from dropdowns - they should appear as pills with X buttons for removal</li>
                <li><strong>New:</strong> Click Submit Build to see the complete JSON response with all form data</li>
                <li>Test that GC, Zoho Show, and Release Notes are all required for the Submit button to be enabled</li>
            </ol>
        </div>
    </div>

    <script>
        // Simplified version of the enhanced form functionality for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Test page loaded');
            
            // Initialize release type dropdown handler
            const releaseTypeSelect = document.getElementById('releaseType');
            if (releaseTypeSelect) {
                releaseTypeSelect.addEventListener('change', function() {
                    const buildFormContainer = document.getElementById('buildFormContainer');
                    if (this.value && buildFormContainer) {
                        buildFormContainer.style.display = 'block';
                        initializeEnhancedBuildForm();
                    } else if (buildFormContainer) {
                        buildFormContainer.style.display = 'none';
                    }
                });
            }
        });

        // Initialize Enhanced Build Form Functionality
        function initializeEnhancedBuildForm() {
            console.log('🚀 Initializing enhanced build form functionality...');

            const gcInput = document.getElementById('gcInput');
            const zohoshowInput = document.getElementById('zohoshowInput');
            const releaseNotesInput = document.getElementById('releaseNotesInput');
            const submitBtn = document.getElementById('submitBuildBtn');
            const clientInputs = document.querySelectorAll('.client-input');
            const applyBranchCheckbox = document.getElementById('applyBranchToAllCheckbox');

            // Function to validate mandatory fields and enable/disable submit button
            function validateMandatoryFields() {
                const gcValue = gcInput ? gcInput.value.trim() : '';
                const zohoshowValue = zohoshowInput ? zohoshowInput.value.trim() : '';
                const releaseNotesValue = releaseNotesInput ? releaseNotesInput.value.trim() : '';

                if (submitBtn) {
                    if (gcValue && zohoshowValue && releaseNotesValue) {
                        submitBtn.disabled = false;
                        submitBtn.classList.remove('btn-disabled');
                        console.log('✅ All mandatory fields filled - Submit button enabled');
                    } else {
                        submitBtn.disabled = true;
                        submitBtn.classList.add('btn-disabled');
                        console.log('❌ Missing mandatory fields - Submit button disabled');
                    }
                }
            }

            // Function to handle Zoho Show input changes
            function handleZohoshowInputChange() {
                if (!zohoshowInput) return;

                const value = zohoshowInput.value.trim();
                const isBuildUrl = value.endsWith('.zip');
                const isCheckboxChecked = applyBranchCheckbox && applyBranchCheckbox.checked;

                if (isBuildUrl) {
                    // If it's a build URL (.zip), keep Zoho Show enabled but disable client sub-product inputs
                    zohoshowInput.disabled = false;
                    zohoshowInput.classList.remove('form-disabled');

                    // Disable all client sub-product inputs
                    clientInputs.forEach(input => {
                        input.disabled = true;
                        input.classList.add('form-disabled');
                        input.style.backgroundColor = '#f8f9fa';
                    });

                    // Disable the checkbox when build URL is detected
                    if (applyBranchCheckbox) {
                        applyBranchCheckbox.disabled = true;
                        applyBranchCheckbox.checked = false;
                    }

                    console.log('🔒 Build URL detected - disabled Client Sub-Product inputs only');
                } else if (value && !isBuildUrl) {
                    // If it's a branch name, enable inputs and conditionally auto-copy to client fields
                    zohoshowInput.disabled = false;
                    zohoshowInput.classList.remove('form-disabled');

                    // Enable all client sub-product inputs
                    clientInputs.forEach(input => {
                        input.disabled = false;
                        input.classList.remove('form-disabled');
                        input.style.backgroundColor = '';

                        // Auto-copy branch name only if checkbox is checked
                        if (isCheckboxChecked) {
                            input.value = value;
                        }
                    });

                    // Enable the checkbox when branch name is detected
                    if (applyBranchCheckbox) {
                        applyBranchCheckbox.disabled = false;
                    }

                    if (isCheckboxChecked) {
                        console.log('🔄 Branch name detected - auto-copied to all Client Sub-Product fields:', value);
                    } else {
                        console.log('🔄 Branch name detected - checkbox not checked, no auto-copy');
                    }
                } else {
                    // Empty value - enable inputs but don't auto-fill
                    zohoshowInput.disabled = false;
                    zohoshowInput.classList.remove('form-disabled');

                    clientInputs.forEach(input => {
                        input.disabled = false;
                        input.classList.remove('form-disabled');
                        input.style.backgroundColor = '';
                    });

                    // Enable the checkbox when input is empty
                    if (applyBranchCheckbox) {
                        applyBranchCheckbox.disabled = false;
                    }
                }

                validateMandatoryFields();
            }

            // Add event listeners
            if (gcInput) {
                gcInput.addEventListener('input', validateMandatoryFields);
                gcInput.addEventListener('blur', validateMandatoryFields);
            }

            if (zohoshowInput) {
                // Enable by default
                zohoshowInput.disabled = false;

                zohoshowInput.addEventListener('input', handleZohoshowInputChange);
                zohoshowInput.addEventListener('blur', handleZohoshowInputChange);
            }

            // Add event listener for the apply branch checkbox
            if (applyBranchCheckbox) {
                applyBranchCheckbox.addEventListener('change', function() {
                    // Trigger the Zoho Show input change handler to apply/remove auto-copy
                    handleZohoshowInputChange();
                });
            }

            // Initialize auto-build update functionality
            initializeAutoBuildUpdateDropdowns();

            // Initial validation
            validateMandatoryFields();

            console.log('✅ Enhanced build form functionality initialized');
        }

        // Initialize Auto Build Update Dropdowns
        function initializeAutoBuildUpdateDropdowns() {
            console.log('🚀 Initializing auto build update dropdowns...');

            const autoBuildConfigs = [
                {
                    checkboxId: 'serverAutoBuildUpdate',
                    dropdownId: 'serverMultiSelect',
                    name: 'Server'
                },
                {
                    checkboxId: 'conversionAutoBuildUpdate',
                    dropdownId: 'conversionMultiSelect',
                    name: 'Conversion'
                },
                {
                    checkboxId: 'picturesAutoBuildUpdate',
                    dropdownId: 'picturesMultiSelect',
                    name: 'Pictures'
                },
                {
                    checkboxId: 'imageconversionAutoBuildUpdate',
                    dropdownId: 'imageconversionMultiSelect',
                    name: 'Image Conversion'
                }
            ];

            autoBuildConfigs.forEach(config => {
                const checkbox = document.getElementById(config.checkboxId);
                const dropdown = document.getElementById(config.dropdownId);

                if (checkbox && dropdown) {
                    // Add event listener to show/hide dropdown based on checkbox state
                    checkbox.addEventListener('change', function() {
                        if (this.checked) {
                            dropdown.style.display = 'block';
                            dropdown.setAttribute('required', 'true');
                            console.log(`✅ ${config.name} multi-select dropdown enabled`);
                        } else {
                            dropdown.style.display = 'none';
                            dropdown.removeAttribute('required');
                            // Clear selections when hiding
                            Array.from(dropdown.options).forEach(option => {
                                option.selected = false;
                            });
                            console.log(`❌ ${config.name} multi-select dropdown disabled`);
                        }
                    });

                    // Initial state - hide dropdown if checkbox is not checked
                    if (!checkbox.checked) {
                        dropdown.style.display = 'none';
                    }

                    console.log(`✅ ${config.name} auto build update initialized`);
                } else {
                    console.warn(`⚠️ Missing elements for ${config.name}: checkbox=${!!checkbox}, dropdown=${!!dropdown}`);
                }
            });

            console.log('✅ Auto build update dropdowns initialized');
        }

        // Add form submission handler
        document.addEventListener('DOMContentLoaded', function() {
            const buildForm = document.getElementById('buildForm');
            if (buildForm) {
                buildForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Collect all form data
                    const formData = new FormData(this);
                    const buildData = {};
                    for (let [key, value] of formData.entries()) {
                        buildData[key] = value;
                    }

                    // Add release type
                    const releaseTypeSelect = document.getElementById('releaseType');
                    if (releaseTypeSelect) {
                        buildData.releaseType = releaseTypeSelect.value;
                    }

                    // Create JSON response
                    const jsonResponse = {
                        success: true,
                        message: 'Build form data collected successfully',
                        timestamp: new Date().toISOString(),
                        formData: buildData
                    };

                    // Display JSON response
                    alert('Form Submitted Successfully!\n\nJSON Response:\n' + JSON.stringify(jsonResponse, null, 2));
                    console.log('📋 Form submission JSON:', jsonResponse);
                });
            }
        });

        // Global function to remove pill items (simplified for test)
        function removePillItem(containerId, value) {
            console.log(`Removing ${value} from ${containerId}`);
            // In the real implementation, this would update the selected items
        }
    </script>
</body>
</html>
