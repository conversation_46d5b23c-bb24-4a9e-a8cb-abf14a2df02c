<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Form Test</title>
    <link rel="stylesheet" href="src/main/resources/static/css/main.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
        }
        .test-title {
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Enhanced Build Form Test</h1>
        
        <div class="test-section">
            <h2 class="test-title">Test the Enhanced Form Functionality</h2>
            
            <!-- Release Type Selection -->
            <div class="form-container">
                <div class="form-row">
                    <label for="releaseType" class="form-label">Release Type</label>
                    <select id="releaseType" name="releaseType" class="form-select" required>
                        <option value="">Select Release Type</option>
                        <option value="HF">HF</option>
                        <option value="Release">Release</option>
                    </select>
                </div>
            </div>

            <!-- Build Form -->
            <div class="form-container" id="buildFormContainer" style="display: none;">
                <form id="buildForm" method="POST">
                    <div class="form-row">
                        <label class="form-label">GC <span class="required">*</span></label>
                        <input type="text" name="buildsetup" id="gcInput" class="form-input" placeholder="Branch or Build url" required>
                    </div>

                    <div class="form-row">
                        <label class="form-label">Zoho Show <span class="required">*</span></label>
                        <input type="text" name="zohoshowinput" id="zohoshowInput" class="form-input" placeholder="Build URL or Branch Name" required>
                    </div>
                
                    <div class="form-row">
                        <label class="form-label">Client Sub-Products</label>
                        <div class="form-inputs" id="clientSubProducts">
                            <input type="text" name="shapeframework" class="form-input client-input" placeholder="shapeframework">
                            <input type="text" name="graphikosmedia" class="form-input client-input" placeholder="graphikosmedia">
                            <input type="text" name="showrenderingframework" class="form-input client-input" placeholder="showrenderingframework">
                            <input type="text" name="graphikosi18n" class="form-input client-input" placeholder="graphikosi18n">
                            <input type="text" name="showlistingdialog" class="form-input client-input" placeholder="showlistingdialog">
                            <input type="text" name="showrightpanel" class="form-input client-input" placeholder="showrightpanel">
                            <input type="text" name="showslideshowviews" class="form-input client-input" placeholder="showslideshowviews">
                            <input type="text" name="showui" class="form-input client-input" placeholder="showui">
                            <input type="text" name="showoffline" class="form-input client-input" placeholder="showoffline">
                        </div>
                    </div>
                
                    <div class="form-row">
                        <label class="form-label">Release Notes</label>
                        <textarea name="releaseNotes" id="releaseNotesInput" class="form-control" rows="3" placeholder="Enter release notes..."></textarea>
                    </div>
                
                    <div class="form-row">
                        <label class="form-label"></label>
                        <button type="submit" id="submitBuildBtn" class="btn btn-primary" disabled>Submit Build</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="test-section">
            <h3 class="test-title">Test Instructions</h3>
            <ol>
                <li>Select a Release Type to show the build form</li>
                <li>Notice that the Submit button is disabled initially</li>
                <li>Fill in the GC field - button should still be disabled</li>
                <li>Fill in the Zoho Show field with a branch name (e.g., "feature-branch") - button should become enabled and branch name should auto-copy to all Client Sub-Product fields</li>
                <li>Clear Zoho Show and enter a build URL ending with .zip (e.g., "build.zip") - Zoho Show field should become disabled and Client Sub-Product fields should be disabled</li>
                <li>Test that both GC and Zoho Show are required for the Submit button to be enabled</li>
            </ol>
        </div>
    </div>

    <script>
        // Simplified version of the enhanced form functionality for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Test page loaded');
            
            // Initialize release type dropdown handler
            const releaseTypeSelect = document.getElementById('releaseType');
            if (releaseTypeSelect) {
                releaseTypeSelect.addEventListener('change', function() {
                    const buildFormContainer = document.getElementById('buildFormContainer');
                    if (this.value && buildFormContainer) {
                        buildFormContainer.style.display = 'block';
                        initializeEnhancedBuildForm();
                    } else if (buildFormContainer) {
                        buildFormContainer.style.display = 'none';
                    }
                });
            }
        });

        // Initialize Enhanced Build Form Functionality
        function initializeEnhancedBuildForm() {
            console.log('🚀 Initializing enhanced build form functionality...');

            const gcInput = document.getElementById('gcInput');
            const zohoshowInput = document.getElementById('zohoshowInput');
            const submitBtn = document.getElementById('submitBuildBtn');
            const clientInputs = document.querySelectorAll('.client-input');

            // Function to validate mandatory fields and enable/disable submit button
            function validateMandatoryFields() {
                const gcValue = gcInput ? gcInput.value.trim() : '';
                const zohoshowValue = zohoshowInput ? zohoshowInput.value.trim() : '';
                
                if (submitBtn) {
                    if (gcValue && zohoshowValue) {
                        submitBtn.disabled = false;
                        submitBtn.classList.remove('btn-disabled');
                        console.log('✅ Submit button enabled');
                    } else {
                        submitBtn.disabled = true;
                        submitBtn.classList.add('btn-disabled');
                        console.log('❌ Submit button disabled - missing required fields');
                    }
                }
            }

            // Function to handle Zoho Show input changes
            function handleZohoshowInputChange() {
                if (!zohoshowInput) return;
                
                const value = zohoshowInput.value.trim();
                const isBuildUrl = value.endsWith('.zip');
                
                if (isBuildUrl) {
                    // If it's a build URL (.zip), disable the input and client sub-product inputs
                    zohoshowInput.disabled = true;
                    zohoshowInput.classList.add('form-disabled');
                    
                    // Disable all client sub-product inputs
                    clientInputs.forEach(input => {
                        input.disabled = true;
                        input.classList.add('form-disabled');
                        input.style.backgroundColor = '#f8f9fa';
                    });
                    
                    console.log('🔒 Build URL detected - disabled Zoho Show and Client Sub-Product inputs');
                } else if (value && !isBuildUrl) {
                    // If it's a branch name, enable inputs and auto-copy to client fields
                    zohoshowInput.disabled = false;
                    zohoshowInput.classList.remove('form-disabled');
                    
                    // Enable all client sub-product inputs
                    clientInputs.forEach(input => {
                        input.disabled = false;
                        input.classList.remove('form-disabled');
                        input.style.backgroundColor = '';
                        // Auto-copy branch name to all client sub-product fields
                        input.value = value;
                    });
                    
                    console.log('🔄 Branch name detected - auto-copied to all Client Sub-Product fields:', value);
                } else {
                    // Empty value - enable inputs but don't auto-fill
                    zohoshowInput.disabled = false;
                    zohoshowInput.classList.remove('form-disabled');
                    
                    clientInputs.forEach(input => {
                        input.disabled = false;
                        input.classList.remove('form-disabled');
                        input.style.backgroundColor = '';
                    });
                }
                
                validateMandatoryFields();
            }

            // Add event listeners
            if (gcInput) {
                gcInput.addEventListener('input', validateMandatoryFields);
                gcInput.addEventListener('blur', validateMandatoryFields);
            }

            if (zohoshowInput) {
                // Enable by default
                zohoshowInput.disabled = false;
                
                zohoshowInput.addEventListener('input', handleZohoshowInputChange);
                zohoshowInput.addEventListener('blur', handleZohoshowInputChange);
            }

            // Initial validation
            validateMandatoryFields();
            
            console.log('✅ Enhanced build form functionality initialized');
        }
    </script>
</body>
</html>
